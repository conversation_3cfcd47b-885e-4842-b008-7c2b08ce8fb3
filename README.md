# Sanrado - IT & Enterprise Services Website

A professional, responsive website for Sanrado, a bootstrapped IT and Enterprise Services (ITES) startup based in Tamil Nadu, India, targeting SMEs.

## 🚀 Features

- **Modern Tech Stack**: Built with Eleventy (11ty) v3.1.2, TailwindCSS v4.1.10, and Nunjucks templates
- **Component-Based Architecture**: Reusable components for consistent design and maintainability
- **Responsive Design**: Mobile-first approach with smooth animations and interactions
- **Performance Optimized**: Minified HTML/CSS/JS, preloaded resources, and optimized images
- **SEO Ready**: Comprehensive meta tags, structured data, and semantic HTML
- **Accessibility Compliant**: WCAG guidelines, keyboard navigation, screen reader support
- **Professional Design**: Inspired by leading tech consultancies with Sanrado's unique brand identity

## 🎨 Design System

### Colors
- **Primary**: #008080 (Teal) - Trust, professionalism, innovation
- **Secondary**: #2C3E50 (Dark Blue-Grey) - Stability, expertise
- **Accent**: #E8F8F8 (Light Teal) - Subtle highlights and backgrounds

### Typography
- **Headings**: Inter - Modern, professional, highly legible
- **Body Text**: Atkinson Hyperlegible - Optimized for accessibility and readability

## 📁 Project Structure

```
sanrado-com/
├── src/
│   ├── _includes/
│   │   ├── base.njk              # Base HTML template
│   │   └── components/           # Reusable components
│   │       ├── navigation.njk    # Site navigation
│   │       ├── footer.njk        # Site footer
│   │       ├── hero-section.njk  # Hero sections
│   │       ├── service-card.njk  # Service showcase cards
│   │       ├── project-card.njk  # Project showcase cards
│   │       ├── button.njk        # Button variants
│   │       └── page-header.njk   # Page headers
│   ├── assets/
│   │   ├── css/
│   │   │   └── style.css         # Main stylesheet with TailwindCSS
│   │   └── js/
│   │       └── main.js           # Interactive functionality
│   ├── index.md                  # Home page
│   ├── services/
│   │   └── index.njk            # Services page
│   ├── projects/
│   │   └── index.njk            # Projects page
│   ├── about/
│   │   └── index.njk            # About page
│   └── contact/
│       └── index.njk            # Contact page
├── dist/                        # Built website (generated)
├── eleventy.config.js           # Eleventy configuration
├── tailwind.config.js           # TailwindCSS configuration
├── package.json                 # Dependencies and scripts
└── README.md                    # This file
```

## 🛠️ Development

### Prerequisites
- Node.js (v18 or higher)
- PNPM package manager

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd sanrado-com

# Install dependencies
pnpm install
```

### Development Commands
```bash
# Start development server with hot reload
pnpm run dev

# Build for production
pnpm run build

# Build CSS only
pnpm run build:css

# Build CSS for production (minified)
pnpm run build:css:prod
```

### Development Server
The development server runs at `http://localhost:8080/` with:
- Hot reload for content changes
- CSS watch mode for style updates
- Live browser refresh

## 🌟 Key Features

### Interactive Elements
- Smooth scroll animations with Intersection Observer
- Hover effects and micro-interactions
- Mobile-responsive hamburger menu
- Project filtering functionality
- Form validation with real-time feedback
- Scroll progress indicator

### Performance Optimizations
- Throttled scroll handlers for 60fps performance
- CSS `will-change` properties for smooth animations
- Minified HTML, CSS, and JavaScript
- Preloaded critical resources
- Optimized font loading

### Accessibility Features
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Reduced motion preferences
- Semantic HTML structure
- ARIA labels and attributes

### SEO Optimization
- Comprehensive meta tags
- Open Graph and Twitter Card support
- Canonical URLs
- Structured data markup
- Semantic HTML5 elements
- Fast loading times

## 📱 Pages

1. **Home** - Hero section, service highlights, trust indicators, company credentials
2. **Services** - Software Development, IT Consulting, Digital Transformation
3. **Projects** - Featured projects, client testimonials, filtering functionality
4. **About** - Company story, mission/vision, founder background, timeline
5. **Contact** - Contact form, office details, FAQ section, social media links

## 🚀 Deployment

The website is built as static files in the `dist/` directory and can be deployed to any static hosting service:

- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront
- Traditional web hosting

## 📄 License

This project is proprietary to Sanrado Technologies. All rights reserved.

## 🤝 Contributing

This is a private project for Sanrado Technologies. For any questions or support, please contact the development team.

---

**Built with ❤️ for Sanrado Technologies**
