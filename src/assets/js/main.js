// Main JavaScript for Sanrado Website
document.addEventListener("DOMContentLoaded", function () {
  // Scroll Progress Bar
  function updateScrollProgress() {
    const scrollProgress = document.querySelector(".scroll-progress");
    if (scrollProgress) {
      const scrollTop = window.pageYOffset;
      const docHeight = document.body.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      scrollProgress.style.width = scrollPercent + "%";
    }
  }

  // Create and add scroll progress bar
  const progressBar = document.createElement("div");
  progressBar.className = "scroll-progress";
  document.body.appendChild(progressBar);

  // Scroll Animation Observer
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("visible");

        // Animate counters
        if (entry.target.classList.contains("stat-counter")) {
          animateCounter(entry.target);
        }
      }
    });
  }, observerOptions);

  // Observe elements for scroll animations
  const animateElements = document.querySelectorAll(
    ".animate-on-scroll, .timeline-item, .stat-counter"
  );
  animateElements.forEach((el) => {
    observer.observe(el);
  });

  // Counter Animation
  function animateCounter(element) {
    const target = parseInt(element.getAttribute("data-target"));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      element.textContent = Math.floor(current);
    }, 16);
  }

  // Mobile Navigation Toggle
  const mobileMenuButton = document.querySelector(".mobile-menu-button");
  const mobileMenu = document.querySelector(".mobile-menu");
  const hamburger = document.querySelector(".hamburger");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", function () {
      const isOpen = mobileMenu.classList.contains("open");
      mobileMenu.classList.toggle("open");
      hamburger.classList.toggle("active");

      // Update ARIA attributes
      mobileMenuButton.setAttribute("aria-expanded", !isOpen);
    });

    // Close mobile menu when clicking on links
    const mobileLinks = mobileMenu.querySelectorAll("a");
    mobileLinks.forEach((link) => {
      link.addEventListener("click", function () {
        mobileMenu.classList.remove("open");
        hamburger.classList.remove("active");
      });
    });
  }

  // Smooth Scrolling for Anchor Links
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href").substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        const offsetTop = targetElement.offsetTop - 80; // Account for fixed header
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    });
  });

  // Active Navigation Link
  function updateActiveNavLink() {
    const sections = document.querySelectorAll("section[id]");
    const navLinks = document.querySelectorAll(".nav-link");

    let current = "";
    sections.forEach((section) => {
      const sectionTop = section.offsetTop - 100;
      if (window.pageYOffset >= sectionTop) {
        current = section.getAttribute("id");
      }
    });

    navLinks.forEach((link) => {
      link.classList.remove("active");
      if (link.getAttribute("href") === `#${current}`) {
        link.classList.add("active");
      }
    });
  }

  // Parallax Effect
  function updateParallax() {
    const parallaxElements = document.querySelectorAll(".parallax");
    const scrollTop = window.pageYOffset;

    parallaxElements.forEach((element) => {
      const speed = element.getAttribute("data-speed") || 0.5;
      const yPos = -(scrollTop * speed);
      element.style.transform = `translateY(${yPos}px)`;
    });
  }

  // Form Validation Enhancement
  const forms = document.querySelectorAll("form");
  forms.forEach((form) => {
    const inputs = form.querySelectorAll("input, textarea, select");

    inputs.forEach((input) => {
      // Real-time validation feedback
      input.addEventListener("blur", function () {
        validateField(this);
      });

      input.addEventListener("input", function () {
        if (this.classList.contains("error")) {
          validateField(this);
        }
      });
    });

    // Form submission
    form.addEventListener("submit", function (e) {
      e.preventDefault();

      let isValid = true;
      inputs.forEach((input) => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (isValid) {
        // Show success message
        showNotification(
          "Thank you! We will get back to you within 24 hours.",
          "success"
        );
        form.reset();
      } else {
        showNotification(
          "Please fill in all required fields correctly.",
          "error"
        );
      }
    });
  });

  function validateField(field) {
    const value = field.value.trim();
    let isValid = true;

    // Remove previous error styling
    field.classList.remove("error");

    // Check required fields
    if (field.hasAttribute("required") && !value) {
      isValid = false;
    }

    // Email validation
    if (field.type === "email" && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
      }
    }

    // Phone validation
    if (field.type === "tel" && value) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(value.replace(/\s/g, ""))) {
        isValid = false;
      }
    }

    if (!isValid) {
      field.classList.add("error");
    }

    return isValid;
  }

  // Notification System
  function showNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span>${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${
        type === "success"
          ? "#10b981"
          : type === "error"
          ? "#ef4444"
          : "#3b82f6"
      };
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      z-index: 10000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      max-width: 400px;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 100);

    // Close button
    const closeBtn = notification.querySelector(".notification-close");
    closeBtn.addEventListener("click", () => {
      removeNotification(notification);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
      removeNotification(notification);
    }, 5000);
  }

  function removeNotification(notification) {
    notification.style.transform = "translateX(100%)";
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  // Project Filter Functionality (for projects page)
  const filterButtons = document.querySelectorAll(".filter-btn");
  const projectCards = document.querySelectorAll(".project-card");

  filterButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const filter = this.getAttribute("data-filter");

      // Update active button
      filterButtons.forEach((btn) => btn.classList.remove("active"));
      this.classList.add("active");

      // Filter projects
      projectCards.forEach((card) => {
        if (filter === "all" || card.getAttribute("data-category") === filter) {
          card.style.display = "block";
          card.style.animation = "fadeIn 0.5s ease-in-out";
        } else {
          card.style.display = "none";
        }
      });
    });
  });

  // Throttled scroll handler for better performance
  let scrollTimeout;
  function throttledScrollHandler() {
    if (!scrollTimeout) {
      scrollTimeout = setTimeout(() => {
        updateScrollProgress();
        updateActiveNavLink();
        updateParallax();
        scrollTimeout = null;
      }, 16); // ~60fps
    }
  }

  // Event Listeners
  window.addEventListener("scroll", throttledScrollHandler, { passive: true });

  window.addEventListener("resize", function () {
    // Close mobile menu on resize
    if (window.innerWidth > 768) {
      if (mobileMenu) {
        mobileMenu.classList.remove("open");
      }
      if (hamburger) {
        hamburger.classList.remove("active");
      }
    }
  });

  // Initialize
  updateScrollProgress();
  updateActiveNavLink();

  // Add loading animation to images
  const images = document.querySelectorAll("img");
  images.forEach((img) => {
    if (!img.complete) {
      img.classList.add("loading");
      img.addEventListener("load", function () {
        this.classList.remove("loading");
      });
    }
  });

  console.log("Sanrado website initialized successfully!");
});
