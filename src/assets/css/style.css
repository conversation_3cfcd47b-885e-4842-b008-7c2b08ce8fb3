@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Atkinson+Hyperlegible:wght@400;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #1e293b;
  --color-secondary: #3b82f6;
  --color-accent: #06b6d4;
  --color-light: #f1f5f9;
  --color-background: #ffffff;
  --color-text: #333333;

  --font-family-sans: "Atkinson Hyperlegible", system-ui, sans-serif;
  --font-family-heading: "Inter", system-ui, sans-serif;

  --animate-fade-in: fadeIn 0.6s ease-in-out;
  --animate-slide-up: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  color: var(--color-text);
  background-color: var(--color-background);
}

/* Enhanced Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.04em;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.03em;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.3;
}

p {
  line-height: 1.7;
  font-size: 1rem;
  color: var(--color-text);
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}

.btn-primary:hover {
  background-color: color-mix(in srgb, var(--color-primary) 90%, black);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: white;
}

.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.section-padding {
  padding: 5rem 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 7rem 0;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 8rem 0;
  }
}

.container-custom {
  max-width: 75rem;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 2.5rem;
  }
}

@media (min-width: 1280px) {
  .container-custom {
    padding: 0 3rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: var(--animate-fade-in);
}

.slide-up {
  animation: var(--animate-slide-up);
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animations for grid items */
.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }

/* Professional hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(30, 41, 59, 0.15);
}

/* Enhanced focus states for accessibility */
.focus-ring:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px color-mix(in srgb, var(--color-accent) 20%, transparent);
}

/* Enhanced Button Styles */
.btn-cta {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    color-mix(in srgb, var(--color-primary) 80%, var(--color-secondary))
  );
  color: white;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.125rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-cta:hover::before {
  left: 100%;
}

.btn-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Navigation Enhancements */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Card Hover Effects */
.service-card {
  background-color: white;
  border-radius: 1.25rem;
  padding: 2.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(30, 41, 59, 0.08);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  transform: scaleX(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 50px -12px rgba(30, 41, 59, 0.15),
    0 10px 20px -5px rgba(30, 41, 59, 0.08);
  border-color: rgba(30, 41, 59, 0.12);
}

/* Project Card Animations */
.project-card {
  background-color: white;
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid #f3f4f6;
}

.project-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.project-card img {
  transition: transform 0.4s ease;
}

.project-card:hover img {
  transform: scale(1.1);
}

/* Loading Animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Form Enhancements */
.form-input {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
}

.form-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px
    color-mix(in srgb, var(--color-primary) 10%, transparent);
  outline: none;
}

.form-input:invalid {
  border-color: #ef4444;
}

/* Mobile Menu Animation */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.mobile-menu.open {
  transform: translateX(0);
}

/* Hamburger Animation */
.hamburger {
  cursor: pointer;
  transition: all 0.3s ease;
}

.hamburger.active .line1 {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active .line2 {
  opacity: 0;
}

.hamburger.active .line3 {
  transform: rotate(45deg) translate(-5px, -6px);
}

.hamburger .line1,
.hamburger .line2,
.hamburger .line3 {
  width: 25px;
  height: 3px;
  background-color: var(--color-secondary);
  margin: 5px 0;
  transition: 0.3s;
}

/* Scroll Progress Bar */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--color-primary),
    var(--color-secondary)
  );
  z-index: 9999;
  transition: width 0.1s ease;
}

/* Parallax Effect */
.parallax {
  transform: translateY(0);
  transition: transform 0.1s ease-out;
}

/* Testimonial Card */
.testimonial-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  color: var(--color-primary);
  opacity: 0.3;
  font-family: serif;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Statistics Counter Animation */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-number {
  animation: countUp 0.8s ease-out;
}

/* Timeline Animation */
.timeline-item {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.timeline-item.visible {
  opacity: 1;
  transform: translateX(0);
}

.timeline-item:nth-child(even) {
  transform: translateX(50px);
}

.timeline-item:nth-child(even).visible {
  transform: translateX(0);
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card,
  .service-card,
  .project-card,
  .testimonial-card {
    border: 2px solid var(--color-secondary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-on-scroll {
    opacity: 1;
    transform: none;
  }
}

/* Print styles */
@media print {
  .nav,
  .mobile-menu,
  .scroll-progress,
  button {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.5;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
  }

  .section-padding {
    padding: 1rem 0;
  }
}

/* Performance optimizations */
.card,
.service-card,
.project-card {
  will-change: transform;
}

.card:not(:hover),
.service-card:not(:hover),
.project-card:not(:hover) {
  will-change: auto;
}
