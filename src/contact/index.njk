---
title: "Contact Sanrado - Get Your Free IT Consultation"
description: "Contact Sanrado for expert IT consulting, software development, and digital transformation services. Get your free consultation today."
layout: "base.njk"
---

{% from "components/page-header.njk" import render as pageHeader %}
{% from "components/button.njk" import primary, secondary, cta %}

{{ pageHeader(
  title="Contact Us",
  subtitle="Ready to Transform Your Business? Let's Talk!",
  breadcrumbs=[
    { text: "Home", href: "/" },
    { text: "Contact" }
  ]
) }}

<!-- Contact Form & Info -->
<section class="section-padding">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Contact Form -->
      <div>
        <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Get Your Free Consultation</h2>
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
          Ready to take your business to the next level? Fill out the form below and our team will get back to you 
          within 24 hours with a customized solution proposal.
        </p>
        
        <form id="contact-form" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="firstName" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">First Name *</label>
              <input type="text" id="firstName" name="firstName" required 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                     style="focus:ring-color: var(--color-primary);">
            </div>
            <div>
              <label for="lastName" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Last Name *</label>
              <input type="text" id="lastName" name="lastName" required 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                     style="focus:ring-color: var(--color-primary);">
            </div>
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Email Address *</label>
            <input type="email" id="email" name="email" required 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                   style="focus:ring-color: var(--color-primary);">
          </div>
          
          <div>
            <label for="phone" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Phone Number</label>
            <input type="tel" id="phone" name="phone" 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                   style="focus:ring-color: var(--color-primary);">
          </div>
          
          <div>
            <label for="company" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Company Name</label>
            <input type="text" id="company" name="company" 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                   style="focus:ring-color: var(--color-primary);">
          </div>
          
          <div>
            <label for="service" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Service Interest *</label>
            <select id="service" name="service" required 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                    style="focus:ring-color: var(--color-primary);">
              <option value="">Select a service...</option>
              <option value="software-development">Software Development</option>
              <option value="it-consulting">IT Consulting</option>
              <option value="digital-transformation">Digital Transformation</option>
              <option value="multiple-services">Multiple Services</option>
              <option value="not-sure">Not Sure - Need Consultation</option>
            </select>
          </div>
          
          <div>
            <label for="budget" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Project Budget Range</label>
            <select id="budget" name="budget" 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300"
                    style="focus:ring-color: var(--color-primary);">
              <option value="">Select budget range...</option>
              <option value="under-5l">Under ₹5 Lakhs</option>
              <option value="5l-10l">₹5 - 10 Lakhs</option>
              <option value="10l-25l">₹10 - 25 Lakhs</option>
              <option value="25l-50l">₹25 - 50 Lakhs</option>
              <option value="above-50l">Above ₹50 Lakhs</option>
              <option value="discuss">Prefer to Discuss</option>
            </select>
          </div>
          
          <div>
            <label for="message" class="block text-sm font-medium mb-2" style="color: var(--color-secondary);">Project Details *</label>
            <textarea id="message" name="message" rows="5" required 
                      placeholder="Tell us about your project requirements, challenges, and goals..."
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:border-transparent transition-all duration-300 resize-vertical"
                      style="focus:ring-color: var(--color-primary);"></textarea>
          </div>
          
          <div class="flex items-start">
            <input type="checkbox" id="privacy" name="privacy" required class="mt-1 mr-3">
            <label for="privacy" class="text-sm text-gray-600">
              I agree to the <a href="/privacy/" class="underline" style="color: var(--color-primary);">Privacy Policy</a> 
              and consent to being contacted about my inquiry. *
            </label>
          </div>
          
          <button type="submit" 
                  class="w-full px-8 py-4 rounded-lg font-medium text-lg text-white transition-all duration-300 hover:shadow-lg"
                  style="background-color: var(--color-primary);">
            Send Message & Get Free Consultation
          </button>
        </form>
      </div>
      
      <!-- Contact Information -->
      <div>
        <div class="bg-white rounded-2xl p-8 shadow-sm mb-8">
          <h3 class="text-2xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Get in Touch</h3>
          
          <div class="space-y-6">
            <div class="flex items-start">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4" style="background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);">
                <svg class="w-6 h-6" style="color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-lg mb-1" style="color: var(--color-secondary);">Email Us</h4>
                <p class="text-gray-600"><EMAIL></p>
                <p class="text-gray-600"><EMAIL></p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4" style="background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);">
                <svg class="w-6 h-6" style="color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-lg mb-1" style="color: var(--color-secondary);">Call Us</h4>
                <p class="text-gray-600">+91 98765 43210</p>
                <p class="text-gray-600 text-sm">Mon - Fri: 9:00 AM - 6:00 PM IST</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4" style="background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);">
                <svg class="w-6 h-6" style="color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-lg mb-1" style="color: var(--color-secondary);">Visit Us</h4>
                <p class="text-gray-600">
                  Sanrado Technologies Pvt. Ltd.<br>
                  123 IT Park, Anna Salai<br>
                  Chennai, Tamil Nadu 600002<br>
                  India
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Response Time -->
        <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 text-white text-center">
          <h3 class="text-2xl font-bold mb-4" style="font-family: var(--font-family-heading);">Quick Response Guarantee</h3>
          <p class="text-lg mb-4 opacity-90">
            We respond to all inquiries within 24 hours, and most within 4 hours during business days.
          </p>
          <div class="flex justify-center items-center">
            <svg class="w-8 h-8 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-lg font-semibold">24-Hour Response Time</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Office Location & Map -->
<section class="section-padding" style="background-color: var(--color-accent);">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Our Office Location</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Located in the heart of Chennai's IT corridor, we're easily accessible and ready to meet with you.
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <div class="bg-white rounded-2xl p-8 shadow-sm">
          <h3 class="text-2xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Office Details</h3>

          <div class="space-y-6">
            <div>
              <h4 class="font-semibold text-lg mb-2" style="color: var(--color-secondary);">Address</h4>
              <p class="text-gray-600 leading-relaxed">
                Sanrado Technologies Pvt. Ltd.<br>
                123 IT Park, 4th Floor<br>
                Anna Salai, Teynampet<br>
                Chennai, Tamil Nadu 600002<br>
                India
              </p>
            </div>

            <div>
              <h4 class="font-semibold text-lg mb-2" style="color: var(--color-secondary);">Business Hours</h4>
              <div class="text-gray-600">
                <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                <p>Saturday: 10:00 AM - 2:00 PM</p>
                <p>Sunday: Closed</p>
              </div>
            </div>

            <div>
              <h4 class="font-semibold text-lg mb-2" style="color: var(--color-secondary);">Getting Here</h4>
              <div class="text-gray-600 space-y-2">
                <p><strong>By Metro:</strong> Teynampet Metro Station (5 min walk)</p>
                <p><strong>By Bus:</strong> Anna Salai bus stops nearby</p>
                <p><strong>Parking:</strong> Visitor parking available</p>
              </div>
            </div>
          </div>

          <div class="mt-8 pt-6 border-t border-gray-200">
            <p class="text-sm text-gray-600 mb-4">
              <strong>Note:</strong> We recommend scheduling an appointment to ensure our team is available to meet with you.
            </p>
            {{ primary("Schedule a Meeting", "/contact/#contact-form") }}
          </div>
        </div>
      </div>

      <div>
        <!-- Map Placeholder -->
        <div class="bg-white rounded-2xl p-8 shadow-sm">
          <div class="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-6">
            <div class="text-center">
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <p class="text-gray-500">Interactive Map</p>
              <p class="text-sm text-gray-400">123 IT Park, Anna Salai, Chennai</p>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-4">
            <a href="https://maps.google.com/?q=Anna+Salai+Chennai" target="_blank" rel="noopener noreferrer"
               class="flex-1 px-4 py-3 text-center rounded-lg border-2 transition-all duration-300 hover:shadow-md"
               style="border-color: var(--color-primary); color: var(--color-primary);">
              <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
              Open in Google Maps
            </a>
            <a href="tel:+919876543210"
               class="flex-1 px-4 py-3 text-center rounded-lg text-white transition-all duration-300 hover:shadow-md"
               style="background-color: var(--color-primary);">
              <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
              </svg>
              Call Now
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="section-padding">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Frequently Asked Questions</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Quick answers to common questions about our services and process.
      </p>
    </div>

    <div class="max-w-4xl mx-auto">
      <div class="space-y-6">
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-secondary);">How quickly can you start my project?</h3>
          <p class="text-gray-600">
            Most projects can begin within 1-2 weeks after contract signing. For urgent requirements,
            we can often accommodate faster start times depending on our current capacity.
          </p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-secondary);">Do you work with businesses outside Tamil Nadu?</h3>
          <p class="text-gray-600">
            While we're based in Tamil Nadu, we serve clients across India and internationally.
            We're experienced in remote collaboration and can work effectively with distributed teams.
          </p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-secondary);">What's included in your free consultation?</h3>
          <p class="text-gray-600">
            Our free consultation includes project scope analysis, technology recommendations,
            timeline estimation, and a detailed proposal with transparent pricing. No obligations attached.
          </p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-secondary);">Do you provide ongoing support after project completion?</h3>
          <p class="text-gray-600">
            Yes, we offer comprehensive maintenance and support packages. This includes bug fixes,
            updates, security patches, and feature enhancements as your business grows.
          </p>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-secondary);">What payment terms do you offer?</h3>
          <p class="text-gray-600">
            We offer flexible payment terms including milestone-based payments, monthly retainers,
            and project-based pricing. We'll work with you to find a payment structure that fits your cash flow.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Social Media & Final CTA -->
<section class="section-padding" style="background-color: var(--color-accent);">
  <div class="container-custom">
    <div class="text-center">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Connect With Us</h2>
      <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
        Follow us on social media for the latest updates, tech insights, and success stories from our clients.
      </p>

      <div class="flex justify-center space-x-6 mb-12">
        <a href="https://linkedin.com/company/sanrado" target="_blank" rel="noopener noreferrer"
           class="w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110"
           style="background-color: #0077B5;">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>

        <a href="https://twitter.com/sanradotech" target="_blank" rel="noopener noreferrer"
           class="w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110"
           style="background-color: #1DA1F2;">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </a>

        <a href="https://github.com/sanrado" target="_blank" rel="noopener noreferrer"
           class="w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110"
           style="background-color: #333;">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
      </div>

      <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-bold mb-4" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Ready to Get Started?</h3>
        <p class="text-lg text-gray-600 mb-8">
          Don't let technology challenges hold your business back. Contact us today for your free consultation
          and discover how we can help you achieve your digital transformation goals.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          {{ cta("Start Your Project Today", "#contact-form") }}
          {{ secondary("Call Us Now", "tel:+919876543210") }}
        </div>
      </div>
    </div>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('contact-form');

  form.addEventListener('submit', function(e) {
    e.preventDefault();

    // Basic form validation
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        isValid = false;
        field.style.borderColor = '#ef4444';
      } else {
        field.style.borderColor = '#d1d5db';
      }
    });

    if (isValid) {
      // In a real implementation, you would send the form data to your server
      alert('Thank you for your message! We will get back to you within 24 hours.');
      form.reset();
    } else {
      alert('Please fill in all required fields.');
    }
  });

  // Reset border color on input
  const inputs = form.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('input', function() {
      this.style.borderColor = '#d1d5db';
    });
  });
});
</script>
