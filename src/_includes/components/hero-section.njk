{% macro render(title, subtitle, description, primaryCta="", primaryHref="#", secondaryCta="", secondaryHref="#", backgroundImage="") %}
<section class="relative section-padding overflow-hidden">
  <!-- Background -->
  <div class="absolute inset-0" style="background: linear-gradient(to bottom right, white, var(--color-light), white);"></div>
  {% if backgroundImage %}
  <div class="absolute inset-0 opacity-5">
    <img src="{{ backgroundImage }}" alt="" class="w-full h-full object-cover">
  </div>
  {% endif %}
  
  <!-- Content -->
  <div class="container-custom relative">
    <div class="max-w-4xl mx-auto text-center">
      <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight" style="color: var(--color-secondary); font-family: var(--font-family-heading); animation: var(--animate-fade-in);">
        {{ title }}
      </h1>
      
      {% if subtitle %}
      <p class="text-xl md:text-2xl mb-6 font-medium" style="color: var(--color-primary); animation: var(--animate-slide-up); animation-delay: 0.2s; animation-fill-mode: both;">
        {{ subtitle }}
      </p>
      {% endif %}
      
      <p class="text-lg md:text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed" style="animation: var(--animate-slide-up); animation-delay: 0.4s; animation-fill-mode: both;">
        {{ description }}
      </p>
      
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center" style="animation: var(--animate-slide-up); animation-delay: 0.6s; animation-fill-mode: both;">
        {% if primaryCta %}
        <a href="{{ primaryHref }}" class="btn-primary text-lg px-8 py-4">
          {{ primaryCta }}
          <svg class="inline-block w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
        {% endif %}
        
        {% if secondaryCta %}
        <a href="{{ secondaryHref }}" class="btn-secondary text-lg px-8 py-4">
          {{ secondaryCta }}
        </a>
        {% endif %}
      </div>
    </div>
  </div>
  
  <!-- Decorative Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 rounded-full opacity-10" style="background-color: var(--color-primary); animation: float 6s ease-in-out infinite;"></div>
  <div class="absolute bottom-20 right-10 w-16 h-16 rounded-full opacity-10" style="background-color: var(--color-secondary); animation: float 8s ease-in-out infinite reverse;"></div>
</section>

<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
</style>
{% endmacro %}
