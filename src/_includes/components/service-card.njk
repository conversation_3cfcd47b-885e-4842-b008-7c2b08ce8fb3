{% macro render(title, description, icon, features=[], href="#") %}
<div class="service-card animate-on-scroll group">
  <div class="flex items-start space-x-5">
    <div class="flex-shrink-0">
      <div class="w-14 h-14 rounded-xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 icon-container" style="background: linear-gradient(135deg, color-mix(in srgb, var(--color-primary) 12%, transparent), color-mix(in srgb, var(--color-accent) 8%, transparent)); border: 1px solid color-mix(in srgb, var(--color-primary) 15%, transparent);">
        {{ icon | safe }}
      </div>
    </div>
    <div class="flex-1">
      <h3 class="text-xl font-bold mb-4 leading-tight" style="color: var(--color-primary); font-family: var(--font-family-heading);">{{ title }}</h3>
      <p class="mb-5 leading-relaxed text-base" style="color: var(--color-text); line-height: 1.7;">{{ description }}</p>
      
      {% if features.length > 0 %}
      <ul class="space-y-3 mb-7">
        {% for feature in features %}
        <li class="flex items-center text-sm" style="color: var(--color-text);">
          <svg class="w-4 h-4 mr-3 flex-shrink-0" style="color: var(--color-accent);" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          {{ feature }}
        </li>
        {% endfor %}
      </ul>
      {% endif %}

      <a href="{{ href }}" class="inline-flex items-center font-semibold text-sm transition-all duration-300 group-hover:gap-2" style="color: var(--color-secondary);">
        Learn More
        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
        </svg>
      </a>
    </div>
  </div>
</div>
{% endmacro %}
