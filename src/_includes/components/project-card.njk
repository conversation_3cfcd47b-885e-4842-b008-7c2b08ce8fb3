{% macro render(title, description, image, tags=[], category="", href="#") %}
<div class="card group project-card" data-category="{{ category }}">
  <div class="aspect-video bg-gray-100 rounded-lg mb-4 overflow-hidden">
    {% if image %}
    <img src="{{ image }}" alt="{{ title }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
    {% else %}
    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
      <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
    </div>
    {% endif %}
  </div>
  
  <div class="space-y-3">
    <h3 class="text-xl font-bold group-hover:text-primary transition-colors duration-200" style="color: var(--color-secondary); font-family: var(--font-family-heading);">{{ title }}</h3>
    
    <p class="text-gray-600 leading-relaxed">{{ description }}</p>
    
    {% if tags.length > 0 %}
    <div class="flex flex-wrap gap-2">
      {% for tag in tags %}
      <span class="px-3 py-1 text-xs font-medium rounded-full border" style="color: var(--color-primary); border-color: var(--color-primary); background-color: color-mix(in srgb, var(--color-primary) 5%, transparent);">
        {{ tag }}
      </span>
      {% endfor %}
    </div>
    {% endif %}
    
    <div class="pt-2">
      <a href="{{ href }}" class="inline-flex items-center font-medium transition-colors duration-200" style="color: var(--color-primary);">
        View Details
        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
        </svg>
      </a>
    </div>
  </div>
</div>
{% endmacro %}
