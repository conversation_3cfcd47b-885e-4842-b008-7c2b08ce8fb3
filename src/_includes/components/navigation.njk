<nav class="bg-white shadow-sm sticky top-0 z-50">
  <div class="container-custom">
    <div class="flex justify-between items-center py-4">
      <!-- Lo<PERSON> and Brand -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-3">
          <div class="w-10 h-10 rounded-lg flex items-center justify-center" style="background-color: var(--color-primary);">
            <span class="text-white font-bold text-xl">S</span>
          </div>
          <span class="text-2xl font-bold" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Sanrado</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <a href="/" class="nav-link {{ 'active' if page.url == '/' }}">Home</a>
        <a href="/services/" class="nav-link {{ 'active' if '/services/' in page.url }}">Services</a>
        <a href="/projects/" class="nav-link {{ 'active' if '/projects/' in page.url }}">Projects</a>
        <a href="/about/" class="nav-link {{ 'active' if '/about/' in page.url }}">About</a>
        <a href="/contact/" class="nav-link {{ 'active' if '/contact/' in page.url }}">Contact</a>
      </div>

      <!-- Mobile Menu Button -->
      <button
        class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors mobile-menu-button"
        id="mobile-menu-button"
        aria-label="Toggle mobile menu"
        aria-expanded="false"
        aria-controls="mobile-menu">
        <div class="hamburger">
          <div class="line1"></div>
          <div class="line2"></div>
          <div class="line3"></div>
        </div>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div class="md:hidden mobile-menu" id="mobile-menu" role="navigation" aria-label="Mobile navigation">
      <div class="py-4 border-t border-gray-100">
        <a href="/" class="mobile-nav-link {{ 'active' if page.url == '/' }}" {{ 'aria-current="page"' if page.url == '/' else '' | safe }}>Home</a>
        <a href="/services/" class="mobile-nav-link {{ 'active' if '/services/' in page.url }}" {{ 'aria-current="page"' if '/services/' in page.url else '' | safe }}>Services</a>
        <a href="/projects/" class="mobile-nav-link {{ 'active' if '/projects/' in page.url }}" {{ 'aria-current="page"' if '/projects/' in page.url else '' | safe }}>Projects</a>
        <a href="/about/" class="mobile-nav-link {{ 'active' if '/about/' in page.url }}" {{ 'aria-current="page"' if '/about/' in page.url else '' | safe }}>About</a>
        <a href="/contact/" class="mobile-nav-link {{ 'active' if '/contact/' in page.url }}" {{ 'aria-current="page"' if '/contact/' in page.url else '' | safe }}>Contact</a>
      </div>
    </div>
  </div>
</nav>

<style>
  .nav-link {
    color: #6b7280;
    font-weight: 500;
    transition: color 0.2s ease;
    position: relative;
    text-decoration: none;
  }

  .nav-link:hover {
    color: var(--color-primary);
  }

  .nav-link.active {
    color: var(--color-primary);
  }

  .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-primary);
  }

  .mobile-nav-link {
    display: block;
    padding: 0.75rem 0;
    color: #6b7280;
    font-weight: 500;
    transition: color 0.2s ease;
    text-decoration: none;
  }

  .mobile-nav-link:hover {
    color: var(--color-primary);
  }

  .mobile-nav-link.active {
    color: var(--color-primary);
    background-color: color-mix(in srgb, var(--color-accent) 50%, transparent);
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: -1rem;
    margin-right: -1rem;
    border-radius: 0.5rem;
  }
</style>


