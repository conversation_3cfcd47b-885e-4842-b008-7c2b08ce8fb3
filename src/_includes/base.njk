<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ title or "Sanrado - IT & Enterprise Services" }}</title>
    <meta name="description" content="{{ description or 'Sanrado provides innovative IT solutions and enterprise services for SMEs in Tamil Nadu, India. Specializing in software development, IT consulting, and digital transformation.' }}">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="canonical" href="{{ page.url }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ page.url }}">
    <meta property="og:title" content="{{ title or 'Sanrado - IT & Enterprise Services' }}">
    <meta property="og:description" content="{{ description or 'Innovative IT solutions for SMEs' }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ page.url }}">
    <meta property="twitter:title" content="{{ title or 'Sanrado - IT & Enterprise Services' }}">
    <meta property="twitter:description" content="{{ description or 'Innovative IT solutions for SMEs' }}">

    <!-- Additional SEO and Accessibility -->
    <meta name="robots" content="index, follow">
    <meta name="author" content="Sanrado Technologies">
    <meta name="theme-color" content="#1e293b">
    <link rel="canonical" href="https://sanrado.com{{ page.url }}">

    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/css/style.css" as="style">
    <link rel="preload" href="/assets/js/main.js" as="script">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' rx='20' fill='%231e293b'/><text x='50' y='65' font-family='Arial,sans-serif' font-size='60' font-weight='bold' text-anchor='middle' fill='white'>S</text></svg>">
  </head>
  <body style="background-color: var(--color-background);">
    {% include "components/navigation.njk" %}

    <main>
      {{ content | safe }}
    </main>

    {% include "components/footer.njk" %}

    <script src="/assets/js/main.js"></script>
  </body>
</html>