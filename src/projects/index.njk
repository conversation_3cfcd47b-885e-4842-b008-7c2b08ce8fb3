---
title: "Our Projects - Sanrado Portfolio"
description: "Explore our successful IT projects and case studies showcasing software development, digital transformation, and consulting solutions for SMEs."
layout: "base.njk"
---

{% from "components/page-header.njk" import render as pageHeader %}
{% from "components/project-card.njk" import render as projectCard %}
{% from "components/button.njk" import primary, secondary, cta %}

{{ pageHeader(
  title="Our Projects",
  subtitle="Successful IT Solutions Delivered for SMEs",
  breadcrumbs=[
    { text: "Home", href: "/" },
    { text: "Projects" }
  ]
) }}

<!-- Projects Overview -->
<section class="section-padding">
  <div class="container-custom">
    <div class="text-center mb-16">
      <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        Discover how we've helped small and medium enterprises across Tamil Nadu transform their operations 
        through innovative technology solutions. Each project represents our commitment to delivering 
        exceptional results that drive business growth.
      </p>
    </div>
    
    <!-- Filter <PERSON> -->
    <div class="flex flex-wrap justify-center gap-4 mb-12">
      <button class="filter-btn active px-6 py-3 rounded-lg font-medium transition-all duration-300" data-filter="all" style="background-color: var(--color-primary); color: white;">
        All Projects
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg font-medium transition-all duration-300 border-2" data-filter="software-development" style="border-color: var(--color-primary); color: var(--color-primary);">
        Software Development
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg font-medium transition-all duration-300 border-2" data-filter="it-consulting" style="border-color: var(--color-primary); color: var(--color-primary);">
        IT Consulting
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg font-medium transition-all duration-300 border-2" data-filter="digital-transformation" style="border-color: var(--color-primary); color: var(--color-primary);">
        Digital Transformation
      </button>
    </div>
  </div>
</section>

<!-- Featured Projects -->
<section class="section-padding" style="background-color: var(--color-accent);">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Featured Projects</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Highlighting some of our most impactful solutions that have transformed businesses across various industries.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 projects-grid">
      {{ projectCard(
        title="E-Commerce Platform for Textile Manufacturer",
        description="Complete digital transformation with custom e-commerce platform, inventory management, and customer portal for a leading textile manufacturer in Coimbatore.",
        image="/assets/images/projects/textile-ecommerce.jpg",
        category="software-development",
        tags=["React", "Node.js", "MongoDB", "Payment Gateway"],
        results=["300% increase in online sales", "50% reduction in order processing time", "Expanded to 5 new markets"],
        href="/projects/textile-ecommerce/"
      ) }}
      
      {{ projectCard(
        title="Hospital Management System",
        description="Comprehensive healthcare management solution with patient records, appointment scheduling, billing, and telemedicine capabilities for a multi-specialty hospital.",
        image="/assets/images/projects/hospital-management.jpg",
        category="software-development",
        tags=["Vue.js", "PHP", "MySQL", "HIPAA Compliant"],
        results=["40% improvement in patient flow", "Reduced waiting times by 60%", "Enhanced patient satisfaction"],
        href="/projects/hospital-management/"
      ) }}
      
      {{ projectCard(
        title="Manufacturing ERP Implementation",
        description="End-to-end ERP solution for a mid-sized manufacturing company, integrating production planning, inventory, finance, and quality management modules.",
        image="/assets/images/projects/manufacturing-erp.jpg",
        category="digital-transformation",
        tags=["ERP", "Cloud Migration", "Process Automation", "Analytics"],
        results=["25% increase in productivity", "Real-time visibility across operations", "Reduced manual errors by 80%"],
        href="/projects/manufacturing-erp/"
      ) }}
      
      {{ projectCard(
        title="IT Infrastructure Modernization",
        description="Complete IT infrastructure overhaul for a financial services firm, including cloud migration, security enhancement, and disaster recovery planning.",
        image="/assets/images/projects/it-infrastructure.jpg",
        category="it-consulting",
        tags=["Cloud Migration", "Security", "Disaster Recovery", "AWS"],
        results=["99.9% uptime achieved", "50% reduction in IT costs", "Enhanced security posture"],
        href="/projects/it-infrastructure/"
      ) }}
      
      {{ projectCard(
        title="Educational Platform for Coaching Institute",
        description="Online learning management system with video streaming, assignment management, progress tracking, and virtual classroom capabilities.",
        image="/assets/images/projects/education-platform.jpg",
        category="software-development",
        tags=["React", "Video Streaming", "LMS", "Mobile App"],
        results=["500+ students enrolled", "Expanded to 3 new cities", "95% student satisfaction"],
        href="/projects/education-platform/"
      ) }}
      
      {{ projectCard(
        title="Supply Chain Optimization",
        description="Digital transformation of supply chain operations for a logistics company, implementing IoT sensors, real-time tracking, and predictive analytics.",
        image="/assets/images/projects/supply-chain.jpg",
        category="digital-transformation",
        tags=["IoT", "Analytics", "Real-time Tracking", "Mobile"],
        results=["30% reduction in delivery time", "Improved customer satisfaction", "Cost savings of ₹2M annually"],
        href="/projects/supply-chain/"
      ) }}
    </div>
  </div>
</section>

<!-- Project Statistics -->
<section class="section-padding">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">Our Impact</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Numbers that speak to our commitment to delivering exceptional results for our clients.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: var(--color-primary);">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-4xl font-bold mb-2" style="color: var(--color-secondary); font-family: var(--font-family-heading);">50+</h3>
        <p class="text-gray-600 font-medium">Projects Completed</p>
      </div>
      
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: var(--color-primary);">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <h3 class="text-4xl font-bold mb-2" style="color: var(--color-secondary); font-family: var(--font-family-heading);">35+</h3>
        <p class="text-gray-600 font-medium">Happy Clients</p>
      </div>
      
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: var(--color-primary);">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        <h3 class="text-4xl font-bold mb-2" style="color: var(--color-secondary); font-family: var(--font-family-heading);">₹5M+</h3>
        <p class="text-gray-600 font-medium">Cost Savings Generated</p>
      </div>
      
      <div class="text-center">
        <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: var(--color-primary);">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-4xl font-bold mb-2" style="color: var(--color-secondary); font-family: var(--font-family-heading);">98%</h3>
        <p class="text-gray-600 font-medium">On-Time Delivery</p>
      </div>
    </div>
  </div>
</section>

<!-- Client Testimonials -->
<section class="section-padding" style="background-color: var(--color-accent);">
  <div class="container-custom">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="color: var(--color-secondary); font-family: var(--font-family-heading);">What Our Clients Say</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Don't just take our word for it. Here's what our clients have to say about working with Sanrado.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div class="bg-white rounded-2xl p-8 shadow-sm">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 rounded-full mr-4" style="background-color: var(--color-primary);"></div>
          <div>
            <h4 class="font-bold text-lg" style="color: var(--color-secondary);">Rajesh Kumar</h4>
            <p class="text-gray-600">CEO, Textile Manufacturing Co.</p>
          </div>
        </div>
        <p class="text-gray-600 mb-4 italic">
          "Sanrado transformed our entire business with their e-commerce platform. Our online sales increased by 300%
          within the first year, and we've been able to expand to new markets we never thought possible."
        </p>
        <div class="flex text-yellow-400">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
        </div>
      </div>

      <div class="bg-white rounded-2xl p-8 shadow-sm">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 rounded-full mr-4" style="background-color: var(--color-primary);"></div>
          <div>
            <h4 class="font-bold text-lg" style="color: var(--color-secondary);">Dr. Priya Sharma</h4>
            <p class="text-gray-600">Director, Multi-Specialty Hospital</p>
          </div>
        </div>
        <p class="text-gray-600 mb-4 italic">
          "The hospital management system developed by Sanrado has revolutionized our operations. Patient flow
          improved by 40%, and our staff can now focus more on patient care rather than administrative tasks."
        </p>
        <div class="flex text-yellow-400">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
        </div>
      </div>

      <div class="bg-white rounded-2xl p-8 shadow-sm">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 rounded-full mr-4" style="background-color: var(--color-primary);"></div>
          <div>
            <h4 class="font-bold text-lg" style="color: var(--color-secondary);">Arun Krishnan</h4>
            <p class="text-gray-600">CTO, Financial Services</p>
          </div>
        </div>
        <p class="text-gray-600 mb-4 italic">
          "Sanrado's IT consulting expertise helped us modernize our entire infrastructure. We achieved 99.9% uptime
          and reduced our IT costs by 50% while significantly improving our security posture."
        </p>
        <div class="flex text-yellow-400">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="section-padding">
  <div class="container-custom">
    <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 md:p-16 text-center text-white">
      <h2 class="text-3xl md:text-4xl font-bold mb-6" style="font-family: var(--font-family-heading);">Ready to Start Your Project?</h2>
      <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
        Let's discuss your requirements and create a solution that drives your business forward.
        Our team is ready to help you achieve your digital transformation goals.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/contact/" class="bg-white text-primary px-8 py-4 rounded-lg font-medium text-lg hover:bg-gray-100 transition-colors duration-300">
          Start Your Project
        </a>
        <a href="/services/" class="border-2 border-white text-white px-8 py-4 rounded-lg font-medium text-lg hover:bg-white hover:text-primary transition-all duration-300">
          Explore Services
        </a>
      </div>
    </div>
  </div>
</section>

<script>
// Project filtering functionality
document.addEventListener('DOMContentLoaded', function() {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const projectCards = document.querySelectorAll('.projects-grid > div');

  filterButtons.forEach(button => {
    button.addEventListener('click', function() {
      const filter = this.getAttribute('data-filter');

      // Update active button
      filterButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.style.backgroundColor = 'transparent';
        btn.style.color = 'var(--color-primary)';
      });

      this.classList.add('active');
      this.style.backgroundColor = 'var(--color-primary)';
      this.style.color = 'white';

      // Filter projects
      projectCards.forEach(card => {
        if (filter === 'all') {
          card.style.display = 'block';
        } else {
          const cardCategory = card.querySelector('[data-category]')?.getAttribute('data-category');
          if (cardCategory === filter) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        }
      });
    });
  });
});
</script>
