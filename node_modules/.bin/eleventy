#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules/@11ty/eleventy/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules/@11ty/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules/@11ty/eleventy/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules/@11ty/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@11ty+eleventy@3.1.2/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@11ty/eleventy/cmd.cjs" "$@"
else
  exec node  "$basedir/../@11ty/eleventy/cmd.cjs" "$@"
fi
