#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/cli/dist/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/cli/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/cli/dist/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/cli/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules/@tailwindcss/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/@tailwindcss+cli@4.1.10/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/index.mjs" "$@"
else
  exec node  "$basedir/../../dist/index.mjs" "$@"
fi
