#!/usr/bin/env node
var se=(e,t)=>(t=Symbol[e])?t:Symbol.for("Symbol."+e),le=e=>{throw TypeError(e)};var q=(e,t,n)=>{if(t!=null){typeof t!="object"&&typeof t!="function"&&le("Object expected");var i,o;n&&(i=t[se("asyncDispose")]),i===void 0&&(i=t[se("dispose")],n&&(o=i)),typeof i!="function"&&le("Object not disposable"),o&&(i=function(){try{o.call(this)}catch(r){return Promise.reject(r)}}),e.push([n,i,t])}else n&&e.push([n]);return t},K=(e,t,n)=>{var i=typeof SuppressedError=="function"?SuppressedError:function(u,s,a,p){return p=Error(a),p.name="SuppressedError",p.error=u,p.suppressed=s,p},o=u=>t=n?new i(u,t,"An error was suppressed during disposal"):(n=!0,u),r=u=>{for(;u=e.pop();)try{var s=u[1]&&u[1].call(u[2]);if(u[0])return Promise.resolve(s).then(r,a=>(o(a),r()))}catch(a){o(a)}if(n)throw t};return r()};import Fe from"mri";function ue(e,t=process.argv.slice(2)){for(let[o,r]of t.entries())r==="-"&&(t[o]="__IO_DEFAULT_VALUE__");let n=Fe(t);for(let o in n)n[o]==="__IO_DEFAULT_VALUE__"&&(n[o]="-");let i={_:n._};for(let[o,{type:r,alias:u,default:s=r==="boolean"?!1:null}]of Object.entries(e)){if(i[o]=s,u){let a=u.slice(1);n[a]!==void 0&&(i[o]=ae(n[a],r))}{let a=o.slice(2);n[a]!==void 0&&(i[o]=ae(n[a],r))}}return i}function ae(e,t){switch(t){case"string":return W(e);case"boolean":return O(e);case"number":return R(e);case"boolean | string":return O(e)??W(e);case"number | string":return R(e)??W(e);case"boolean | number":return O(e)??R(e);case"boolean | number | string":return O(e)??R(e)??W(e);default:throw new Error(`Unhandled type: ${t}`)}}function O(e){if(e===!0||e===!1)return e;if(e==="true")return!0;if(e==="false")return!1}function R(e){if(typeof e=="number")return e;{let t=Number(e);if(!Number.isNaN(t))return t}}function W(e){return`${e}`}import De from"@parcel/watcher";import{compile as Ne,env as Ee,Instrumentation as me,optimize as Ue,toSourceMap as he}from"@tailwindcss/node";import{clearRequireCache as Le}from"@tailwindcss/node/require-cache";import{Scanner as Ie}from"@tailwindcss/oxide";import{existsSync as je}from"fs";import X from"fs/promises";import v from"path";var F=class{#e=new Set([]);queueMacrotask(t){let n=setTimeout(t,0);return this.add(()=>{clearTimeout(n)})}add(t){return this.#e.add(t),()=>{this.#e.delete(t),t()}}async dispose(){for(let t of this.#e)await t();this.#e.clear()}};import Oe from"fs";import de from"path";import{stripVTControlCharacters as Re}from"util";import b from"picocolors";import pe from"enhanced-resolve";import Me from"fs";import{createRequire as ke}from"module";var Be=ke(import.meta.url).resolve;function ce(e){if(typeof globalThis.__tw_resolve=="function"){let t=globalThis.__tw_resolve(e);if(t)return t}return Be(e)}var He=pe.ResolverFactory.createResolver({fileSystem:new pe.CachedInputFileSystem(Me,4e3),useSyncFileSystemCalls:!0,extensions:[".css"],mainFields:["style"],conditionNames:["style"]});function fe(e){let t=typeof e=="number"?BigInt(e):e;return t<1000n?`${t}ns`:(t/=1000n,t<1000n?`${t}\xB5s`:(t/=1000n,t<1000n?`${t}ms`:(t/=1000n,t<60n?`${t}s`:(t/=60n,t<60n?`${t}m`:(t/=60n,t<24n?`${t}h`:(t/=24n,`${t}d`))))))}var z={indent:2};function D(){return`${b.italic(b.bold(b.blue("\u2248")))} tailwindcss ${b.blue(`v${We()}`)}`}function A(e){return`${b.dim(b.blue("`"))}${b.blue(e)}${b.dim(b.blue("`"))}`}function N(e,t=process.cwd(),{preferAbsoluteIfShorter:n=!0}={}){let i=de.relative(t,e);return i.startsWith("..")||(i=`.${de.sep}${i}`),n&&i.length>e.length?e:i}function G(e,t){let n=e.split(" "),i=[],o="",r=0;for(let u of n){let s=Re(u).length;r+s+1>t&&(i.push(o),o="",r=0),o+=(r?" ":"")+u,r+=s+(r?1:0)}return r&&i.push(o),i}function E(e){let t=fe(e);return e<=50*1e6?b.green(t):e<=300*1e6?b.blue(t):e<=1e3*1e6?b.yellow(t):b.red(t)}function M(e,t=0){return`${" ".repeat(t+z.indent)}${e}`}function g(e=""){process.stderr.write(`${e}
`)}function h(e=""){process.stdout.write(`${e}
`)}function We(){if(typeof globalThis.__tw_version=="string")return globalThis.__tw_version;let{version:e}=JSON.parse(Oe.readFileSync(ce("tailwindcss/package.json"),"utf-8"));return e}import J from"fs/promises";import ze from"path";function Q(){return new Promise((e,t)=>{let n="";process.stdin.on("data",i=>{n+=i}),process.stdin.on("end",()=>e(n)),process.stdin.on("error",i=>t(i))})}async function Y(e,t){try{if(await J.readFile(e,"utf8")===t)return}catch{}await J.mkdir(ze.dirname(e),{recursive:!0}),await J.writeFile(e,t,"utf8")}var ye=String.raw,l=Ee.DEBUG;function U(){return{"--input":{type:"string",description:"Input file",alias:"-i"},"--output":{type:"string",description:"Output file",alias:"-o",default:"-"},"--watch":{type:"boolean | string",description:"Watch for changes and rebuild as needed",alias:"-w"},"--minify":{type:"boolean",description:"Optimize and minify the output",alias:"-m"},"--optimize":{type:"boolean",description:"Optimize the output without minifying"},"--cwd":{type:"string",description:"The current working directory",default:"."},"--map":{type:"boolean | string",description:"Generate a source map",default:!1}}}async function H(e){try{return await e()}catch(t){t instanceof Error&&g(t.toString()),process.exit(1)}}async function ge(e){var ne=[];try{g(D());g();let t=q(ne,new me);l&&t.start("[@tailwindcss/cli] (initial build)");let n=v.resolve(e["--cwd"]);e["--output"]&&e["--output"]!=="-"&&(e["--output"]=v.resolve(n,e["--output"]));e["--input"]&&e["--input"]!=="-"&&(e["--input"]=v.resolve(n,e["--input"]),je(e["--input"])||(g(`Specified input file ${A(N(e["--input"]))} does not exist.`),process.exit(1)));e["--input"]===e["--output"]&&e["--input"]!=="-"&&(g(`Specified input file ${A(N(e["--input"]))} and output file ${A(N(e["--output"]))} are identical.`),process.exit(1));e["--map"]==="-"&&(g("Use --map without a value to inline the source map"),process.exit(1));e["--map"]&&e["--map"]!==!0&&(e["--map"]=v.resolve(n,e["--map"]));let i=process.hrtime.bigint();let o=e["--input"]?e["--input"]==="-"?await Q():await X.readFile(e["--input"],"utf-8"):ye`
        @import 'tailwindcss';
      `;let r={css:"",optimizedCss:""};async function u(S,x,f,w){let $=S;if(f["--minify"]||f["--optimize"])if(S!==r.css){l&&w.start("Optimize CSS");let T=Ue(S,{file:f["--input"]??"input.css",minify:f["--minify"]??!1,map:x?.raw??void 0});l&&w.end("Optimize CSS"),r.css=S,r.optimizedCss=T.code,T.map&&(x=he(T.map)),$=T.code}else $=r.optimizedCss;x&&(f["--map"]===!0?($+=`
`,$+=x.inline):typeof f["--map"]=="string"&&(l&&w.start("Write source map"),await Y(f["--map"],x.raw),l&&w.end("Write source map"))),l&&w.start("Write output"),f["--output"]&&f["--output"]!=="-"?await Y(f["--output"],$):h($),l&&w.end("Write output")}let s=e["--input"]&&e["--input"]!=="-"?v.resolve(e["--input"]):null;let a=s?v.dirname(s):process.cwd();let p=s?[s]:[];async function d(S,x){l&&x.start("Setup compiler");let f=await Ne(S,{from:e["--output"]?s??"stdin.css":void 0,base:a,onDependency(T){p.push(T)}}),w=(f.root==="none"?[]:f.root===null?[{base:n,pattern:"**/*",negated:!1}]:[{...f.root,negated:!1}]).concat(f.sources),$=new Ie({sources:w});return l&&x.end("Setup compiler"),[f,$]}let[m,y]=await H(()=>d(o,t));if(e["--watch"]){let S=await be(we(y),async function x(f){try{var w=[];try{if(f.length===1&&f[0]===e["--output"])return;let c=q(w,new me);l&&c.start("[@tailwindcss/cli] (watcher)");let ie=process.hrtime.bigint();let re=[];let I="incremental";let oe=p;for(let _ of f){if(oe.includes(_)){I="full";break}re.push({file:_,extension:v.extname(_).slice(1)})}let j="";let P=null;if(I==="full"){let _=e["--input"]?e["--input"]==="-"?await Q():await X.readFile(e["--input"],"utf-8"):ye`
                  @import 'tailwindcss';
                `;Le(oe),p=s?[s]:[],[m,y]=await d(_,c),l&&c.start("Scan for candidates");let V=y.scan();l&&c.end("Scan for candidates"),l&&c.start("Setup new watchers");let Ae=await be(we(y),x);l&&c.end("Setup new watchers"),l&&c.start("Cleanup old watchers"),await S(),l&&c.end("Cleanup old watchers"),S=Ae,l&&c.start("Build CSS"),j=m.build(V),l&&c.end("Build CSS"),e["--map"]&&(l&&c.start("Build Source Map"),P=m.buildSourceMap(),l&&c.end("Build Source Map"))}else if(I==="incremental"){l&&c.start("Scan for candidates");let _=y.scanFiles(re);if(l&&c.end("Scan for candidates"),_.length<=0){let V=process.hrtime.bigint();g(`Done in ${E(V-ie)}`);return}l&&c.start("Build CSS"),j=m.build(_),l&&c.end("Build CSS"),e["--map"]&&(l&&c.start("Build Source Map"),P=m.buildSourceMap(),l&&c.end("Build Source Map"))}await u(j,P,e,c);let ve=process.hrtime.bigint();g(`Done in ${E(ve-ie)}`)}catch($){var T=$,Ce=!0}finally{K(w,T,Ce)}}catch(c){c instanceof Error&&g(c.toString())}});e["--watch"]!=="always"&&process.stdin.on("end",()=>{S().then(()=>process.exit(0),()=>process.exit(1))}),process.stdin.resume()}l&&t.start("Scan for candidates");let L=y.scan();l&&t.end("Scan for candidates");l&&t.start("Build CSS");let k=await H(()=>m.build(L));l&&t.end("Build CSS");let B=null;e["--map"]&&(l&&t.start("Build Source Map"),B=await H(()=>he(m.buildSourceMap())),l&&t.end("Build Source Map"));await u(k,B,e,t);let xe=process.hrtime.bigint();g(`Done in ${E(xe-i)}`)}catch($e){var Te=$e,_e=!0}finally{K(ne,Te,_e)}}async function be(e,t){e=e.sort((s,a)=>s.length-a.length);let n=[];for(let s=0;s<e.length;++s)for(let a=0;a<s;++a)e[s].startsWith(`${e[a]}/`)&&n.push(e[s]);e=e.filter(s=>!n.includes(s));let i=new F,o=new Set,r=new F;async function u(){await r.dispose(),r.queueMacrotask(()=>{t(Array.from(o)),o.clear()})}for(let s of e){let{unsubscribe:a}=await De.subscribe(s,async(p,d)=>{if(p){console.error(p);return}await Promise.all(d.map(async m=>{if(m.type==="delete")return;let y=null;try{y=await X.lstat(m.path)}catch{}!y?.isFile()&&!y?.isSymbolicLink()||o.add(m.path)})),await u()});i.add(a)}return async()=>{await i.dispose(),await r.dispose()}}function we(e){return[...new Set(e.normalizedSources.flatMap(t=>t.base))]}import C from"picocolors";function Z({invalid:e,usage:t,options:n}){let i=process.stdout.columns;if(h(D()),e&&(h(),h(`${C.dim("Invalid command:")} ${e}`)),t&&t.length>0){h(),h(C.dim("Usage:"));for(let[o,r]of t.entries()){let u=r.slice(0,r.indexOf("[")),s=r.slice(r.indexOf("["));s=s.replace(/\[.*?\]/g,d=>C.dim(d));let p=G(s,i-z.indent-u.length-1);p.length>1&&o!==0&&h(),h(M(`${u}${p.shift()}`));for(let d of p)h(M(d,u.length))}}if(n){let o=0;for(let{alias:a}of Object.values(n))a&&(o=Math.max(o,a.length));let r=[],u=0;for(let[a,{alias:p}]of Object.entries(n)){let d=[p&&`${p.padStart(o)}`,p?a:" ".repeat(o+2)+a].filter(Boolean).join(", ");r.push(d),u=Math.max(u,d.length)}h(),h(C.dim("Options:"));let s=8;for(let{description:a,default:p=null}of Object.values(n)){let d=r.shift(),m=s+(u-d.length),y=2,L=i-d.length-m-y-z.indent,k=G(p!==null?`${a} ${C.dim(`[default:\u202F${A(`${p}`)}]`)}`:a,L);h(M(`${C.blue(d)} ${C.dim(C.gray("\xB7")).repeat(m)} ${k.shift()}`));for(let B of k)h(M(`${" ".repeat(d.length+m+y)}${B}`))}}}var ee={"--help":{type:"boolean",description:"Display usage information",alias:"-h"}},te=ue({...U(),...ee}),Se=te._[0];Se&&(Z({invalid:Se,usage:["tailwindcss [options]"],options:{...U(),...ee}}),process.exit(1));(process.stdout.isTTY&&process.argv[2]===void 0||te["--help"])&&(Z({usage:["tailwindcss [--input input.css] [--output output.css] [--watch] [options\u2026]"],options:{...U(),...ee}}),process.exit(0));ge(te);
