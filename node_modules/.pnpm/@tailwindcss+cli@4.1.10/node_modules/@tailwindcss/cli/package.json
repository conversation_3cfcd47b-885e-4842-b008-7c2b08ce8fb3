{"name": "@tailwindcss/cli", "version": "4.1.10", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tailwindlabs/tailwindcss.git", "directory": "packages/@tailwindcss-cli"}, "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "bin": {"tailwindcss": "./dist/index.mjs"}, "exports": {"./package.json": "./package.json"}, "files": ["dist"], "publishConfig": {"provenance": true, "access": "public"}, "dependencies": {"@parcel/watcher": "^2.5.1", "enhanced-resolve": "^5.18.1", "mri": "^1.2.0", "picocolors": "^1.1.1", "@tailwindcss/node": "4.1.10", "@tailwindcss/oxide": "4.1.10", "tailwindcss": "4.1.10"}, "scripts": {"lint": "tsc --noEmit", "build": "tsup-node", "dev": "pnpm run build -- --watch"}}