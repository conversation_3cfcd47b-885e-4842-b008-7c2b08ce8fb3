{"name": "@sindresorhus/transliterate", "version": "1.6.0", "description": "Convert Unicode characters to Latin characters using transliteration", "license": "MIT", "repository": "sindresorhus/transliterate", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "replacements.js"], "keywords": ["transliterate", "transliteration", "string", "deburr", "unicode", "ascii", "text", "latin", "latinize", "convert", "replace"], "dependencies": {"escape-string-regexp": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}