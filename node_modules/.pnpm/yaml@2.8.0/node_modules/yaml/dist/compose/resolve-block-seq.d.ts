import { YAMLSeq } from '../nodes/YAMLSeq';
import type { BlockSequence } from '../parse/cst';
import type { CollectionTag } from '../schema/types';
import type { ComposeContext, ComposeNode } from './compose-node';
import type { ComposeErrorHandler } from './composer';
export declare function resolveBlockSeq({ composeNode, composeEmptyNode }: ComposeNode, ctx: ComposeContext, bs: BlockSequence, onError: ComposeErrorHandler, tag?: CollectionTag): YAMLSeq.Parsed;
