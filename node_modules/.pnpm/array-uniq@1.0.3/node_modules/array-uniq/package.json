{"name": "array-uniq", "version": "1.0.3", "description": "Create an array without duplicates", "license": "MIT", "repository": "sindresorhus/array-uniq", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["array", "arr", "set", "uniq", "unique", "es6", "duplicate", "remove"], "devDependencies": {"ava": "*", "es6-set": "^0.1.0", "require-uncached": "^1.0.2", "xo": "*"}}