{"name": "acorn-walk", "description": "ECMAScript (ESTree) AST walker", "homepage": "https://github.com/acornjs/acorn", "main": "dist/walk.js", "types": "dist/walk.d.ts", "module": "dist/walk.mjs", "exports": {".": [{"import": "./dist/walk.mjs", "require": "./dist/walk.js", "default": "./dist/walk.js"}, "./dist/walk.js"], "./package.json": "./package.json"}, "version": "8.3.4", "engines": {"node": ">=0.4.0"}, "dependencies": {"acorn": "^8.11.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "https://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "https://rreverser.com/"}, {"name": "<PERSON>", "web": "http://adrianheine.de"}], "repository": {"type": "git", "url": "https://github.com/acornjs/acorn.git"}, "scripts": {"prepare": "cd ..; npm run build:walk"}, "license": "MIT"}