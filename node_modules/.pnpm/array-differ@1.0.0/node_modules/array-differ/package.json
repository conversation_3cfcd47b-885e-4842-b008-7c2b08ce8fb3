{"name": "array-differ", "version": "1.0.0", "description": "Create an array with values that are present in the first input array but not additional ones", "license": "MIT", "repository": "sindresorhus/array-differ", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["array", "difference", "diff", "differ", "filter", "exclude"], "devDependencies": {"mocha": "*"}}