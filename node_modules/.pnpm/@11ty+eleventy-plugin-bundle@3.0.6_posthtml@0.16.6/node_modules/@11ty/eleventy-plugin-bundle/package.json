{"name": "@11ty/eleventy-plugin-bundle", "version": "3.0.6", "description": "Little bundles of code, little bundles of joy.", "main": "eleventy.bundle.js", "type": "module", "scripts": {"sample": "DEBUG=Eleventy:Bundle npx @11ty/eleventy --config=sample/sample-config.js --input=sample --serve", "test": "npx ava"}, "publishConfig": {"access": "public"}, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "keywords": ["eleventy", "eleventy-plugin"], "repository": {"type": "git", "url": "git://github.com/11ty/eleventy-plugin-bundle.git"}, "bugs": "https://github.com/11ty/eleventy-plugin-bundle/issues", "homepage": "https://www.11ty.dev/", "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "ava": {"failFast": true, "files": ["test/*.js", "test/*.mjs"], "watchMode": {"ignoreChanges": ["**/_site/**", ".cache"]}}, "devDependencies": {"@11ty/eleventy": "^3.0.0", "ava": "^6.2.0", "postcss": "^8.5.3", "postcss-nested": "^7.0.2", "sass": "^1.86.3"}, "dependencies": {"@11ty/eleventy-utils": "^2.0.2", "debug": "^4.4.0", "posthtml-match-helper": "^2.0.3"}}