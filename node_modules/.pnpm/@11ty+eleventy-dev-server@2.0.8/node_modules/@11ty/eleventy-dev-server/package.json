{"name": "@11ty/eleventy-dev-server", "version": "2.0.8", "description": "A minimal, modern, generic, hot-reloading local web server to help web developers.", "main": "server.js", "scripts": {"test": "npx ava --verbose", "sample": "node cmd.js --input=test/stubs"}, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "bin": {"eleventy-dev-server": "./cmd.js"}, "keywords": ["eleventy", "server", "cli"], "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "repository": {"type": "git", "url": "git://github.com/11ty/eleventy-dev-server.git"}, "bugs": "https://github.com/11ty/eleventy-dev-server/issues", "homepage": "https://github.com/11ty/eleventy-dev-server/", "dependencies": {"@11ty/eleventy-utils": "^2.0.1", "chokidar": "^3.6.0", "debug": "^4.4.0", "finalhandler": "^1.3.1", "mime": "^3.0.0", "minimist": "^1.2.8", "morphdom": "^2.7.4", "please-upgrade-node": "^3.2.0", "send": "^1.1.0", "ssri": "^11.0.0", "urlpattern-polyfill": "^10.0.0", "ws": "^8.18.1"}, "devDependencies": {"ava": "^6.2.0"}}