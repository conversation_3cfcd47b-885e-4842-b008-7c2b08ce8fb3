name: Publish Release to npm
on:
  release:
    types: [published]
permissions: read-all
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # 4.1.7
      - uses: actions/setup-node@1e60f620b9541d16bece96c5465dc8ee9832be0b # 4.0.3
        with:
          node-version: "20"
          registry-url: 'https://registry.npmjs.org'
      - run: npm ci
      - run: npm test
      - if: ${{ github.event.release.tag_name != '' && env.NPM_PUBLISH_TAG != '' }}
        run: npm publish --provenance --access=public --tag=${{ env.NPM_PUBLISH_TAG }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_PUBLISH_TAG: ${{ contains(github.event.release.tag_name, '-beta.') && 'beta' || 'latest' }}
