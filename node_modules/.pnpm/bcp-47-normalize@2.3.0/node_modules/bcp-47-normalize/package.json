{"name": "bcp-47-normalize", "version": "2.3.0", "description": "Normalize, canonicalize, and format BCP 47 tags", "license": "MIT", "keywords": ["bcp", "47", "bcp47", "bcp-47", "language", "region", "script", "tag", "subtag", "format", "pretty", "normal", "canonical"], "repository": "wooorm/bcp-47-normalize", "bugs": "https://github.com/wooorm/bcp-47-normalize/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"bcp-47": "^2.0.0", "bcp-47-match": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/node-fetch": "^3.0.0", "@types/xast": "^2.0.0", "c8": "^8.0.0", "cldr-core": "^43.0.0", "node-fetch": "^3.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unist-util-visit": "^5.0.0", "xast-util-from-xml": "^3.0.0", "xo": "^0.55.0"}, "scripts": {"prepack": "npm run generate && npm run build && npm run format", "generate": "node --conditions development build.js", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}