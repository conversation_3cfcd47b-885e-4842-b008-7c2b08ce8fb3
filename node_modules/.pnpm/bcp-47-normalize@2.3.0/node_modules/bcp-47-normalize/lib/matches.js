/**
 * @typedef Change
 * @property {string} from
 * @property {string} to
 */

/**
 * @type {Array<Change>}
 */
export const matches = [
  {
    from: 'in',
    to: 'id'
  },
  {
    from: 'iw',
    to: 'he'
  },
  {
    from: 'ji',
    to: 'yi'
  },
  {
    from: 'jw',
    to: 'jv'
  },
  {
    from: 'mo',
    to: 'ro'
  },
  {
    from: 'scc',
    to: 'sr'
  },
  {
    from: 'scr',
    to: 'hr'
  },
  {
    from: 'aam',
    to: 'aas'
  },
  {
    from: 'adp',
    to: 'dz'
  },
  {
    from: 'aue',
    to: 'ktz'
  },
  {
    from: 'ayx',
    to: 'nun'
  },
  {
    from: 'bgm',
    to: 'bcg'
  },
  {
    from: 'bjd',
    to: 'drl'
  },
  {
    from: 'ccq',
    to: 'rki'
  },
  {
    from: 'cjr',
    to: 'mom'
  },
  {
    from: 'cka',
    to: 'cmr'
  },
  {
    from: 'cmk',
    to: 'xch'
  },
  {
    from: 'coy',
    to: 'pij'
  },
  {
    from: 'cqu',
    to: 'quh'
  },
  {
    from: 'drh',
    to: 'mn'
  },
  {
    from: 'drw',
    to: 'fa-af'
  },
  {
    from: 'gav',
    to: 'dev'
  },
  {
    from: 'gfx',
    to: 'vaj'
  },
  {
    from: 'ggn',
    to: 'gvr'
  },
  {
    from: 'gti',
    to: 'nyc'
  },
  {
    from: 'guv',
    to: 'duz'
  },
  {
    from: 'hrr',
    to: 'jal'
  },
  {
    from: 'ibi',
    to: 'opa'
  },
  {
    from: 'ilw',
    to: 'gal'
  },
  {
    from: 'jeg',
    to: 'oyb'
  },
  {
    from: 'kgc',
    to: 'tdf'
  },
  {
    from: 'kgh',
    to: 'kml'
  },
  {
    from: 'koj',
    to: 'kwv'
  },
  {
    from: 'krm',
    to: 'bmf'
  },
  {
    from: 'ktr',
    to: 'dtp'
  },
  {
    from: 'kvs',
    to: 'gdj'
  },
  {
    from: 'kwq',
    to: 'yam'
  },
  {
    from: 'kxe',
    to: 'tvd'
  },
  {
    from: 'kzj',
    to: 'dtp'
  },
  {
    from: 'kzt',
    to: 'dtp'
  },
  {
    from: 'lii',
    to: 'raq'
  },
  {
    from: 'lmm',
    to: 'rmx'
  },
  {
    from: 'meg',
    to: 'cir'
  },
  {
    from: 'mst',
    to: 'mry'
  },
  {
    from: 'mwj',
    to: 'vaj'
  },
  {
    from: 'myt',
    to: 'mry'
  },
  {
    from: 'nad',
    to: 'xny'
  },
  {
    from: 'ncp',
    to: 'kdz'
  },
  {
    from: 'nnx',
    to: 'ngv'
  },
  {
    from: 'nts',
    to: 'pij'
  },
  {
    from: 'oun',
    to: 'vaj'
  },
  {
    from: 'pcr',
    to: 'adx'
  },
  {
    from: 'pmc',
    to: 'huw'
  },
  {
    from: 'pmu',
    to: 'phr'
  },
  {
    from: 'ppa',
    to: 'bfy'
  },
  {
    from: 'ppr',
    to: 'lcq'
  },
  {
    from: 'pry',
    to: 'prt'
  },
  {
    from: 'puz',
    to: 'pub'
  },
  {
    from: 'sca',
    to: 'hle'
  },
  {
    from: 'skk',
    to: 'oyb'
  },
  {
    from: 'tdu',
    to: 'dtp'
  },
  {
    from: 'thc',
    to: 'tpo'
  },
  {
    from: 'thx',
    to: 'oyb'
  },
  {
    from: 'tie',
    to: 'ras'
  },
  {
    from: 'tkk',
    to: 'twm'
  },
  {
    from: 'tlw',
    to: 'weo'
  },
  {
    from: 'tmp',
    to: 'tyj'
  },
  {
    from: 'tne',
    to: 'kak'
  },
  {
    from: 'tnf',
    to: 'fa-af'
  },
  {
    from: 'tsf',
    to: 'taj'
  },
  {
    from: 'uok',
    to: 'ema'
  },
  {
    from: 'xba',
    to: 'cax'
  },
  {
    from: 'xia',
    to: 'acn'
  },
  {
    from: 'xkh',
    to: 'waw'
  },
  {
    from: 'xsj',
    to: 'suj'
  },
  {
    from: 'ybd',
    to: 'rki'
  },
  {
    from: 'yma',
    to: 'lrr'
  },
  {
    from: 'ymt',
    to: 'mtm'
  },
  {
    from: 'yos',
    to: 'zom'
  },
  {
    from: 'yuu',
    to: 'yug'
  },
  {
    from: 'asd',
    to: 'snz'
  },
  {
    from: 'dit',
    to: 'dif'
  },
  {
    from: 'llo',
    to: 'ngt'
  },
  {
    from: 'myd',
    to: 'aog'
  },
  {
    from: 'nns',
    to: 'nbr'
  },
  {
    from: 'agp',
    to: 'apf'
  },
  {
    from: 'ais',
    to: 'ami'
  },
  {
    from: 'ajt',
    to: 'aeb'
  },
  {
    from: 'baz',
    to: 'nvo'
  },
  {
    from: 'bhk',
    to: 'fbl'
  },
  {
    from: 'bic',
    to: 'bir'
  },
  {
    from: 'bjq',
    to: 'bzc'
  },
  {
    from: 'bkb',
    to: 'ebk'
  },
  {
    from: 'blg',
    to: 'iba'
  },
  {
    from: 'btb',
    to: 'beb'
  },
  {
    from: 'daf',
    to: 'dnj'
  },
  {
    from: 'dap',
    to: 'njz'
  },
  {
    from: 'djl',
    to: 'dze'
  },
  {
    from: 'dkl',
    to: 'aqd'
  },
  {
    from: 'drr',
    to: 'kzk'
  },
  {
    from: 'dud',
    to: 'uth'
  },
  {
    from: 'duj',
    to: 'dwu'
  },
  {
    from: 'dwl',
    to: 'dbt'
  },
  {
    from: 'elp',
    to: 'amq'
  },
  {
    from: 'gbc',
    to: 'wny'
  },
  {
    from: 'ggo',
    to: 'esg'
  },
  {
    from: 'ggr',
    to: 'gtu'
  },
  {
    from: 'gio',
    to: 'aou'
  },
  {
    from: 'gli',
    to: 'kzk'
  },
  {
    from: 'ill',
    to: 'ilm'
  },
  {
    from: 'izi',
    to: 'eza'
  },
  {
    from: 'jar',
    to: 'jgk'
  },
  {
    from: 'kdv',
    to: 'zkd'
  },
  {
    from: 'kgd',
    to: 'ncq'
  },
  {
    from: 'kpp',
    to: 'jkm'
  },
  {
    from: 'kxl',
    to: 'kru'
  },
  {
    from: 'kzh',
    to: 'dgl'
  },
  {
    from: 'lak',
    to: 'ksp'
  },
  {
    from: 'leg',
    to: 'enl'
  },
  {
    from: 'mgx',
    to: 'jbk'
  },
  {
    from: 'mnt',
    to: 'wnn'
  },
  {
    from: 'mof',
    to: 'xnt'
  },
  {
    from: 'mwd',
    to: 'dmw'
  },
  {
    from: 'nbf',
    to: 'nru'
  },
  {
    from: 'nbx',
    to: 'ekc'
  },
  {
    from: 'nln',
    to: 'azd'
  },
  {
    from: 'nlr',
    to: 'nrk'
  },
  {
    from: 'noo',
    to: 'dtd'
  },
  {
    from: 'nxu',
    to: 'bpp'
  },
  {
    from: 'pat',
    to: 'kxr'
  },
  {
    from: 'rmr',
    to: 'emx'
  },
  {
    from: 'sap',
    to: 'aqt'
  },
  {
    from: 'sgl',
    to: 'isk'
  },
  {
    from: 'smd',
    to: 'kmb'
  },
  {
    from: 'snb',
    to: 'iba'
  },
  {
    from: 'sul',
    to: 'sgd'
  },
  {
    from: 'sum',
    to: 'ulw'
  },
  {
    from: 'tgg',
    to: 'bjp'
  },
  {
    from: 'thw',
    to: 'ola'
  },
  {
    from: 'tid',
    to: 'itd'
  },
  {
    from: 'unp',
    to: 'wro'
  },
  {
    from: 'wgw',
    to: 'wgb'
  },
  {
    from: 'wit',
    to: 'nol'
  },
  {
    from: 'wiw',
    to: 'nwo'
  },
  {
    from: 'xrq',
    to: 'dmw'
  },
  {
    from: 'yen',
    to: 'ynq'
  },
  {
    from: 'yiy',
    to: 'yrm'
  },
  {
    from: 'zir',
    to: 'scv'
  },
  {
    from: 'sgn-br',
    to: 'bzs'
  },
  {
    from: 'sgn-co',
    to: 'csn'
  },
  {
    from: 'sgn-de',
    to: 'gsg'
  },
  {
    from: 'sgn-dk',
    to: 'dsl'
  },
  {
    from: 'sgn-fr',
    to: 'fsl'
  },
  {
    from: 'sgn-gb',
    to: 'bfi'
  },
  {
    from: 'sgn-gr',
    to: 'gss'
  },
  {
    from: 'sgn-ie',
    to: 'isg'
  },
  {
    from: 'sgn-it',
    to: 'ise'
  },
  {
    from: 'sgn-jp',
    to: 'jsl'
  },
  {
    from: 'sgn-mx',
    to: 'mfs'
  },
  {
    from: 'sgn-ni',
    to: 'ncs'
  },
  {
    from: 'sgn-nl',
    to: 'dse'
  },
  {
    from: 'sgn-no',
    to: 'nsi'
  },
  {
    from: 'sgn-pt',
    to: 'psr'
  },
  {
    from: 'sgn-se',
    to: 'swl'
  },
  {
    from: 'sgn-us',
    to: 'ase'
  },
  {
    from: 'sgn-za',
    to: 'sfs'
  },
  {
    from: 'sgn-es',
    to: 'ssp'
  },
  {
    from: 'zh-cmn',
    to: 'zh'
  },
  {
    from: 'zh-cmn-hans',
    to: 'zh-hans'
  },
  {
    from: 'zh-cmn-hant',
    to: 'zh-hant'
  },
  {
    from: 'zh-gan',
    to: 'gan'
  },
  {
    from: 'zh-wuu',
    to: 'wuu'
  },
  {
    from: 'zh-yue',
    to: 'yue'
  },
  {
    from: 'no-bokmal',
    to: 'nb'
  },
  {
    from: 'no-nynorsk',
    to: 'nn'
  },
  {
    from: 'aa-saaho',
    to: 'ssy'
  },
  {
    from: 'sh',
    to: 'sr-latn'
  },
  {
    from: 'cnr',
    to: 'sr-me'
  },
  {
    from: 'tl',
    to: 'fil'
  },
  {
    from: 'aju',
    to: 'jrb'
  },
  {
    from: 'als',
    to: 'sq'
  },
  {
    from: 'arb',
    to: 'ar'
  },
  {
    from: 'ayr',
    to: 'ay'
  },
  {
    from: 'azj',
    to: 'az'
  },
  {
    from: 'bcc',
    to: 'bal'
  },
  {
    from: 'bcl',
    to: 'bik'
  },
  {
    from: 'bxk',
    to: 'luy'
  },
  {
    from: 'bxr',
    to: 'bua'
  },
  {
    from: 'cld',
    to: 'syr'
  },
  {
    from: 'cmn',
    to: 'zh'
  },
  {
    from: 'cwd',
    to: 'cr'
  },
  {
    from: 'dgo',
    to: 'doi'
  },
  {
    from: 'dhd',
    to: 'mwr'
  },
  {
    from: 'dik',
    to: 'din'
  },
  {
    from: 'diq',
    to: 'zza'
  },
  {
    from: 'lbk',
    to: 'bnc'
  },
  {
    from: 'ekk',
    to: 'et'
  },
  {
    from: 'emk',
    to: 'man'
  },
  {
    from: 'esk',
    to: 'ik'
  },
  {
    from: 'fat',
    to: 'ak'
  },
  {
    from: 'fuc',
    to: 'ff'
  },
  {
    from: 'gaz',
    to: 'om'
  },
  {
    from: 'gbo',
    to: 'grb'
  },
  {
    from: 'gno',
    to: 'gon'
  },
  {
    from: 'gug',
    to: 'gn'
  },
  {
    from: 'gya',
    to: 'gba'
  },
  {
    from: 'hdn',
    to: 'hai'
  },
  {
    from: 'hea',
    to: 'hmn'
  },
  {
    from: 'ike',
    to: 'iu'
  },
  {
    from: 'kmr',
    to: 'ku'
  },
  {
    from: 'knc',
    to: 'kr'
  },
  {
    from: 'kng',
    to: 'kg'
  },
  {
    from: 'knn',
    to: 'kok'
  },
  {
    from: 'kpv',
    to: 'kv'
  },
  {
    from: 'lvs',
    to: 'lv'
  },
  {
    from: 'mhr',
    to: 'chm'
  },
  {
    from: 'mup',
    to: 'raj'
  },
  {
    from: 'khk',
    to: 'mn'
  },
  {
    from: 'npi',
    to: 'ne'
  },
  {
    from: 'ojg',
    to: 'oj'
  },
  {
    from: 'ory',
    to: 'or'
  },
  {
    from: 'pbu',
    to: 'ps'
  },
  {
    from: 'pes',
    to: 'fa'
  },
  {
    from: 'plt',
    to: 'mg'
  },
  {
    from: 'pnb',
    to: 'lah'
  },
  {
    from: 'quz',
    to: 'qu'
  },
  {
    from: 'rmy',
    to: 'rom'
  },
  {
    from: 'spy',
    to: 'kln'
  },
  {
    from: 'src',
    to: 'sc'
  },
  {
    from: 'swh',
    to: 'sw'
  },
  {
    from: 'ttq',
    to: 'tmh'
  },
  {
    from: 'tw',
    to: 'ak'
  },
  {
    from: 'umu',
    to: 'del'
  },
  {
    from: 'uzn',
    to: 'uz'
  },
  {
    from: 'xpe',
    to: 'kpe'
  },
  {
    from: 'xsl',
    to: 'den'
  },
  {
    from: 'ydd',
    to: 'yi'
  },
  {
    from: 'zai',
    to: 'zap'
  },
  {
    from: 'zsm',
    to: 'ms'
  },
  {
    from: 'zyb',
    to: 'za'
  },
  {
    from: 'him',
    to: 'srx'
  },
  {
    from: 'mnk',
    to: 'man'
  },
  {
    from: 'bh',
    to: 'bho'
  },
  {
    from: 'prs',
    to: 'fa-af'
  },
  {
    from: 'swc',
    to: 'sw-cd'
  },
  {
    from: 'aar',
    to: 'aa'
  },
  {
    from: 'abk',
    to: 'ab'
  },
  {
    from: 'ave',
    to: 'ae'
  },
  {
    from: 'afr',
    to: 'af'
  },
  {
    from: 'aka',
    to: 'ak'
  },
  {
    from: 'amh',
    to: 'am'
  },
  {
    from: 'arg',
    to: 'an'
  },
  {
    from: 'ara',
    to: 'ar'
  },
  {
    from: 'asm',
    to: 'as'
  },
  {
    from: 'ava',
    to: 'av'
  },
  {
    from: 'aym',
    to: 'ay'
  },
  {
    from: 'aze',
    to: 'az'
  },
  {
    from: 'bak',
    to: 'ba'
  },
  {
    from: 'bel',
    to: 'be'
  },
  {
    from: 'bul',
    to: 'bg'
  },
  {
    from: 'bih',
    to: 'bho'
  },
  {
    from: 'bis',
    to: 'bi'
  },
  {
    from: 'bam',
    to: 'bm'
  },
  {
    from: 'ben',
    to: 'bn'
  },
  {
    from: 'bod',
    to: 'bo'
  },
  {
    from: 'bre',
    to: 'br'
  },
  {
    from: 'bos',
    to: 'bs'
  },
  {
    from: 'cat',
    to: 'ca'
  },
  {
    from: 'che',
    to: 'ce'
  },
  {
    from: 'cha',
    to: 'ch'
  },
  {
    from: 'cos',
    to: 'co'
  },
  {
    from: 'cre',
    to: 'cr'
  },
  {
    from: 'ces',
    to: 'cs'
  },
  {
    from: 'chu',
    to: 'cu'
  },
  {
    from: 'chv',
    to: 'cv'
  },
  {
    from: 'cym',
    to: 'cy'
  },
  {
    from: 'dan',
    to: 'da'
  },
  {
    from: 'deu',
    to: 'de'
  },
  {
    from: 'div',
    to: 'dv'
  },
  {
    from: 'dzo',
    to: 'dz'
  },
  {
    from: 'ewe',
    to: 'ee'
  },
  {
    from: 'ell',
    to: 'el'
  },
  {
    from: 'eng',
    to: 'en'
  },
  {
    from: 'epo',
    to: 'eo'
  },
  {
    from: 'spa',
    to: 'es'
  },
  {
    from: 'est',
    to: 'et'
  },
  {
    from: 'eus',
    to: 'eu'
  },
  {
    from: 'fas',
    to: 'fa'
  },
  {
    from: 'ful',
    to: 'ff'
  },
  {
    from: 'fin',
    to: 'fi'
  },
  {
    from: 'fij',
    to: 'fj'
  },
  {
    from: 'fao',
    to: 'fo'
  },
  {
    from: 'fra',
    to: 'fr'
  },
  {
    from: 'fry',
    to: 'fy'
  },
  {
    from: 'gle',
    to: 'ga'
  },
  {
    from: 'gla',
    to: 'gd'
  },
  {
    from: 'glg',
    to: 'gl'
  },
  {
    from: 'grn',
    to: 'gn'
  },
  {
    from: 'guj',
    to: 'gu'
  },
  {
    from: 'glv',
    to: 'gv'
  },
  {
    from: 'hau',
    to: 'ha'
  },
  {
    from: 'heb',
    to: 'he'
  },
  {
    from: 'hin',
    to: 'hi'
  },
  {
    from: 'hmo',
    to: 'ho'
  },
  {
    from: 'hrv',
    to: 'hr'
  },
  {
    from: 'hat',
    to: 'ht'
  },
  {
    from: 'hun',
    to: 'hu'
  },
  {
    from: 'hye',
    to: 'hy'
  },
  {
    from: 'her',
    to: 'hz'
  },
  {
    from: 'ina',
    to: 'ia'
  },
  {
    from: 'ind',
    to: 'id'
  },
  {
    from: 'ile',
    to: 'ie'
  },
  {
    from: 'ibo',
    to: 'ig'
  },
  {
    from: 'iii',
    to: 'ii'
  },
  {
    from: 'ipk',
    to: 'ik'
  },
  {
    from: 'ido',
    to: 'io'
  },
  {
    from: 'isl',
    to: 'is'
  },
  {
    from: 'ita',
    to: 'it'
  },
  {
    from: 'iku',
    to: 'iu'
  },
  {
    from: 'jpn',
    to: 'ja'
  },
  {
    from: 'jav',
    to: 'jv'
  },
  {
    from: 'kat',
    to: 'ka'
  },
  {
    from: 'kon',
    to: 'kg'
  },
  {
    from: 'kik',
    to: 'ki'
  },
  {
    from: 'kua',
    to: 'kj'
  },
  {
    from: 'kaz',
    to: 'kk'
  },
  {
    from: 'kal',
    to: 'kl'
  },
  {
    from: 'khm',
    to: 'km'
  },
  {
    from: 'kan',
    to: 'kn'
  },
  {
    from: 'kor',
    to: 'ko'
  },
  {
    from: 'kau',
    to: 'kr'
  },
  {
    from: 'kas',
    to: 'ks'
  },
  {
    from: 'kur',
    to: 'ku'
  },
  {
    from: 'kom',
    to: 'kv'
  },
  {
    from: 'cor',
    to: 'kw'
  },
  {
    from: 'kir',
    to: 'ky'
  },
  {
    from: 'lat',
    to: 'la'
  },
  {
    from: 'ltz',
    to: 'lb'
  },
  {
    from: 'lug',
    to: 'lg'
  },
  {
    from: 'lim',
    to: 'li'
  },
  {
    from: 'lin',
    to: 'ln'
  },
  {
    from: 'lao',
    to: 'lo'
  },
  {
    from: 'lit',
    to: 'lt'
  },
  {
    from: 'lub',
    to: 'lu'
  },
  {
    from: 'lav',
    to: 'lv'
  },
  {
    from: 'mlg',
    to: 'mg'
  },
  {
    from: 'mah',
    to: 'mh'
  },
  {
    from: 'mri',
    to: 'mi'
  },
  {
    from: 'mkd',
    to: 'mk'
  },
  {
    from: 'mal',
    to: 'ml'
  },
  {
    from: 'mon',
    to: 'mn'
  },
  {
    from: 'mol',
    to: 'ro'
  },
  {
    from: 'mar',
    to: 'mr'
  },
  {
    from: 'msa',
    to: 'ms'
  },
  {
    from: 'mlt',
    to: 'mt'
  },
  {
    from: 'mya',
    to: 'my'
  },
  {
    from: 'nau',
    to: 'na'
  },
  {
    from: 'nob',
    to: 'nb'
  },
  {
    from: 'nde',
    to: 'nd'
  },
  {
    from: 'nep',
    to: 'ne'
  },
  {
    from: 'ndo',
    to: 'ng'
  },
  {
    from: 'nld',
    to: 'nl'
  },
  {
    from: 'nno',
    to: 'nn'
  },
  {
    from: 'nor',
    to: 'no'
  },
  {
    from: 'nbl',
    to: 'nr'
  },
  {
    from: 'nav',
    to: 'nv'
  },
  {
    from: 'nya',
    to: 'ny'
  },
  {
    from: 'oci',
    to: 'oc'
  },
  {
    from: 'oji',
    to: 'oj'
  },
  {
    from: 'orm',
    to: 'om'
  },
  {
    from: 'ori',
    to: 'or'
  },
  {
    from: 'oss',
    to: 'os'
  },
  {
    from: 'pan',
    to: 'pa'
  },
  {
    from: 'pli',
    to: 'pi'
  },
  {
    from: 'pol',
    to: 'pl'
  },
  {
    from: 'pus',
    to: 'ps'
  },
  {
    from: 'por',
    to: 'pt'
  },
  {
    from: 'que',
    to: 'qu'
  },
  {
    from: 'roh',
    to: 'rm'
  },
  {
    from: 'run',
    to: 'rn'
  },
  {
    from: 'ron',
    to: 'ro'
  },
  {
    from: 'rus',
    to: 'ru'
  },
  {
    from: 'kin',
    to: 'rw'
  },
  {
    from: 'san',
    to: 'sa'
  },
  {
    from: 'srd',
    to: 'sc'
  },
  {
    from: 'snd',
    to: 'sd'
  },
  {
    from: 'sme',
    to: 'se'
  },
  {
    from: 'sag',
    to: 'sg'
  },
  {
    from: 'hbs',
    to: 'sr-latn'
  },
  {
    from: 'sin',
    to: 'si'
  },
  {
    from: 'slk',
    to: 'sk'
  },
  {
    from: 'slv',
    to: 'sl'
  },
  {
    from: 'smo',
    to: 'sm'
  },
  {
    from: 'sna',
    to: 'sn'
  },
  {
    from: 'som',
    to: 'so'
  },
  {
    from: 'sqi',
    to: 'sq'
  },
  {
    from: 'srp',
    to: 'sr'
  },
  {
    from: 'ssw',
    to: 'ss'
  },
  {
    from: 'sot',
    to: 'st'
  },
  {
    from: 'sun',
    to: 'su'
  },
  {
    from: 'swe',
    to: 'sv'
  },
  {
    from: 'swa',
    to: 'sw'
  },
  {
    from: 'tam',
    to: 'ta'
  },
  {
    from: 'tel',
    to: 'te'
  },
  {
    from: 'tgk',
    to: 'tg'
  },
  {
    from: 'tha',
    to: 'th'
  },
  {
    from: 'tir',
    to: 'ti'
  },
  {
    from: 'tuk',
    to: 'tk'
  },
  {
    from: 'tgl',
    to: 'fil'
  },
  {
    from: 'tsn',
    to: 'tn'
  },
  {
    from: 'ton',
    to: 'to'
  },
  {
    from: 'tur',
    to: 'tr'
  },
  {
    from: 'tso',
    to: 'ts'
  },
  {
    from: 'tat',
    to: 'tt'
  },
  {
    from: 'twi',
    to: 'ak'
  },
  {
    from: 'tah',
    to: 'ty'
  },
  {
    from: 'uig',
    to: 'ug'
  },
  {
    from: 'ukr',
    to: 'uk'
  },
  {
    from: 'urd',
    to: 'ur'
  },
  {
    from: 'uzb',
    to: 'uz'
  },
  {
    from: 'ven',
    to: 've'
  },
  {
    from: 'vie',
    to: 'vi'
  },
  {
    from: 'vol',
    to: 'vo'
  },
  {
    from: 'wln',
    to: 'wa'
  },
  {
    from: 'wol',
    to: 'wo'
  },
  {
    from: 'xho',
    to: 'xh'
  },
  {
    from: 'yid',
    to: 'yi'
  },
  {
    from: 'yor',
    to: 'yo'
  },
  {
    from: 'zha',
    to: 'za'
  },
  {
    from: 'zho',
    to: 'zh'
  },
  {
    from: 'zul',
    to: 'zu'
  },
  {
    from: 'alb',
    to: 'sq'
  },
  {
    from: 'arm',
    to: 'hy'
  },
  {
    from: 'baq',
    to: 'eu'
  },
  {
    from: 'bur',
    to: 'my'
  },
  {
    from: 'chi',
    to: 'zh'
  },
  {
    from: 'cze',
    to: 'cs'
  },
  {
    from: 'dut',
    to: 'nl'
  },
  {
    from: 'fre',
    to: 'fr'
  },
  {
    from: 'geo',
    to: 'ka'
  },
  {
    from: 'ger',
    to: 'de'
  },
  {
    from: 'gre',
    to: 'el'
  },
  {
    from: 'ice',
    to: 'is'
  },
  {
    from: 'mac',
    to: 'mk'
  },
  {
    from: 'mao',
    to: 'mi'
  },
  {
    from: 'may',
    to: 'ms'
  },
  {
    from: 'per',
    to: 'fa'
  },
  {
    from: 'rum',
    to: 'ro'
  },
  {
    from: 'slo',
    to: 'sk'
  },
  {
    from: 'tib',
    to: 'bo'
  },
  {
    from: 'wel',
    to: 'cy'
  },
  {
    from: 'und-aaland',
    to: 'und-ax'
  },
  {
    from: 'hy-arevmda',
    to: 'hyw'
  },
  {
    from: 'und-arevmda',
    to: 'und'
  },
  {
    from: 'und-arevela',
    to: 'und'
  },
  {
    from: 'und-lojban',
    to: 'und'
  },
  {
    from: 'und-saaho',
    to: 'und'
  },
  {
    from: 'und-bokmal',
    to: 'und'
  },
  {
    from: 'und-nynorsk',
    to: 'und'
  },
  {
    from: 'und-hakka',
    to: 'und'
  },
  {
    from: 'und-xiang',
    to: 'und'
  },
  {
    from: 'und-hepburn-heploc',
    to: 'und-alalc97'
  }
]
