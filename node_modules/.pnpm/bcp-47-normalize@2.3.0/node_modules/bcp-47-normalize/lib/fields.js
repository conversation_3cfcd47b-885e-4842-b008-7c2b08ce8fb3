/**
 * @typedef {'script'|'region'|'variants'} Field
 *
 * @typedef AddOrRemove
 * @property {Field} field
 * @property {string} value
 *
 * @typedef Change
 * @property {AddOrRemove} from
 * @property {AddOrRemove} to
 */

/**
 * @type {Array<Change>}
 */
export const fields = [
  {
    from: {
      field: 'script',
      value: 'qaai'
    },
    to: {
      field: 'script',
      value: 'zinh'
    }
  },
  {
    from: {
      field: 'region',
      value: 'bu'
    },
    to: {
      field: 'region',
      value: 'mm'
    }
  },
  {
    from: {
      field: 'region',
      value: 'ct'
    },
    to: {
      field: 'region',
      value: 'ki'
    }
  },
  {
    from: {
      field: 'region',
      value: 'dd'
    },
    to: {
      field: 'region',
      value: 'de'
    }
  },
  {
    from: {
      field: 'region',
      value: 'dy'
    },
    to: {
      field: 'region',
      value: 'bj'
    }
  },
  {
    from: {
      field: 'region',
      value: 'fx'
    },
    to: {
      field: 'region',
      value: 'fr'
    }
  },
  {
    from: {
      field: 'region',
      value: 'hv'
    },
    to: {
      field: 'region',
      value: 'bf'
    }
  },
  {
    from: {
      field: 'region',
      value: 'jt'
    },
    to: {
      field: 'region',
      value: 'um'
    }
  },
  {
    from: {
      field: 'region',
      value: 'mi'
    },
    to: {
      field: 'region',
      value: 'um'
    }
  },
  {
    from: {
      field: 'region',
      value: 'nh'
    },
    to: {
      field: 'region',
      value: 'vu'
    }
  },
  {
    from: {
      field: 'region',
      value: 'nq'
    },
    to: {
      field: 'region',
      value: 'aq'
    }
  },
  {
    from: {
      field: 'region',
      value: 'pu'
    },
    to: {
      field: 'region',
      value: 'um'
    }
  },
  {
    from: {
      field: 'region',
      value: 'pz'
    },
    to: {
      field: 'region',
      value: 'pa'
    }
  },
  {
    from: {
      field: 'region',
      value: 'qu'
    },
    to: {
      field: 'region',
      value: 'eu'
    }
  },
  {
    from: {
      field: 'region',
      value: 'rh'
    },
    to: {
      field: 'region',
      value: 'zw'
    }
  },
  {
    from: {
      field: 'region',
      value: 'tp'
    },
    to: {
      field: 'region',
      value: 'tl'
    }
  },
  {
    from: {
      field: 'region',
      value: 'uk'
    },
    to: {
      field: 'region',
      value: 'gb'
    }
  },
  {
    from: {
      field: 'region',
      value: 'vd'
    },
    to: {
      field: 'region',
      value: 'vn'
    }
  },
  {
    from: {
      field: 'region',
      value: 'wk'
    },
    to: {
      field: 'region',
      value: 'um'
    }
  },
  {
    from: {
      field: 'region',
      value: 'yd'
    },
    to: {
      field: 'region',
      value: 'ye'
    }
  },
  {
    from: {
      field: 'region',
      value: 'zr'
    },
    to: {
      field: 'region',
      value: 'cd'
    }
  },
  {
    from: {
      field: 'region',
      value: '230'
    },
    to: {
      field: 'region',
      value: 'et'
    }
  },
  {
    from: {
      field: 'region',
      value: '280'
    },
    to: {
      field: 'region',
      value: 'de'
    }
  },
  {
    from: {
      field: 'region',
      value: '736'
    },
    to: {
      field: 'region',
      value: 'sd'
    }
  },
  {
    from: {
      field: 'region',
      value: '886'
    },
    to: {
      field: 'region',
      value: 'ye'
    }
  },
  {
    from: {
      field: 'region',
      value: '958'
    },
    to: {
      field: 'region',
      value: 'aa'
    }
  },
  {
    from: {
      field: 'region',
      value: '020'
    },
    to: {
      field: 'region',
      value: 'ad'
    }
  },
  {
    from: {
      field: 'region',
      value: '784'
    },
    to: {
      field: 'region',
      value: 'ae'
    }
  },
  {
    from: {
      field: 'region',
      value: '004'
    },
    to: {
      field: 'region',
      value: 'af'
    }
  },
  {
    from: {
      field: 'region',
      value: '028'
    },
    to: {
      field: 'region',
      value: 'ag'
    }
  },
  {
    from: {
      field: 'region',
      value: '660'
    },
    to: {
      field: 'region',
      value: 'ai'
    }
  },
  {
    from: {
      field: 'region',
      value: '008'
    },
    to: {
      field: 'region',
      value: 'al'
    }
  },
  {
    from: {
      field: 'region',
      value: '051'
    },
    to: {
      field: 'region',
      value: 'am'
    }
  },
  {
    from: {
      field: 'region',
      value: '024'
    },
    to: {
      field: 'region',
      value: 'ao'
    }
  },
  {
    from: {
      field: 'region',
      value: '010'
    },
    to: {
      field: 'region',
      value: 'aq'
    }
  },
  {
    from: {
      field: 'region',
      value: '032'
    },
    to: {
      field: 'region',
      value: 'ar'
    }
  },
  {
    from: {
      field: 'region',
      value: '016'
    },
    to: {
      field: 'region',
      value: 'as'
    }
  },
  {
    from: {
      field: 'region',
      value: '040'
    },
    to: {
      field: 'region',
      value: 'at'
    }
  },
  {
    from: {
      field: 'region',
      value: '036'
    },
    to: {
      field: 'region',
      value: 'au'
    }
  },
  {
    from: {
      field: 'region',
      value: '533'
    },
    to: {
      field: 'region',
      value: 'aw'
    }
  },
  {
    from: {
      field: 'region',
      value: '248'
    },
    to: {
      field: 'region',
      value: 'ax'
    }
  },
  {
    from: {
      field: 'region',
      value: '031'
    },
    to: {
      field: 'region',
      value: 'az'
    }
  },
  {
    from: {
      field: 'region',
      value: '070'
    },
    to: {
      field: 'region',
      value: 'ba'
    }
  },
  {
    from: {
      field: 'region',
      value: '052'
    },
    to: {
      field: 'region',
      value: 'bb'
    }
  },
  {
    from: {
      field: 'region',
      value: '050'
    },
    to: {
      field: 'region',
      value: 'bd'
    }
  },
  {
    from: {
      field: 'region',
      value: '056'
    },
    to: {
      field: 'region',
      value: 'be'
    }
  },
  {
    from: {
      field: 'region',
      value: '854'
    },
    to: {
      field: 'region',
      value: 'bf'
    }
  },
  {
    from: {
      field: 'region',
      value: '100'
    },
    to: {
      field: 'region',
      value: 'bg'
    }
  },
  {
    from: {
      field: 'region',
      value: '048'
    },
    to: {
      field: 'region',
      value: 'bh'
    }
  },
  {
    from: {
      field: 'region',
      value: '108'
    },
    to: {
      field: 'region',
      value: 'bi'
    }
  },
  {
    from: {
      field: 'region',
      value: '204'
    },
    to: {
      field: 'region',
      value: 'bj'
    }
  },
  {
    from: {
      field: 'region',
      value: '652'
    },
    to: {
      field: 'region',
      value: 'bl'
    }
  },
  {
    from: {
      field: 'region',
      value: '060'
    },
    to: {
      field: 'region',
      value: 'bm'
    }
  },
  {
    from: {
      field: 'region',
      value: '096'
    },
    to: {
      field: 'region',
      value: 'bn'
    }
  },
  {
    from: {
      field: 'region',
      value: '068'
    },
    to: {
      field: 'region',
      value: 'bo'
    }
  },
  {
    from: {
      field: 'region',
      value: '535'
    },
    to: {
      field: 'region',
      value: 'bq'
    }
  },
  {
    from: {
      field: 'region',
      value: '076'
    },
    to: {
      field: 'region',
      value: 'br'
    }
  },
  {
    from: {
      field: 'region',
      value: '044'
    },
    to: {
      field: 'region',
      value: 'bs'
    }
  },
  {
    from: {
      field: 'region',
      value: '064'
    },
    to: {
      field: 'region',
      value: 'bt'
    }
  },
  {
    from: {
      field: 'region',
      value: '104'
    },
    to: {
      field: 'region',
      value: 'mm'
    }
  },
  {
    from: {
      field: 'region',
      value: '074'
    },
    to: {
      field: 'region',
      value: 'bv'
    }
  },
  {
    from: {
      field: 'region',
      value: '072'
    },
    to: {
      field: 'region',
      value: 'bw'
    }
  },
  {
    from: {
      field: 'region',
      value: '112'
    },
    to: {
      field: 'region',
      value: 'by'
    }
  },
  {
    from: {
      field: 'region',
      value: '084'
    },
    to: {
      field: 'region',
      value: 'bz'
    }
  },
  {
    from: {
      field: 'region',
      value: '124'
    },
    to: {
      field: 'region',
      value: 'ca'
    }
  },
  {
    from: {
      field: 'region',
      value: '166'
    },
    to: {
      field: 'region',
      value: 'cc'
    }
  },
  {
    from: {
      field: 'region',
      value: '180'
    },
    to: {
      field: 'region',
      value: 'cd'
    }
  },
  {
    from: {
      field: 'region',
      value: '140'
    },
    to: {
      field: 'region',
      value: 'cf'
    }
  },
  {
    from: {
      field: 'region',
      value: '178'
    },
    to: {
      field: 'region',
      value: 'cg'
    }
  },
  {
    from: {
      field: 'region',
      value: '756'
    },
    to: {
      field: 'region',
      value: 'ch'
    }
  },
  {
    from: {
      field: 'region',
      value: '384'
    },
    to: {
      field: 'region',
      value: 'ci'
    }
  },
  {
    from: {
      field: 'region',
      value: '184'
    },
    to: {
      field: 'region',
      value: 'ck'
    }
  },
  {
    from: {
      field: 'region',
      value: '152'
    },
    to: {
      field: 'region',
      value: 'cl'
    }
  },
  {
    from: {
      field: 'region',
      value: '120'
    },
    to: {
      field: 'region',
      value: 'cm'
    }
  },
  {
    from: {
      field: 'region',
      value: '156'
    },
    to: {
      field: 'region',
      value: 'cn'
    }
  },
  {
    from: {
      field: 'region',
      value: '170'
    },
    to: {
      field: 'region',
      value: 'co'
    }
  },
  {
    from: {
      field: 'region',
      value: '188'
    },
    to: {
      field: 'region',
      value: 'cr'
    }
  },
  {
    from: {
      field: 'region',
      value: '192'
    },
    to: {
      field: 'region',
      value: 'cu'
    }
  },
  {
    from: {
      field: 'region',
      value: '132'
    },
    to: {
      field: 'region',
      value: 'cv'
    }
  },
  {
    from: {
      field: 'region',
      value: '531'
    },
    to: {
      field: 'region',
      value: 'cw'
    }
  },
  {
    from: {
      field: 'region',
      value: '162'
    },
    to: {
      field: 'region',
      value: 'cx'
    }
  },
  {
    from: {
      field: 'region',
      value: '196'
    },
    to: {
      field: 'region',
      value: 'cy'
    }
  },
  {
    from: {
      field: 'region',
      value: '203'
    },
    to: {
      field: 'region',
      value: 'cz'
    }
  },
  {
    from: {
      field: 'region',
      value: '278'
    },
    to: {
      field: 'region',
      value: 'de'
    }
  },
  {
    from: {
      field: 'region',
      value: '276'
    },
    to: {
      field: 'region',
      value: 'de'
    }
  },
  {
    from: {
      field: 'region',
      value: '262'
    },
    to: {
      field: 'region',
      value: 'dj'
    }
  },
  {
    from: {
      field: 'region',
      value: '208'
    },
    to: {
      field: 'region',
      value: 'dk'
    }
  },
  {
    from: {
      field: 'region',
      value: '212'
    },
    to: {
      field: 'region',
      value: 'dm'
    }
  },
  {
    from: {
      field: 'region',
      value: '214'
    },
    to: {
      field: 'region',
      value: 'do'
    }
  },
  {
    from: {
      field: 'region',
      value: '012'
    },
    to: {
      field: 'region',
      value: 'dz'
    }
  },
  {
    from: {
      field: 'region',
      value: '218'
    },
    to: {
      field: 'region',
      value: 'ec'
    }
  },
  {
    from: {
      field: 'region',
      value: '233'
    },
    to: {
      field: 'region',
      value: 'ee'
    }
  },
  {
    from: {
      field: 'region',
      value: '818'
    },
    to: {
      field: 'region',
      value: 'eg'
    }
  },
  {
    from: {
      field: 'region',
      value: '732'
    },
    to: {
      field: 'region',
      value: 'eh'
    }
  },
  {
    from: {
      field: 'region',
      value: '232'
    },
    to: {
      field: 'region',
      value: 'er'
    }
  },
  {
    from: {
      field: 'region',
      value: '724'
    },
    to: {
      field: 'region',
      value: 'es'
    }
  },
  {
    from: {
      field: 'region',
      value: '231'
    },
    to: {
      field: 'region',
      value: 'et'
    }
  },
  {
    from: {
      field: 'region',
      value: '246'
    },
    to: {
      field: 'region',
      value: 'fi'
    }
  },
  {
    from: {
      field: 'region',
      value: '242'
    },
    to: {
      field: 'region',
      value: 'fj'
    }
  },
  {
    from: {
      field: 'region',
      value: '238'
    },
    to: {
      field: 'region',
      value: 'fk'
    }
  },
  {
    from: {
      field: 'region',
      value: '583'
    },
    to: {
      field: 'region',
      value: 'fm'
    }
  },
  {
    from: {
      field: 'region',
      value: '234'
    },
    to: {
      field: 'region',
      value: 'fo'
    }
  },
  {
    from: {
      field: 'region',
      value: '250'
    },
    to: {
      field: 'region',
      value: 'fr'
    }
  },
  {
    from: {
      field: 'region',
      value: '249'
    },
    to: {
      field: 'region',
      value: 'fr'
    }
  },
  {
    from: {
      field: 'region',
      value: '266'
    },
    to: {
      field: 'region',
      value: 'ga'
    }
  },
  {
    from: {
      field: 'region',
      value: '826'
    },
    to: {
      field: 'region',
      value: 'gb'
    }
  },
  {
    from: {
      field: 'region',
      value: '308'
    },
    to: {
      field: 'region',
      value: 'gd'
    }
  },
  {
    from: {
      field: 'region',
      value: '268'
    },
    to: {
      field: 'region',
      value: 'ge'
    }
  },
  {
    from: {
      field: 'region',
      value: '254'
    },
    to: {
      field: 'region',
      value: 'gf'
    }
  },
  {
    from: {
      field: 'region',
      value: '831'
    },
    to: {
      field: 'region',
      value: 'gg'
    }
  },
  {
    from: {
      field: 'region',
      value: '288'
    },
    to: {
      field: 'region',
      value: 'gh'
    }
  },
  {
    from: {
      field: 'region',
      value: '292'
    },
    to: {
      field: 'region',
      value: 'gi'
    }
  },
  {
    from: {
      field: 'region',
      value: '304'
    },
    to: {
      field: 'region',
      value: 'gl'
    }
  },
  {
    from: {
      field: 'region',
      value: '270'
    },
    to: {
      field: 'region',
      value: 'gm'
    }
  },
  {
    from: {
      field: 'region',
      value: '324'
    },
    to: {
      field: 'region',
      value: 'gn'
    }
  },
  {
    from: {
      field: 'region',
      value: '312'
    },
    to: {
      field: 'region',
      value: 'gp'
    }
  },
  {
    from: {
      field: 'region',
      value: '226'
    },
    to: {
      field: 'region',
      value: 'gq'
    }
  },
  {
    from: {
      field: 'region',
      value: '300'
    },
    to: {
      field: 'region',
      value: 'gr'
    }
  },
  {
    from: {
      field: 'region',
      value: '239'
    },
    to: {
      field: 'region',
      value: 'gs'
    }
  },
  {
    from: {
      field: 'region',
      value: '320'
    },
    to: {
      field: 'region',
      value: 'gt'
    }
  },
  {
    from: {
      field: 'region',
      value: '316'
    },
    to: {
      field: 'region',
      value: 'gu'
    }
  },
  {
    from: {
      field: 'region',
      value: '624'
    },
    to: {
      field: 'region',
      value: 'gw'
    }
  },
  {
    from: {
      field: 'region',
      value: '328'
    },
    to: {
      field: 'region',
      value: 'gy'
    }
  },
  {
    from: {
      field: 'region',
      value: '344'
    },
    to: {
      field: 'region',
      value: 'hk'
    }
  },
  {
    from: {
      field: 'region',
      value: '334'
    },
    to: {
      field: 'region',
      value: 'hm'
    }
  },
  {
    from: {
      field: 'region',
      value: '340'
    },
    to: {
      field: 'region',
      value: 'hn'
    }
  },
  {
    from: {
      field: 'region',
      value: '191'
    },
    to: {
      field: 'region',
      value: 'hr'
    }
  },
  {
    from: {
      field: 'region',
      value: '332'
    },
    to: {
      field: 'region',
      value: 'ht'
    }
  },
  {
    from: {
      field: 'region',
      value: '348'
    },
    to: {
      field: 'region',
      value: 'hu'
    }
  },
  {
    from: {
      field: 'region',
      value: '360'
    },
    to: {
      field: 'region',
      value: 'id'
    }
  },
  {
    from: {
      field: 'region',
      value: '372'
    },
    to: {
      field: 'region',
      value: 'ie'
    }
  },
  {
    from: {
      field: 'region',
      value: '376'
    },
    to: {
      field: 'region',
      value: 'il'
    }
  },
  {
    from: {
      field: 'region',
      value: '833'
    },
    to: {
      field: 'region',
      value: 'im'
    }
  },
  {
    from: {
      field: 'region',
      value: '356'
    },
    to: {
      field: 'region',
      value: 'in'
    }
  },
  {
    from: {
      field: 'region',
      value: '086'
    },
    to: {
      field: 'region',
      value: 'io'
    }
  },
  {
    from: {
      field: 'region',
      value: '368'
    },
    to: {
      field: 'region',
      value: 'iq'
    }
  },
  {
    from: {
      field: 'region',
      value: '364'
    },
    to: {
      field: 'region',
      value: 'ir'
    }
  },
  {
    from: {
      field: 'region',
      value: '352'
    },
    to: {
      field: 'region',
      value: 'is'
    }
  },
  {
    from: {
      field: 'region',
      value: '380'
    },
    to: {
      field: 'region',
      value: 'it'
    }
  },
  {
    from: {
      field: 'region',
      value: '832'
    },
    to: {
      field: 'region',
      value: 'je'
    }
  },
  {
    from: {
      field: 'region',
      value: '388'
    },
    to: {
      field: 'region',
      value: 'jm'
    }
  },
  {
    from: {
      field: 'region',
      value: '400'
    },
    to: {
      field: 'region',
      value: 'jo'
    }
  },
  {
    from: {
      field: 'region',
      value: '392'
    },
    to: {
      field: 'region',
      value: 'jp'
    }
  },
  {
    from: {
      field: 'region',
      value: '404'
    },
    to: {
      field: 'region',
      value: 'ke'
    }
  },
  {
    from: {
      field: 'region',
      value: '417'
    },
    to: {
      field: 'region',
      value: 'kg'
    }
  },
  {
    from: {
      field: 'region',
      value: '116'
    },
    to: {
      field: 'region',
      value: 'kh'
    }
  },
  {
    from: {
      field: 'region',
      value: '296'
    },
    to: {
      field: 'region',
      value: 'ki'
    }
  },
  {
    from: {
      field: 'region',
      value: '174'
    },
    to: {
      field: 'region',
      value: 'km'
    }
  },
  {
    from: {
      field: 'region',
      value: '659'
    },
    to: {
      field: 'region',
      value: 'kn'
    }
  },
  {
    from: {
      field: 'region',
      value: '408'
    },
    to: {
      field: 'region',
      value: 'kp'
    }
  },
  {
    from: {
      field: 'region',
      value: '410'
    },
    to: {
      field: 'region',
      value: 'kr'
    }
  },
  {
    from: {
      field: 'region',
      value: '414'
    },
    to: {
      field: 'region',
      value: 'kw'
    }
  },
  {
    from: {
      field: 'region',
      value: '136'
    },
    to: {
      field: 'region',
      value: 'ky'
    }
  },
  {
    from: {
      field: 'region',
      value: '398'
    },
    to: {
      field: 'region',
      value: 'kz'
    }
  },
  {
    from: {
      field: 'region',
      value: '418'
    },
    to: {
      field: 'region',
      value: 'la'
    }
  },
  {
    from: {
      field: 'region',
      value: '422'
    },
    to: {
      field: 'region',
      value: 'lb'
    }
  },
  {
    from: {
      field: 'region',
      value: '662'
    },
    to: {
      field: 'region',
      value: 'lc'
    }
  },
  {
    from: {
      field: 'region',
      value: '438'
    },
    to: {
      field: 'region',
      value: 'li'
    }
  },
  {
    from: {
      field: 'region',
      value: '144'
    },
    to: {
      field: 'region',
      value: 'lk'
    }
  },
  {
    from: {
      field: 'region',
      value: '430'
    },
    to: {
      field: 'region',
      value: 'lr'
    }
  },
  {
    from: {
      field: 'region',
      value: '426'
    },
    to: {
      field: 'region',
      value: 'ls'
    }
  },
  {
    from: {
      field: 'region',
      value: '440'
    },
    to: {
      field: 'region',
      value: 'lt'
    }
  },
  {
    from: {
      field: 'region',
      value: '442'
    },
    to: {
      field: 'region',
      value: 'lu'
    }
  },
  {
    from: {
      field: 'region',
      value: '428'
    },
    to: {
      field: 'region',
      value: 'lv'
    }
  },
  {
    from: {
      field: 'region',
      value: '434'
    },
    to: {
      field: 'region',
      value: 'ly'
    }
  },
  {
    from: {
      field: 'region',
      value: '504'
    },
    to: {
      field: 'region',
      value: 'ma'
    }
  },
  {
    from: {
      field: 'region',
      value: '492'
    },
    to: {
      field: 'region',
      value: 'mc'
    }
  },
  {
    from: {
      field: 'region',
      value: '498'
    },
    to: {
      field: 'region',
      value: 'md'
    }
  },
  {
    from: {
      field: 'region',
      value: '499'
    },
    to: {
      field: 'region',
      value: 'me'
    }
  },
  {
    from: {
      field: 'region',
      value: '663'
    },
    to: {
      field: 'region',
      value: 'mf'
    }
  },
  {
    from: {
      field: 'region',
      value: '450'
    },
    to: {
      field: 'region',
      value: 'mg'
    }
  },
  {
    from: {
      field: 'region',
      value: '584'
    },
    to: {
      field: 'region',
      value: 'mh'
    }
  },
  {
    from: {
      field: 'region',
      value: '807'
    },
    to: {
      field: 'region',
      value: 'mk'
    }
  },
  {
    from: {
      field: 'region',
      value: '466'
    },
    to: {
      field: 'region',
      value: 'ml'
    }
  },
  {
    from: {
      field: 'region',
      value: '496'
    },
    to: {
      field: 'region',
      value: 'mn'
    }
  },
  {
    from: {
      field: 'region',
      value: '446'
    },
    to: {
      field: 'region',
      value: 'mo'
    }
  },
  {
    from: {
      field: 'region',
      value: '580'
    },
    to: {
      field: 'region',
      value: 'mp'
    }
  },
  {
    from: {
      field: 'region',
      value: '474'
    },
    to: {
      field: 'region',
      value: 'mq'
    }
  },
  {
    from: {
      field: 'region',
      value: '478'
    },
    to: {
      field: 'region',
      value: 'mr'
    }
  },
  {
    from: {
      field: 'region',
      value: '500'
    },
    to: {
      field: 'region',
      value: 'ms'
    }
  },
  {
    from: {
      field: 'region',
      value: '470'
    },
    to: {
      field: 'region',
      value: 'mt'
    }
  },
  {
    from: {
      field: 'region',
      value: '480'
    },
    to: {
      field: 'region',
      value: 'mu'
    }
  },
  {
    from: {
      field: 'region',
      value: '462'
    },
    to: {
      field: 'region',
      value: 'mv'
    }
  },
  {
    from: {
      field: 'region',
      value: '454'
    },
    to: {
      field: 'region',
      value: 'mw'
    }
  },
  {
    from: {
      field: 'region',
      value: '484'
    },
    to: {
      field: 'region',
      value: 'mx'
    }
  },
  {
    from: {
      field: 'region',
      value: '458'
    },
    to: {
      field: 'region',
      value: 'my'
    }
  },
  {
    from: {
      field: 'region',
      value: '508'
    },
    to: {
      field: 'region',
      value: 'mz'
    }
  },
  {
    from: {
      field: 'region',
      value: '516'
    },
    to: {
      field: 'region',
      value: 'na'
    }
  },
  {
    from: {
      field: 'region',
      value: '540'
    },
    to: {
      field: 'region',
      value: 'nc'
    }
  },
  {
    from: {
      field: 'region',
      value: '562'
    },
    to: {
      field: 'region',
      value: 'ne'
    }
  },
  {
    from: {
      field: 'region',
      value: '574'
    },
    to: {
      field: 'region',
      value: 'nf'
    }
  },
  {
    from: {
      field: 'region',
      value: '566'
    },
    to: {
      field: 'region',
      value: 'ng'
    }
  },
  {
    from: {
      field: 'region',
      value: '558'
    },
    to: {
      field: 'region',
      value: 'ni'
    }
  },
  {
    from: {
      field: 'region',
      value: '528'
    },
    to: {
      field: 'region',
      value: 'nl'
    }
  },
  {
    from: {
      field: 'region',
      value: '578'
    },
    to: {
      field: 'region',
      value: 'no'
    }
  },
  {
    from: {
      field: 'region',
      value: '524'
    },
    to: {
      field: 'region',
      value: 'np'
    }
  },
  {
    from: {
      field: 'region',
      value: '520'
    },
    to: {
      field: 'region',
      value: 'nr'
    }
  },
  {
    from: {
      field: 'region',
      value: '570'
    },
    to: {
      field: 'region',
      value: 'nu'
    }
  },
  {
    from: {
      field: 'region',
      value: '554'
    },
    to: {
      field: 'region',
      value: 'nz'
    }
  },
  {
    from: {
      field: 'region',
      value: '512'
    },
    to: {
      field: 'region',
      value: 'om'
    }
  },
  {
    from: {
      field: 'region',
      value: '591'
    },
    to: {
      field: 'region',
      value: 'pa'
    }
  },
  {
    from: {
      field: 'region',
      value: '604'
    },
    to: {
      field: 'region',
      value: 'pe'
    }
  },
  {
    from: {
      field: 'region',
      value: '258'
    },
    to: {
      field: 'region',
      value: 'pf'
    }
  },
  {
    from: {
      field: 'region',
      value: '598'
    },
    to: {
      field: 'region',
      value: 'pg'
    }
  },
  {
    from: {
      field: 'region',
      value: '608'
    },
    to: {
      field: 'region',
      value: 'ph'
    }
  },
  {
    from: {
      field: 'region',
      value: '586'
    },
    to: {
      field: 'region',
      value: 'pk'
    }
  },
  {
    from: {
      field: 'region',
      value: '616'
    },
    to: {
      field: 'region',
      value: 'pl'
    }
  },
  {
    from: {
      field: 'region',
      value: '666'
    },
    to: {
      field: 'region',
      value: 'pm'
    }
  },
  {
    from: {
      field: 'region',
      value: '612'
    },
    to: {
      field: 'region',
      value: 'pn'
    }
  },
  {
    from: {
      field: 'region',
      value: '630'
    },
    to: {
      field: 'region',
      value: 'pr'
    }
  },
  {
    from: {
      field: 'region',
      value: '275'
    },
    to: {
      field: 'region',
      value: 'ps'
    }
  },
  {
    from: {
      field: 'region',
      value: '620'
    },
    to: {
      field: 'region',
      value: 'pt'
    }
  },
  {
    from: {
      field: 'region',
      value: '585'
    },
    to: {
      field: 'region',
      value: 'pw'
    }
  },
  {
    from: {
      field: 'region',
      value: '600'
    },
    to: {
      field: 'region',
      value: 'py'
    }
  },
  {
    from: {
      field: 'region',
      value: '634'
    },
    to: {
      field: 'region',
      value: 'qa'
    }
  },
  {
    from: {
      field: 'region',
      value: '959'
    },
    to: {
      field: 'region',
      value: 'qm'
    }
  },
  {
    from: {
      field: 'region',
      value: '960'
    },
    to: {
      field: 'region',
      value: 'qn'
    }
  },
  {
    from: {
      field: 'region',
      value: '962'
    },
    to: {
      field: 'region',
      value: 'qp'
    }
  },
  {
    from: {
      field: 'region',
      value: '963'
    },
    to: {
      field: 'region',
      value: 'qq'
    }
  },
  {
    from: {
      field: 'region',
      value: '964'
    },
    to: {
      field: 'region',
      value: 'qr'
    }
  },
  {
    from: {
      field: 'region',
      value: '965'
    },
    to: {
      field: 'region',
      value: 'qs'
    }
  },
  {
    from: {
      field: 'region',
      value: '966'
    },
    to: {
      field: 'region',
      value: 'qt'
    }
  },
  {
    from: {
      field: 'region',
      value: '967'
    },
    to: {
      field: 'region',
      value: 'eu'
    }
  },
  {
    from: {
      field: 'region',
      value: '968'
    },
    to: {
      field: 'region',
      value: 'qv'
    }
  },
  {
    from: {
      field: 'region',
      value: '969'
    },
    to: {
      field: 'region',
      value: 'qw'
    }
  },
  {
    from: {
      field: 'region',
      value: '970'
    },
    to: {
      field: 'region',
      value: 'qx'
    }
  },
  {
    from: {
      field: 'region',
      value: '971'
    },
    to: {
      field: 'region',
      value: 'qy'
    }
  },
  {
    from: {
      field: 'region',
      value: '972'
    },
    to: {
      field: 'region',
      value: 'qz'
    }
  },
  {
    from: {
      field: 'region',
      value: '638'
    },
    to: {
      field: 'region',
      value: 're'
    }
  },
  {
    from: {
      field: 'region',
      value: '642'
    },
    to: {
      field: 'region',
      value: 'ro'
    }
  },
  {
    from: {
      field: 'region',
      value: '688'
    },
    to: {
      field: 'region',
      value: 'rs'
    }
  },
  {
    from: {
      field: 'region',
      value: '643'
    },
    to: {
      field: 'region',
      value: 'ru'
    }
  },
  {
    from: {
      field: 'region',
      value: '646'
    },
    to: {
      field: 'region',
      value: 'rw'
    }
  },
  {
    from: {
      field: 'region',
      value: '682'
    },
    to: {
      field: 'region',
      value: 'sa'
    }
  },
  {
    from: {
      field: 'region',
      value: '090'
    },
    to: {
      field: 'region',
      value: 'sb'
    }
  },
  {
    from: {
      field: 'region',
      value: '690'
    },
    to: {
      field: 'region',
      value: 'sc'
    }
  },
  {
    from: {
      field: 'region',
      value: '729'
    },
    to: {
      field: 'region',
      value: 'sd'
    }
  },
  {
    from: {
      field: 'region',
      value: '752'
    },
    to: {
      field: 'region',
      value: 'se'
    }
  },
  {
    from: {
      field: 'region',
      value: '702'
    },
    to: {
      field: 'region',
      value: 'sg'
    }
  },
  {
    from: {
      field: 'region',
      value: '654'
    },
    to: {
      field: 'region',
      value: 'sh'
    }
  },
  {
    from: {
      field: 'region',
      value: '705'
    },
    to: {
      field: 'region',
      value: 'si'
    }
  },
  {
    from: {
      field: 'region',
      value: '744'
    },
    to: {
      field: 'region',
      value: 'sj'
    }
  },
  {
    from: {
      field: 'region',
      value: '703'
    },
    to: {
      field: 'region',
      value: 'sk'
    }
  },
  {
    from: {
      field: 'region',
      value: '694'
    },
    to: {
      field: 'region',
      value: 'sl'
    }
  },
  {
    from: {
      field: 'region',
      value: '674'
    },
    to: {
      field: 'region',
      value: 'sm'
    }
  },
  {
    from: {
      field: 'region',
      value: '686'
    },
    to: {
      field: 'region',
      value: 'sn'
    }
  },
  {
    from: {
      field: 'region',
      value: '706'
    },
    to: {
      field: 'region',
      value: 'so'
    }
  },
  {
    from: {
      field: 'region',
      value: '740'
    },
    to: {
      field: 'region',
      value: 'sr'
    }
  },
  {
    from: {
      field: 'region',
      value: '728'
    },
    to: {
      field: 'region',
      value: 'ss'
    }
  },
  {
    from: {
      field: 'region',
      value: '678'
    },
    to: {
      field: 'region',
      value: 'st'
    }
  },
  {
    from: {
      field: 'region',
      value: '222'
    },
    to: {
      field: 'region',
      value: 'sv'
    }
  },
  {
    from: {
      field: 'region',
      value: '534'
    },
    to: {
      field: 'region',
      value: 'sx'
    }
  },
  {
    from: {
      field: 'region',
      value: '760'
    },
    to: {
      field: 'region',
      value: 'sy'
    }
  },
  {
    from: {
      field: 'region',
      value: '748'
    },
    to: {
      field: 'region',
      value: 'sz'
    }
  },
  {
    from: {
      field: 'region',
      value: '796'
    },
    to: {
      field: 'region',
      value: 'tc'
    }
  },
  {
    from: {
      field: 'region',
      value: '148'
    },
    to: {
      field: 'region',
      value: 'td'
    }
  },
  {
    from: {
      field: 'region',
      value: '260'
    },
    to: {
      field: 'region',
      value: 'tf'
    }
  },
  {
    from: {
      field: 'region',
      value: '768'
    },
    to: {
      field: 'region',
      value: 'tg'
    }
  },
  {
    from: {
      field: 'region',
      value: '764'
    },
    to: {
      field: 'region',
      value: 'th'
    }
  },
  {
    from: {
      field: 'region',
      value: '762'
    },
    to: {
      field: 'region',
      value: 'tj'
    }
  },
  {
    from: {
      field: 'region',
      value: '772'
    },
    to: {
      field: 'region',
      value: 'tk'
    }
  },
  {
    from: {
      field: 'region',
      value: '626'
    },
    to: {
      field: 'region',
      value: 'tl'
    }
  },
  {
    from: {
      field: 'region',
      value: '795'
    },
    to: {
      field: 'region',
      value: 'tm'
    }
  },
  {
    from: {
      field: 'region',
      value: '788'
    },
    to: {
      field: 'region',
      value: 'tn'
    }
  },
  {
    from: {
      field: 'region',
      value: '776'
    },
    to: {
      field: 'region',
      value: 'to'
    }
  },
  {
    from: {
      field: 'region',
      value: '792'
    },
    to: {
      field: 'region',
      value: 'tr'
    }
  },
  {
    from: {
      field: 'region',
      value: '780'
    },
    to: {
      field: 'region',
      value: 'tt'
    }
  },
  {
    from: {
      field: 'region',
      value: '798'
    },
    to: {
      field: 'region',
      value: 'tv'
    }
  },
  {
    from: {
      field: 'region',
      value: '158'
    },
    to: {
      field: 'region',
      value: 'tw'
    }
  },
  {
    from: {
      field: 'region',
      value: '834'
    },
    to: {
      field: 'region',
      value: 'tz'
    }
  },
  {
    from: {
      field: 'region',
      value: '804'
    },
    to: {
      field: 'region',
      value: 'ua'
    }
  },
  {
    from: {
      field: 'region',
      value: '800'
    },
    to: {
      field: 'region',
      value: 'ug'
    }
  },
  {
    from: {
      field: 'region',
      value: '581'
    },
    to: {
      field: 'region',
      value: 'um'
    }
  },
  {
    from: {
      field: 'region',
      value: '840'
    },
    to: {
      field: 'region',
      value: 'us'
    }
  },
  {
    from: {
      field: 'region',
      value: '858'
    },
    to: {
      field: 'region',
      value: 'uy'
    }
  },
  {
    from: {
      field: 'region',
      value: '860'
    },
    to: {
      field: 'region',
      value: 'uz'
    }
  },
  {
    from: {
      field: 'region',
      value: '336'
    },
    to: {
      field: 'region',
      value: 'va'
    }
  },
  {
    from: {
      field: 'region',
      value: '670'
    },
    to: {
      field: 'region',
      value: 'vc'
    }
  },
  {
    from: {
      field: 'region',
      value: '862'
    },
    to: {
      field: 'region',
      value: 've'
    }
  },
  {
    from: {
      field: 'region',
      value: '092'
    },
    to: {
      field: 'region',
      value: 'vg'
    }
  },
  {
    from: {
      field: 'region',
      value: '850'
    },
    to: {
      field: 'region',
      value: 'vi'
    }
  },
  {
    from: {
      field: 'region',
      value: '704'
    },
    to: {
      field: 'region',
      value: 'vn'
    }
  },
  {
    from: {
      field: 'region',
      value: '548'
    },
    to: {
      field: 'region',
      value: 'vu'
    }
  },
  {
    from: {
      field: 'region',
      value: '876'
    },
    to: {
      field: 'region',
      value: 'wf'
    }
  },
  {
    from: {
      field: 'region',
      value: '882'
    },
    to: {
      field: 'region',
      value: 'ws'
    }
  },
  {
    from: {
      field: 'region',
      value: '973'
    },
    to: {
      field: 'region',
      value: 'xa'
    }
  },
  {
    from: {
      field: 'region',
      value: '974'
    },
    to: {
      field: 'region',
      value: 'xb'
    }
  },
  {
    from: {
      field: 'region',
      value: '975'
    },
    to: {
      field: 'region',
      value: 'xc'
    }
  },
  {
    from: {
      field: 'region',
      value: '976'
    },
    to: {
      field: 'region',
      value: 'xd'
    }
  },
  {
    from: {
      field: 'region',
      value: '977'
    },
    to: {
      field: 'region',
      value: 'xe'
    }
  },
  {
    from: {
      field: 'region',
      value: '978'
    },
    to: {
      field: 'region',
      value: 'xf'
    }
  },
  {
    from: {
      field: 'region',
      value: '979'
    },
    to: {
      field: 'region',
      value: 'xg'
    }
  },
  {
    from: {
      field: 'region',
      value: '980'
    },
    to: {
      field: 'region',
      value: 'xh'
    }
  },
  {
    from: {
      field: 'region',
      value: '981'
    },
    to: {
      field: 'region',
      value: 'xi'
    }
  },
  {
    from: {
      field: 'region',
      value: '982'
    },
    to: {
      field: 'region',
      value: 'xj'
    }
  },
  {
    from: {
      field: 'region',
      value: '983'
    },
    to: {
      field: 'region',
      value: 'xk'
    }
  },
  {
    from: {
      field: 'region',
      value: '984'
    },
    to: {
      field: 'region',
      value: 'xl'
    }
  },
  {
    from: {
      field: 'region',
      value: '985'
    },
    to: {
      field: 'region',
      value: 'xm'
    }
  },
  {
    from: {
      field: 'region',
      value: '986'
    },
    to: {
      field: 'region',
      value: 'xn'
    }
  },
  {
    from: {
      field: 'region',
      value: '987'
    },
    to: {
      field: 'region',
      value: 'xo'
    }
  },
  {
    from: {
      field: 'region',
      value: '988'
    },
    to: {
      field: 'region',
      value: 'xp'
    }
  },
  {
    from: {
      field: 'region',
      value: '989'
    },
    to: {
      field: 'region',
      value: 'xq'
    }
  },
  {
    from: {
      field: 'region',
      value: '990'
    },
    to: {
      field: 'region',
      value: 'xr'
    }
  },
  {
    from: {
      field: 'region',
      value: '991'
    },
    to: {
      field: 'region',
      value: 'xs'
    }
  },
  {
    from: {
      field: 'region',
      value: '992'
    },
    to: {
      field: 'region',
      value: 'xt'
    }
  },
  {
    from: {
      field: 'region',
      value: '993'
    },
    to: {
      field: 'region',
      value: 'xu'
    }
  },
  {
    from: {
      field: 'region',
      value: '994'
    },
    to: {
      field: 'region',
      value: 'xv'
    }
  },
  {
    from: {
      field: 'region',
      value: '995'
    },
    to: {
      field: 'region',
      value: 'xw'
    }
  },
  {
    from: {
      field: 'region',
      value: '996'
    },
    to: {
      field: 'region',
      value: 'xx'
    }
  },
  {
    from: {
      field: 'region',
      value: '997'
    },
    to: {
      field: 'region',
      value: 'xy'
    }
  },
  {
    from: {
      field: 'region',
      value: '998'
    },
    to: {
      field: 'region',
      value: 'xz'
    }
  },
  {
    from: {
      field: 'region',
      value: '720'
    },
    to: {
      field: 'region',
      value: 'ye'
    }
  },
  {
    from: {
      field: 'region',
      value: '887'
    },
    to: {
      field: 'region',
      value: 'ye'
    }
  },
  {
    from: {
      field: 'region',
      value: '175'
    },
    to: {
      field: 'region',
      value: 'yt'
    }
  },
  {
    from: {
      field: 'region',
      value: '710'
    },
    to: {
      field: 'region',
      value: 'za'
    }
  },
  {
    from: {
      field: 'region',
      value: '894'
    },
    to: {
      field: 'region',
      value: 'zm'
    }
  },
  {
    from: {
      field: 'region',
      value: '716'
    },
    to: {
      field: 'region',
      value: 'zw'
    }
  },
  {
    from: {
      field: 'region',
      value: '999'
    },
    to: {
      field: 'region',
      value: 'zz'
    }
  },
  {
    from: {
      field: 'variants',
      value: 'polytoni'
    },
    to: {
      field: 'variants',
      value: 'polyton'
    }
  },
  {
    from: {
      field: 'variants',
      value: 'heploc'
    },
    to: {
      field: 'variants',
      value: 'alalc97'
    }
  }
]
