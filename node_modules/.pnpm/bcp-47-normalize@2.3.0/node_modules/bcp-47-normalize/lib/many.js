/**
 * @typedef {'script'|'region'|'variants'} Field
 */

/**
 * @type {{region: Record<string, Array<string>>}}
 */
export const many = {
  region: {
    172: [
      'ru',
      'am',
      'az',
      'by',
      'ge',
      'kg',
      'kz',
      'md',
      'tj',
      'tm',
      'ua',
      'uz'
    ],
    200: ['cz', 'sk'],
    530: ['cw', 'sx', 'bq'],
    532: ['cw', 'sx', 'bq'],
    536: ['sa', 'iq'],
    582: ['fm', 'mh', 'mp', 'pw'],
    810: [
      'ru',
      'am',
      'az',
      'by',
      'ee',
      'ge',
      'kz',
      'kg',
      'lv',
      'lt',
      'md',
      'tj',
      'tm',
      'ua',
      'uz'
    ],
    830: ['je', 'gg'],
    890: ['rs', 'me', 'si', 'hr', 'mk', 'ba'],
    891: ['rs', 'me'],
    an: ['cw', 'sx', 'bq'],
    cs: ['rs', 'me'],
    fq: ['aq', 'tf'],
    nt: ['sa', 'iq'],
    pc: ['fm', 'mh', 'mp', 'pw'],
    su: [
      'ru',
      'am',
      'az',
      'by',
      'ee',
      'ge',
      'kz',
      'kg',
      'lv',
      'lt',
      'md',
      'tj',
      'tm',
      'ua',
      'uz'
    ],
    yu: ['rs', 'me'],
    '062': ['034', '143'],
    ant: ['cw', 'sx', 'bq'],
    scg: ['rs', 'me'],
    ntz: ['sa', 'iq'],
    sun: [
      'ru',
      'am',
      'az',
      'by',
      'ee',
      'ge',
      'kz',
      'kg',
      'lv',
      'lt',
      'md',
      'tj',
      'tm',
      'ua',
      'uz'
    ],
    yug: ['rs', 'me']
  }
}
