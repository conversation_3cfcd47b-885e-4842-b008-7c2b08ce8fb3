/**
 * @type {Record<string, string>}
 */
export const likely = {
  aa: 'aa-latn-et',
  aaa: 'aaa-latn-ng',
  aab: 'aab-latn-ng',
  aac: 'aac-latn-pg',
  aad: 'aad-latn-pg',
  aae: 'aae-latn-it',
  'aae-grek': 'aae-grek-it',
  aaf: 'aaf-mlym-in',
  'aaf-arab': 'aaf-arab-in',
  aag: 'aag-latn-pg',
  aah: 'aah-latn-pg',
  aai: 'aai-latn-zz',
  aak: 'aak-latn-zz',
  aal: 'aal-latn-cm',
  aan: 'aan-latn-br',
  aao: 'aao-arab-dz',
  aap: 'aap-latn-br',
  aaq: 'aaq-latn-us',
  aas: 'aas-latn-tz',
  aat: 'aat-grek-gr',
  aau: 'aau-latn-zz',
  aaw: 'aaw-latn-pg',
  aax: 'aax-latn-id',
  aaz: 'aaz-latn-id',
  ab: 'ab-cyrl-ge',
  aba: 'aba-latn-ci',
  abb: 'abb-latn-cm',
  abc: 'abc-latn-ph',
  abd: 'abd-latn-ph',
  abe: 'abe-latn-ca',
  abf: 'abf-latn-my',
  abg: 'abg-latn-pg',
  abh: 'abh-arab-tj',
  abi: 'abi-latn-zz',
  abl: 'abl-rjng-id',
  'abl-latn': 'abl-latn-id',
  abm: 'abm-latn-ng',
  abn: 'abn-latn-ng',
  abo: 'abo-latn-ng',
  abp: 'abp-latn-ph',
  abq: 'abq-cyrl-zz',
  abr: 'abr-latn-gh',
  abs: 'abs-latn-id',
  abt: 'abt-latn-zz',
  abu: 'abu-latn-ci',
  abv: 'abv-arab-bh',
  abw: 'abw-latn-pg',
  abx: 'abx-latn-ph',
  aby: 'aby-latn-zz',
  abz: 'abz-latn-id',
  aca: 'aca-latn-co',
  acb: 'acb-latn-ng',
  acd: 'acd-latn-zz',
  ace: 'ace-latn-id',
  acf: 'acf-latn-lc',
  ach: 'ach-latn-ug',
  acm: 'acm-arab-iq',
  acn: 'acn-latn-cn',
  acp: 'acp-latn-ng',
  acq: 'acq-arab-ye',
  acr: 'acr-latn-gt',
  acs: 'acs-latn-br',
  act: 'act-latn-nl',
  acu: 'acu-latn-ec',
  acv: 'acv-latn-us',
  acw: 'acw-arab-sa',
  acx: 'acx-arab-om',
  acy: 'acy-latn-cy',
  'acy-arab': 'acy-arab-cy',
  'acy-grek': 'acy-grek-cy',
  acz: 'acz-latn-sd',
  ada: 'ada-latn-gh',
  adb: 'adb-latn-tl',
  add: 'add-latn-cm',
  ade: 'ade-latn-zz',
  adf: 'adf-arab-om',
  adg: 'adg-latn-au',
  adh: 'adh-latn-ug',
  adi: 'adi-latn-in',
  'adi-tibt': 'adi-tibt-cn',
  adj: 'adj-latn-zz',
  adl: 'adl-latn-in',
  adn: 'adn-latn-id',
  ado: 'ado-latn-pg',
  adp: 'adp-tibt-bt',
  adq: 'adq-latn-gh',
  adr: 'adr-latn-id',
  adt: 'adt-latn-au',
  adu: 'adu-latn-ng',
  adw: 'adw-latn-br',
  adx: 'adx-tibt-cn',
  ady: 'ady-cyrl-ru',
  adz: 'adz-latn-zz',
  ae: 'ae-avst-ir',
  aea: 'aea-latn-au',
  aeb: 'aeb-arab-tn',
  aec: 'aec-arab-eg',
  aee: 'aee-arab-af',
  aek: 'aek-latn-nc',
  ael: 'ael-latn-cm',
  aem: 'aem-latn-vn',
  aeq: 'aeq-arab-pk',
  aer: 'aer-latn-au',
  aeu: 'aeu-latn-cn',
  aew: 'aew-latn-pg',
  aey: 'aey-latn-zz',
  aez: 'aez-latn-pg',
  af: 'af-latn-za',
  afb: 'afb-arab-kw',
  afd: 'afd-latn-pg',
  afe: 'afe-latn-ng',
  afh: 'afh-latn-gh',
  afi: 'afi-latn-pg',
  afk: 'afk-latn-pg',
  afn: 'afn-latn-ng',
  afo: 'afo-latn-ng',
  afp: 'afp-latn-pg',
  afs: 'afs-latn-mx',
  afu: 'afu-latn-gh',
  afz: 'afz-latn-id',
  aga: 'aga-latn-pe',
  agb: 'agb-latn-ng',
  agc: 'agc-latn-zz',
  agd: 'agd-latn-zz',
  age: 'age-latn-pg',
  agf: 'agf-latn-id',
  agg: 'agg-latn-zz',
  agh: 'agh-latn-cd',
  agi: 'agi-deva-in',
  agj: 'agj-ethi-et',
  'agj-arab': 'agj-arab-et',
  agk: 'agk-latn-ph',
  agl: 'agl-latn-pg',
  agm: 'agm-latn-zz',
  agn: 'agn-latn-ph',
  ago: 'ago-latn-zz',
  agq: 'agq-latn-cm',
  agr: 'agr-latn-pe',
  ags: 'ags-latn-cm',
  agt: 'agt-latn-ph',
  agu: 'agu-latn-gt',
  agv: 'agv-latn-ph',
  agw: 'agw-latn-sb',
  agx: 'agx-cyrl-ru',
  agy: 'agy-latn-ph',
  agz: 'agz-latn-ph',
  aha: 'aha-latn-zz',
  ahb: 'ahb-latn-vu',
  ahg: 'ahg-ethi-et',
  ahh: 'ahh-latn-id',
  ahi: 'ahi-latn-ci',
  ahk: 'ahk-latn-mm',
  'ahk-mymr': 'ahk-mymr-mm',
  'ahk-th': 'ahk-latn-th',
  'ahk-thai': 'ahk-thai-th',
  ahl: 'ahl-latn-zz',
  ahm: 'ahm-latn-ci',
  ahn: 'ahn-latn-ng',
  aho: 'aho-ahom-in',
  ahp: 'ahp-latn-ci',
  ahr: 'ahr-deva-in',
  ahs: 'ahs-latn-ng',
  aht: 'aht-latn-us',
  aia: 'aia-latn-sb',
  aib: 'aib-arab-cn',
  aic: 'aic-latn-pg',
  aid: 'aid-latn-au',
  aie: 'aie-latn-pg',
  aif: 'aif-latn-pg',
  aig: 'aig-latn-ag',
  aij: 'aij-hebr-il',
  aik: 'aik-latn-ng',
  ail: 'ail-latn-pg',
  aim: 'aim-latn-in',
  ain: 'ain-kana-jp',
  'ain-latn': 'ain-latn-jp',
  aio: 'aio-mymr-in',
  aip: 'aip-latn-id',
  aiq: 'aiq-arab-af',
  air: 'air-latn-id',
  ait: 'ait-latn-br',
  aiw: 'aiw-latn-et',
  'aiw-arab': 'aiw-arab-et',
  'aiw-ethi': 'aiw-ethi-et',
  aix: 'aix-latn-pg',
  aiy: 'aiy-latn-cf',
  aja: 'aja-latn-ss',
  ajg: 'ajg-latn-zz',
  aji: 'aji-latn-nc',
  ajn: 'ajn-latn-au',
  ajp: 'ajp-arab-jo',
  ajt: 'ajt-arab-tn',
  ajw: 'ajw-latn-ng',
  ajz: 'ajz-latn-in',
  ak: 'ak-latn-gh',
  akb: 'akb-latn-id',
  'akb-batk': 'akb-batk-id',
  akc: 'akc-latn-id',
  akd: 'akd-latn-ng',
  ake: 'ake-latn-gy',
  akf: 'akf-latn-ng',
  akg: 'akg-latn-id',
  akh: 'akh-latn-pg',
  aki: 'aki-latn-pg',
  akk: 'akk-xsux-iq',
  akl: 'akl-latn-ph',
  ako: 'ako-latn-sr',
  akp: 'akp-latn-gh',
  akq: 'akq-latn-pg',
  akr: 'akr-latn-vu',
  aks: 'aks-latn-tg',
  akt: 'akt-latn-pg',
  aku: 'aku-latn-cm',
  akv: 'akv-cyrl-ru',
  akw: 'akw-latn-cg',
  akz: 'akz-latn-us',
  ala: 'ala-latn-zz',
  alc: 'alc-latn-cl',
  ald: 'ald-latn-ci',
  ale: 'ale-latn-us',
  alf: 'alf-latn-ng',
  alh: 'alh-latn-au',
  ali: 'ali-latn-zz',
  alj: 'alj-latn-ph',
  alk: 'alk-laoo-la',
  all: 'all-mlym-in',
  alm: 'alm-latn-vu',
  aln: 'aln-latn-xk',
  alo: 'alo-latn-id',
  alp: 'alp-latn-id',
  alq: 'alq-latn-ca',
  alr: 'alr-cyrl-ru',
  alt: 'alt-cyrl-ru',
  alu: 'alu-latn-sb',
  alw: 'alw-ethi-et',
  alx: 'alx-latn-pg',
  aly: 'aly-latn-au',
  alz: 'alz-latn-cd',
  am: 'am-ethi-et',
  ama: 'ama-latn-br',
  amb: 'amb-latn-ng',
  amc: 'amc-latn-pe',
  ame: 'ame-latn-pe',
  amf: 'amf-latn-et',
  'amf-ethi': 'amf-ethi-et',
  amg: 'amg-latn-au',
  ami: 'ami-latn-tw',
  amj: 'amj-latn-td',
  amk: 'amk-latn-id',
  amm: 'amm-latn-zz',
  amn: 'amn-latn-zz',
  amo: 'amo-latn-ng',
  amp: 'amp-latn-zz',
  amq: 'amq-latn-id',
  amr: 'amr-latn-pe',
  ams: 'ams-jpan-jp',
  amt: 'amt-latn-pg',
  amu: 'amu-latn-mx',
  amv: 'amv-latn-id',
  amw: 'amw-syrc-sy',
  'amw-arab': 'amw-arab-sy',
  'amw-armi': 'amw-armi-sy',
  'amw-latn': 'amw-latn-sy',
  amx: 'amx-latn-au',
  amy: 'amy-latn-au',
  amz: 'amz-latn-au',
  an: 'an-latn-es',
  ana: 'ana-latn-co',
  anb: 'anb-latn-pe',
  anc: 'anc-latn-zz',
  and: 'and-latn-id',
  ane: 'ane-latn-nc',
  anf: 'anf-latn-gh',
  ang: 'ang-latn-gb',
  anh: 'anh-latn-pg',
  ani: 'ani-cyrl-ru',
  anj: 'anj-latn-pg',
  ank: 'ank-latn-zz',
  anl: 'anl-latn-mm',
  anm: 'anm-latn-in',
  ann: 'ann-latn-ng',
  ano: 'ano-latn-co',
  anp: 'anp-deva-in',
  anr: 'anr-deva-in',
  ans: 'ans-latn-co',
  ant: 'ant-latn-au',
  anu: 'anu-ethi-et',
  'anu-arab': 'anu-arab-ss',
  'anu-latn': 'anu-latn-ss',
  anv: 'anv-latn-cm',
  anw: 'anw-latn-ng',
  anx: 'anx-latn-pg',
  any: 'any-latn-zz',
  anz: 'anz-latn-pg',
  aoa: 'aoa-latn-st',
  aob: 'aob-latn-pg',
  aoc: 'aoc-latn-ve',
  aod: 'aod-latn-pg',
  aoe: 'aoe-latn-pg',
  aof: 'aof-latn-pg',
  aog: 'aog-latn-pg',
  aoi: 'aoi-latn-au',
  aoj: 'aoj-latn-zz',
  aok: 'aok-latn-nc',
  aol: 'aol-latn-id',
  aom: 'aom-latn-zz',
  aon: 'aon-latn-pg',
  aor: 'aor-latn-vu',
  aos: 'aos-latn-id',
  aot: 'aot-beng-bd',
  'aot-latn': 'aot-latn-in',
  aox: 'aox-latn-gy',
  aoz: 'aoz-latn-id',
  apb: 'apb-latn-sb',
  apc: 'apc-arab-sy',
  apd: 'apd-arab-tg',
  ape: 'ape-latn-zz',
  apf: 'apf-latn-ph',
  apg: 'apg-latn-id',
  aph: 'aph-deva-np',
  api: 'api-latn-br',
  apj: 'apj-latn-us',
  apk: 'apk-latn-us',
  apl: 'apl-latn-us',
  apm: 'apm-latn-us',
  apn: 'apn-latn-br',
  apo: 'apo-latn-pg',
  app: 'app-latn-vu',
  apr: 'apr-latn-zz',
  aps: 'aps-latn-zz',
  apt: 'apt-latn-in',
  apu: 'apu-latn-br',
  apv: 'apv-latn-br',
  apw: 'apw-latn-us',
  apx: 'apx-latn-id',
  apy: 'apy-latn-br',
  apz: 'apz-latn-zz',
  aqc: 'aqc-cyrl-ru',
  aqd: 'aqd-latn-ml',
  aqg: 'aqg-latn-ng',
  aqk: 'aqk-latn-ng',
  aqm: 'aqm-latn-id',
  aqn: 'aqn-latn-ph',
  aqr: 'aqr-latn-nc',
  aqt: 'aqt-latn-py',
  aqz: 'aqz-latn-br',
  ar: 'ar-arab-eg',
  arc: 'arc-armi-ir',
  'arc-nbat': 'arc-nbat-jo',
  'arc-palm': 'arc-palm-sy',
  ard: 'ard-latn-au',
  are: 'are-latn-au',
  arh: 'arh-latn-zz',
  ari: 'ari-latn-us',
  arj: 'arj-latn-br',
  ark: 'ark-latn-br',
  arl: 'arl-latn-pe',
  arn: 'arn-latn-cl',
  aro: 'aro-latn-bo',
  arp: 'arp-latn-us',
  arq: 'arq-arab-dz',
  arr: 'arr-latn-br',
  ars: 'ars-arab-sa',
  aru: 'aru-latn-br',
  arw: 'arw-latn-sr',
  arx: 'arx-latn-br',
  ary: 'ary-arab-ma',
  arz: 'arz-arab-eg',
  as: 'as-beng-in',
  asa: 'asa-latn-tz',
  asb: 'asb-latn-ca',
  asc: 'asc-latn-id',
  ase: 'ase-sgnw-us',
  asg: 'asg-latn-zz',
  ash: 'ash-latn-pe',
  asi: 'asi-latn-id',
  asj: 'asj-latn-cm',
  ask: 'ask-arab-af',
  asl: 'asl-latn-id',
  asn: 'asn-latn-br',
  aso: 'aso-latn-zz',
  ass: 'ass-latn-cm',
  ast: 'ast-latn-es',
  asu: 'asu-latn-br',
  asv: 'asv-latn-cd',
  asx: 'asx-latn-pg',
  asy: 'asy-latn-id',
  asz: 'asz-latn-id',
  ata: 'ata-latn-zz',
  atb: 'atb-latn-cn',
  'atb-lisu': 'atb-lisu-cn',
  atc: 'atc-latn-pe',
  atd: 'atd-latn-ph',
  ate: 'ate-latn-pg',
  atg: 'atg-latn-zz',
  ati: 'ati-latn-ci',
  atj: 'atj-latn-ca',
  atk: 'atk-latn-ph',
  atl: 'atl-latn-ph',
  atm: 'atm-latn-ph',
  atn: 'atn-arab-ir',
  ato: 'ato-latn-cm',
  atp: 'atp-latn-ph',
  atq: 'atq-latn-id',
  atr: 'atr-latn-br',
  ats: 'ats-latn-us',
  att: 'att-latn-ph',
  atu: 'atu-latn-ss',
  atv: 'atv-cyrl-ru',
  atw: 'atw-latn-us',
  atx: 'atx-latn-br',
  aty: 'aty-latn-vu',
  atz: 'atz-latn-ph',
  aua: 'aua-latn-sb',
  auc: 'auc-latn-ec',
  aud: 'aud-latn-sb',
  aug: 'aug-latn-bj',
  auh: 'auh-latn-zm',
  aui: 'aui-latn-pg',
  auj: 'auj-arab-ly',
  'auj-latn': 'auj-latn-ly',
  'auj-tfng': 'auj-tfng-ly',
  auk: 'auk-latn-pg',
  aul: 'aul-latn-vu',
  aum: 'aum-latn-ng',
  aun: 'aun-latn-pg',
  auo: 'auo-latn-ng',
  aup: 'aup-latn-pg',
  auq: 'auq-latn-id',
  aur: 'aur-latn-pg',
  aut: 'aut-latn-pf',
  auu: 'auu-latn-id',
  auw: 'auw-latn-id',
  auy: 'auy-latn-zz',
  auz: 'auz-arab-uz',
  av: 'av-cyrl-ru',
  avb: 'avb-latn-pg',
  avd: 'avd-arab-ir',
  avi: 'avi-latn-ci',
  avk: 'avk-latn-001',
  avl: 'avl-arab-zz',
  avm: 'avm-latn-au',
  avn: 'avn-latn-zz',
  avo: 'avo-latn-br',
  avs: 'avs-latn-pe',
  avt: 'avt-latn-zz',
  avu: 'avu-latn-zz',
  avv: 'avv-latn-br',
  awa: 'awa-deva-in',
  awb: 'awb-latn-zz',
  awc: 'awc-latn-ng',
  awe: 'awe-latn-br',
  awg: 'awg-latn-au',
  awh: 'awh-latn-id',
  awi: 'awi-latn-pg',
  awk: 'awk-latn-au',
  awm: 'awm-latn-pg',
  awn: 'awn-ethi-et',
  awo: 'awo-latn-zz',
  awr: 'awr-latn-id',
  aws: 'aws-latn-id',
  awt: 'awt-latn-br',
  awu: 'awu-latn-id',
  awv: 'awv-latn-id',
  aww: 'aww-latn-pg',
  awx: 'awx-latn-zz',
  awy: 'awy-latn-id',
  axb: 'axb-latn-ar',
  axe: 'axe-latn-au',
  axg: 'axg-latn-br',
  axk: 'axk-latn-cf',
  axl: 'axl-latn-au',
  axm: 'axm-armn-am',
  axx: 'axx-latn-nc',
  ay: 'ay-latn-bo',
  aya: 'aya-latn-pg',
  ayb: 'ayb-latn-zz',
  ayc: 'ayc-latn-pe',
  ayd: 'ayd-latn-au',
  aye: 'aye-latn-ng',
  ayg: 'ayg-latn-tg',
  ayh: 'ayh-arab-ye',
  ayi: 'ayi-latn-ng',
  ayk: 'ayk-latn-ng',
  ayl: 'ayl-arab-ly',
  ayn: 'ayn-arab-ye',
  ayo: 'ayo-latn-py',
  ayp: 'ayp-arab-iq',
  ayq: 'ayq-latn-pg',
  ays: 'ays-latn-ph',
  ayt: 'ayt-latn-ph',
  ayu: 'ayu-latn-ng',
  ayz: 'ayz-latn-id',
  az: 'az-latn-az',
  'az-arab': 'az-arab-ir',
  'az-iq': 'az-arab-iq',
  'az-ir': 'az-arab-ir',
  'az-ru': 'az-cyrl-ru',
  azb: 'azb-arab-ir',
  'azb-cyrl': 'azb-cyrl-az',
  'azb-latn': 'azb-latn-az',
  azd: 'azd-latn-mx',
  azg: 'azg-latn-mx',
  azm: 'azm-latn-mx',
  azn: 'azn-latn-mx',
  azo: 'azo-latn-cm',
  azt: 'azt-latn-ph',
  azz: 'azz-latn-mx',
  ba: 'ba-cyrl-ru',
  baa: 'baa-latn-sb',
  bab: 'bab-latn-gw',
  bac: 'bac-latn-id',
  bae: 'bae-latn-ve',
  baf: 'baf-latn-cm',
  bag: 'bag-latn-cm',
  bah: 'bah-latn-bs',
  baj: 'baj-latn-id',
  bal: 'bal-arab-pk',
  ban: 'ban-latn-id',
  bao: 'bao-latn-co',
  bap: 'bap-deva-np',
  bar: 'bar-latn-at',
  bas: 'bas-latn-cm',
  bau: 'bau-latn-ng',
  bav: 'bav-latn-zz',
  baw: 'baw-latn-cm',
  bax: 'bax-bamu-cm',
  bay: 'bay-latn-id',
  bba: 'bba-latn-zz',
  bbb: 'bbb-latn-zz',
  bbc: 'bbc-latn-id',
  bbd: 'bbd-latn-zz',
  bbe: 'bbe-latn-cd',
  bbf: 'bbf-latn-pg',
  bbg: 'bbg-latn-ga',
  bbi: 'bbi-latn-cm',
  bbj: 'bbj-latn-cm',
  bbk: 'bbk-latn-cm',
  bbl: 'bbl-geor-ge',
  bbm: 'bbm-latn-cd',
  bbn: 'bbn-latn-pg',
  bbo: 'bbo-latn-bf',
  bbp: 'bbp-latn-zz',
  bbq: 'bbq-latn-cm',
  bbr: 'bbr-latn-zz',
  bbs: 'bbs-latn-ng',
  bbt: 'bbt-latn-ng',
  bbu: 'bbu-latn-ng',
  bbv: 'bbv-latn-pg',
  bbw: 'bbw-latn-cm',
  bbx: 'bbx-latn-cm',
  bby: 'bby-latn-cm',
  bca: 'bca-latn-cn',
  'bca-hani': 'bca-hani-cn',
  bcb: 'bcb-latn-sn',
  bcd: 'bcd-latn-id',
  bce: 'bce-latn-cm',
  bcf: 'bcf-latn-zz',
  bcg: 'bcg-latn-gn',
  bch: 'bch-latn-zz',
  bci: 'bci-latn-ci',
  bcj: 'bcj-latn-au',
  bck: 'bck-latn-au',
  bcm: 'bcm-latn-zz',
  bcn: 'bcn-latn-zz',
  bco: 'bco-latn-zz',
  bcp: 'bcp-latn-cd',
  bcq: 'bcq-ethi-zz',
  bcr: 'bcr-latn-ca',
  bcs: 'bcs-latn-ng',
  bct: 'bct-latn-cd',
  bcu: 'bcu-latn-zz',
  bcv: 'bcv-latn-ng',
  bcw: 'bcw-latn-cm',
  bcy: 'bcy-latn-ng',
  bcz: 'bcz-latn-sn',
  bda: 'bda-latn-sn',
  bdb: 'bdb-latn-id',
  bdc: 'bdc-latn-co',
  bdd: 'bdd-latn-zz',
  bde: 'bde-latn-ng',
  bdf: 'bdf-latn-pg',
  bdg: 'bdg-latn-my',
  bdh: 'bdh-latn-ss',
  bdi: 'bdi-latn-sd',
  bdj: 'bdj-latn-ss',
  bdk: 'bdk-latn-az',
  bdl: 'bdl-latn-id',
  bdm: 'bdm-latn-td',
  bdn: 'bdn-latn-cm',
  bdo: 'bdo-latn-td',
  bdp: 'bdp-latn-tz',
  bdq: 'bdq-latn-vn',
  bdr: 'bdr-latn-my',
  bds: 'bds-latn-tz',
  bdt: 'bdt-latn-cf',
  bdu: 'bdu-latn-cm',
  bdv: 'bdv-orya-in',
  bdw: 'bdw-latn-id',
  bdx: 'bdx-latn-id',
  bdy: 'bdy-latn-au',
  bdz: 'bdz-arab-pk',
  be: 'be-cyrl-by',
  bea: 'bea-latn-ca',
  'bea-cans': 'bea-cans-ca',
  beb: 'beb-latn-cm',
  bec: 'bec-latn-cm',
  bed: 'bed-latn-id',
  bee: 'bee-deva-in',
  bef: 'bef-latn-zz',
  beh: 'beh-latn-zz',
  bei: 'bei-latn-id',
  bej: 'bej-arab-sd',
  bek: 'bek-latn-pg',
  bem: 'bem-latn-zm',
  beo: 'beo-latn-pg',
  bep: 'bep-latn-id',
  beq: 'beq-latn-cg',
  bes: 'bes-latn-td',
  bet: 'bet-latn-zz',
  beu: 'beu-latn-id',
  bev: 'bev-latn-ci',
  bew: 'bew-latn-id',
  bex: 'bex-latn-zz',
  bey: 'bey-latn-pg',
  bez: 'bez-latn-tz',
  bfa: 'bfa-latn-ss',
  'bfa-arab': 'bfa-arab-ss',
  bfb: 'bfb-deva-in',
  bfc: 'bfc-latn-cn',
  bfd: 'bfd-latn-cm',
  bfe: 'bfe-latn-id',
  bff: 'bff-latn-cf',
  bfg: 'bfg-latn-id',
  bfh: 'bfh-latn-pg',
  bfj: 'bfj-latn-cm',
  bfl: 'bfl-latn-cf',
  bfm: 'bfm-latn-cm',
  bfn: 'bfn-latn-tl',
  bfo: 'bfo-latn-bf',
  bfp: 'bfp-latn-cm',
  bfq: 'bfq-taml-in',
  bfs: 'bfs-latn-cn',
  'bfs-hani': 'bfs-hani-cn',
  bft: 'bft-arab-pk',
  bfu: 'bfu-tibt-in',
  'bfu-takr': 'bfu-takr-in',
  bfw: 'bfw-orya-in',
  bfx: 'bfx-latn-ph',
  bfy: 'bfy-deva-in',
  bfz: 'bfz-deva-in',
  bg: 'bg-cyrl-bg',
  bga: 'bga-latn-ng',
  bgb: 'bgb-latn-id',
  bgc: 'bgc-deva-in',
  bgd: 'bgd-deva-in',
  bgf: 'bgf-latn-cm',
  bgg: 'bgg-latn-in',
  bgi: 'bgi-latn-ph',
  bgj: 'bgj-latn-cm',
  bgn: 'bgn-arab-pk',
  bgo: 'bgo-latn-gn',
  bgp: 'bgp-arab-pk',
  bgq: 'bgq-deva-in',
  bgr: 'bgr-latn-in',
  bgs: 'bgs-latn-ph',
  bgt: 'bgt-latn-sb',
  bgu: 'bgu-latn-ng',
  bgv: 'bgv-latn-id',
  bgw: 'bgw-deva-in',
  bgx: 'bgx-grek-tr',
  bgy: 'bgy-latn-id',
  bgz: 'bgz-latn-id',
  bha: 'bha-deva-in',
  bhb: 'bhb-deva-in',
  bhc: 'bhc-latn-id',
  bhd: 'bhd-deva-in',
  'bhd-arab': 'bhd-arab-in',
  'bhd-takr': 'bhd-takr-in',
  bhe: 'bhe-arab-pk',
  bhf: 'bhf-latn-pg',
  bhg: 'bhg-latn-zz',
  bhh: 'bhh-cyrl-il',
  'bhh-hebr': 'bhh-hebr-il',
  'bhh-latn': 'bhh-latn-il',
  bhi: 'bhi-deva-in',
  bhj: 'bhj-deva-np',
  bhl: 'bhl-latn-zz',
  bhm: 'bhm-arab-om',
  bhn: 'bhn-syrc-ge',
  bho: 'bho-deva-in',
  bhp: 'bhp-latn-id',
  bhq: 'bhq-latn-id',
  bhr: 'bhr-latn-mg',
  bhs: 'bhs-latn-cm',
  bht: 'bht-takr-in',
  'bht-deva': 'bht-deva-in',
  'bht-latn': 'bht-latn-in',
  bhu: 'bhu-deva-in',
  bhv: 'bhv-latn-id',
  bhw: 'bhw-latn-id',
  bhy: 'bhy-latn-zz',
  bhz: 'bhz-latn-id',
  bi: 'bi-latn-vu',
  bia: 'bia-latn-au',
  bib: 'bib-latn-zz',
  bid: 'bid-latn-td',
  bie: 'bie-latn-pg',
  bif: 'bif-latn-gw',
  big: 'big-latn-zz',
  bik: 'bik-latn-ph',
  bil: 'bil-latn-ng',
  bim: 'bim-latn-zz',
  bin: 'bin-latn-ng',
  bio: 'bio-latn-zz',
  bip: 'bip-latn-cd',
  biq: 'biq-latn-zz',
  bir: 'bir-latn-pg',
  bit: 'bit-latn-pg',
  biu: 'biu-latn-in',
  biv: 'biv-latn-gh',
  biw: 'biw-latn-cm',
  biy: 'biy-deva-in',
  biz: 'biz-latn-cd',
  bja: 'bja-latn-cd',
  bjb: 'bjb-latn-au',
  bjc: 'bjc-latn-pg',
  bjf: 'bjf-syrc-il',
  bjg: 'bjg-latn-gw',
  bjh: 'bjh-latn-zz',
  bji: 'bji-ethi-zz',
  bjj: 'bjj-deva-in',
  bjk: 'bjk-latn-pg',
  bjl: 'bjl-latn-pg',
  bjm: 'bjm-arab-iq',
  bjn: 'bjn-latn-id',
  bjo: 'bjo-latn-zz',
  bjp: 'bjp-latn-pg',
  bjr: 'bjr-latn-zz',
  bjs: 'bjs-latn-bb',
  bjt: 'bjt-latn-sn',
  bju: 'bju-latn-cm',
  bjv: 'bjv-latn-td',
  bjw: 'bjw-latn-ci',
  bjx: 'bjx-latn-ph',
  bjy: 'bjy-latn-au',
  bjz: 'bjz-latn-zz',
  bka: 'bka-latn-ng',
  bkc: 'bkc-latn-zz',
  bkd: 'bkd-latn-ph',
  bkf: 'bkf-latn-cd',
  bkg: 'bkg-latn-cf',
  bkh: 'bkh-latn-cm',
  bki: 'bki-latn-vu',
  bkj: 'bkj-latn-cf',
  bkl: 'bkl-latn-id',
  bkm: 'bkm-latn-cm',
  bkn: 'bkn-latn-id',
  bko: 'bko-latn-cm',
  bkp: 'bkp-latn-cd',
  bkq: 'bkq-latn-zz',
  bkr: 'bkr-latn-id',
  bks: 'bks-latn-ph',
  bkt: 'bkt-latn-cd',
  bku: 'bku-latn-ph',
  bkv: 'bkv-latn-zz',
  bkw: 'bkw-latn-cg',
  bkx: 'bkx-latn-tl',
  bky: 'bky-latn-ng',
  bkz: 'bkz-latn-id',
  bla: 'bla-latn-ca',
  blb: 'blb-latn-sb',
  blc: 'blc-latn-ca',
  bld: 'bld-latn-id',
  ble: 'ble-latn-gw',
  blf: 'blf-latn-id',
  blg: 'blg-latn-my',
  blh: 'blh-latn-lr',
  bli: 'bli-latn-cd',
  blj: 'blj-latn-id',
  blk: 'blk-mymr-mm',
  blm: 'blm-latn-ss',
  bln: 'bln-latn-ph',
  blo: 'blo-latn-bj',
  blp: 'blp-latn-sb',
  blq: 'blq-latn-pg',
  blr: 'blr-latn-cn',
  'blr-tale': 'blr-tale-cn',
  'blr-thai': 'blr-thai-th',
  bls: 'bls-latn-id',
  blt: 'blt-tavt-vn',
  blv: 'blv-latn-ao',
  blw: 'blw-latn-ph',
  blx: 'blx-latn-ph',
  bly: 'bly-latn-bj',
  blz: 'blz-latn-id',
  bm: 'bm-latn-ml',
  bma: 'bma-latn-ng',
  bmb: 'bmb-latn-cd',
  bmc: 'bmc-latn-pg',
  bmd: 'bmd-latn-gn',
  bme: 'bme-latn-cf',
  bmf: 'bmf-latn-sl',
  bmg: 'bmg-latn-cd',
  bmh: 'bmh-latn-zz',
  bmi: 'bmi-latn-td',
  bmj: 'bmj-deva-np',
  bmk: 'bmk-latn-zz',
  bml: 'bml-latn-cd',
  bmm: 'bmm-latn-mg',
  bmn: 'bmn-latn-pg',
  bmo: 'bmo-latn-cm',
  bmp: 'bmp-latn-pg',
  bmq: 'bmq-latn-ml',
  bmr: 'bmr-latn-co',
  bms: 'bms-latn-ne',
  bmu: 'bmu-latn-zz',
  bmv: 'bmv-latn-cm',
  bmw: 'bmw-latn-cg',
  bmx: 'bmx-latn-pg',
  bmz: 'bmz-latn-pg',
  bn: 'bn-beng-bd',
  bna: 'bna-latn-id',
  bnb: 'bnb-latn-my',
  bnc: 'bnc-latn-ph',
  bnd: 'bnd-latn-id',
  bne: 'bne-latn-id',
  bnf: 'bnf-latn-id',
  bng: 'bng-latn-zz',
  bni: 'bni-latn-cd',
  bnj: 'bnj-latn-ph',
  bnk: 'bnk-latn-vu',
  bnm: 'bnm-latn-zz',
  bnn: 'bnn-latn-tw',
  bno: 'bno-latn-ph',
  bnp: 'bnp-latn-zz',
  bnq: 'bnq-latn-id',
  bnr: 'bnr-latn-vu',
  bns: 'bns-deva-in',
  bnu: 'bnu-latn-id',
  bnv: 'bnv-latn-id',
  bnw: 'bnw-latn-pg',
  bnx: 'bnx-latn-cd',
  bny: 'bny-latn-my',
  bnz: 'bnz-latn-cm',
  bo: 'bo-tibt-cn',
  boa: 'boa-latn-pe',
  bob: 'bob-latn-ke',
  boe: 'boe-latn-cm',
  bof: 'bof-latn-bf',
  boh: 'boh-latn-cd',
  boj: 'boj-latn-zz',
  bok: 'bok-latn-cg',
  bol: 'bol-latn-ng',
  bom: 'bom-latn-zz',
  bon: 'bon-latn-zz',
  boo: 'boo-latn-ml',
  bop: 'bop-latn-pg',
  boq: 'boq-latn-pg',
  bor: 'bor-latn-br',
  bot: 'bot-latn-ss',
  bou: 'bou-latn-tz',
  bov: 'bov-latn-gh',
  bow: 'bow-latn-pg',
  box: 'box-latn-bf',
  boy: 'boy-latn-cf',
  boz: 'boz-latn-ml',
  'boz-arab': 'boz-arab-ml',
  bpa: 'bpa-latn-vu',
  bpc: 'bpc-latn-cm',
  bpd: 'bpd-latn-cf',
  bpe: 'bpe-latn-pg',
  bpg: 'bpg-latn-id',
  bph: 'bph-cyrl-ru',
  bpi: 'bpi-latn-pg',
  bpj: 'bpj-latn-cd',
  bpk: 'bpk-latn-nc',
  bpl: 'bpl-latn-au',
  bpm: 'bpm-latn-pg',
  bpo: 'bpo-latn-id',
  bpp: 'bpp-latn-id',
  bpq: 'bpq-latn-id',
  bpr: 'bpr-latn-ph',
  bps: 'bps-latn-ph',
  bpt: 'bpt-latn-au',
  bpu: 'bpu-latn-pg',
  bpv: 'bpv-latn-id',
  bpw: 'bpw-latn-pg',
  bpx: 'bpx-deva-in',
  bpy: 'bpy-beng-in',
  bpz: 'bpz-latn-id',
  bqa: 'bqa-latn-bj',
  bqb: 'bqb-latn-id',
  bqc: 'bqc-latn-zz',
  bqd: 'bqd-latn-cm',
  bqf: 'bqf-latn-gn',
  'bqf-arab': 'bqf-arab-gn',
  bqg: 'bqg-latn-tg',
  bqi: 'bqi-arab-ir',
  bqj: 'bqj-latn-sn',
  bqk: 'bqk-latn-cf',
  bql: 'bql-latn-pg',
  bqm: 'bqm-latn-cm',
  bqo: 'bqo-latn-cm',
  bqp: 'bqp-latn-zz',
  bqq: 'bqq-latn-id',
  bqr: 'bqr-latn-id',
  bqs: 'bqs-latn-pg',
  bqt: 'bqt-latn-cm',
  bqu: 'bqu-latn-cd',
  bqv: 'bqv-latn-ci',
  bqw: 'bqw-latn-ng',
  bqx: 'bqx-latn-ng',
  bqz: 'bqz-latn-cm',
  br: 'br-latn-fr',
  bra: 'bra-deva-in',
  brb: 'brb-khmr-kh',
  'brb-laoo': 'brb-laoo-la',
  'brb-latn': 'brb-latn-vn',
  brc: 'brc-latn-gy',
  brd: 'brd-deva-np',
  brf: 'brf-latn-cd',
  brg: 'brg-latn-bo',
  brh: 'brh-arab-pk',
  bri: 'bri-latn-cm',
  brj: 'brj-latn-vu',
  brk: 'brk-arab-sd',
  brl: 'brl-latn-bw',
  brm: 'brm-latn-cd',
  brn: 'brn-latn-cr',
  brp: 'brp-latn-id',
  brq: 'brq-latn-pg',
  brr: 'brr-latn-sb',
  brs: 'brs-latn-id',
  brt: 'brt-latn-ng',
  bru: 'bru-latn-vn',
  'bru-laoo': 'bru-laoo-la',
  'bru-thai': 'bru-thai-la',
  brv: 'brv-laoo-la',
  brx: 'brx-deva-in',
  bry: 'bry-latn-pg',
  brz: 'brz-latn-zz',
  bs: 'bs-latn-ba',
  bsa: 'bsa-latn-id',
  bsb: 'bsb-latn-bn',
  bsc: 'bsc-latn-sn',
  bse: 'bse-latn-cm',
  bsf: 'bsf-latn-ng',
  bsh: 'bsh-arab-af',
  bsi: 'bsi-latn-cm',
  bsj: 'bsj-latn-zz',
  bsk: 'bsk-arab-pk',
  'bsk-latn': 'bsk-latn-pk',
  bsl: 'bsl-latn-ng',
  bsm: 'bsm-latn-id',
  bsn: 'bsn-latn-co',
  bso: 'bso-latn-td',
  bsp: 'bsp-latn-gn',
  bsq: 'bsq-bass-lr',
  bsr: 'bsr-latn-ng',
  bss: 'bss-latn-cm',
  bst: 'bst-ethi-zz',
  bsu: 'bsu-latn-id',
  bsv: 'bsv-latn-gn',
  'bsv-arab': 'bsv-arab-gn',
  bsw: 'bsw-latn-et',
  'bsw-ethi': 'bsw-ethi-et',
  bsx: 'bsx-latn-ng',
  bsy: 'bsy-latn-my',
  bta: 'bta-latn-ng',
  btc: 'btc-latn-cm',
  btd: 'btd-batk-id',
  bte: 'bte-latn-ng',
  btf: 'btf-latn-td',
  btg: 'btg-latn-ci',
  bth: 'bth-latn-my',
  bti: 'bti-latn-id',
  btj: 'btj-latn-id',
  btm: 'btm-batk-id',
  btn: 'btn-latn-ph',
  bto: 'bto-latn-ph',
  btp: 'btp-latn-pg',
  btq: 'btq-latn-my',
  btr: 'btr-latn-vu',
  bts: 'bts-latn-id',
  'bts-batk': 'bts-batk-id',
  btt: 'btt-latn-zz',
  btu: 'btu-latn-ng',
  btv: 'btv-deva-pk',
  btw: 'btw-latn-ph',
  btx: 'btx-latn-id',
  'btx-batk': 'btx-batk-id',
  bty: 'bty-latn-id',
  btz: 'btz-latn-id',
  bua: 'bua-cyrl-ru',
  bub: 'bub-latn-td',
  buc: 'buc-latn-yt',
  bud: 'bud-latn-zz',
  bue: 'bue-latn-ca',
  buf: 'buf-latn-cd',
  bug: 'bug-latn-id',
  buh: 'buh-latn-cn',
  bui: 'bui-latn-cg',
  buj: 'buj-latn-ng',
  buk: 'buk-latn-zz',
  bum: 'bum-latn-cm',
  bun: 'bun-latn-sl',
  buo: 'buo-latn-zz',
  bup: 'bup-latn-id',
  buq: 'buq-latn-pg',
  bus: 'bus-latn-zz',
  but: 'but-latn-pg',
  buu: 'buu-latn-zz',
  buv: 'buv-latn-pg',
  buw: 'buw-latn-ga',
  bux: 'bux-latn-ng',
  buy: 'buy-latn-sl',
  buz: 'buz-latn-ng',
  bva: 'bva-latn-td',
  bvb: 'bvb-latn-gq',
  bvc: 'bvc-latn-sb',
  bvd: 'bvd-latn-sb',
  bve: 'bve-latn-id',
  bvf: 'bvf-latn-td',
  bvg: 'bvg-latn-cm',
  bvh: 'bvh-latn-ng',
  bvi: 'bvi-latn-ss',
  bvj: 'bvj-latn-ng',
  bvk: 'bvk-latn-id',
  bvm: 'bvm-latn-cm',
  bvn: 'bvn-latn-pg',
  bvo: 'bvo-latn-td',
  bvq: 'bvq-latn-cf',
  bvr: 'bvr-latn-au',
  bvt: 'bvt-latn-id',
  bvu: 'bvu-latn-id',
  bvv: 'bvv-latn-ve',
  bvw: 'bvw-latn-ng',
  bvx: 'bvx-latn-cg',
  bvy: 'bvy-latn-ph',
  bvz: 'bvz-latn-id',
  bwa: 'bwa-latn-nc',
  bwb: 'bwb-latn-fj',
  bwc: 'bwc-latn-zm',
  bwd: 'bwd-latn-zz',
  bwe: 'bwe-mymr-mm',
  'bwe-latn': 'bwe-latn-mm',
  bwf: 'bwf-latn-pg',
  bwg: 'bwg-latn-mz',
  bwh: 'bwh-latn-cm',
  bwi: 'bwi-latn-ve',
  bwj: 'bwj-latn-bf',
  bwk: 'bwk-latn-pg',
  bwl: 'bwl-latn-cd',
  bwm: 'bwm-latn-pg',
  bwo: 'bwo-latn-et',
  'bwo-ethi': 'bwo-ethi-et',
  bwp: 'bwp-latn-id',
  bwq: 'bwq-latn-bf',
  bwr: 'bwr-latn-zz',
  bws: 'bws-latn-cd',
  bwt: 'bwt-latn-cm',
  bwu: 'bwu-latn-gh',
  bww: 'bww-latn-cd',
  bwx: 'bwx-latn-cn',
  bwy: 'bwy-latn-bf',
  bwz: 'bwz-latn-cg',
  bxa: 'bxa-latn-sb',
  bxb: 'bxb-latn-ss',
  bxc: 'bxc-latn-gq',
  bxf: 'bxf-latn-pg',
  bxg: 'bxg-latn-cd',
  bxh: 'bxh-latn-zz',
  bxi: 'bxi-latn-au',
  bxj: 'bxj-latn-au',
  bxl: 'bxl-latn-bf',
  bxm: 'bxm-cyrl-mn',
  'bxm-latn': 'bxm-latn-mn',
  'bxm-mong': 'bxm-mong-mn',
  bxn: 'bxn-latn-au',
  bxo: 'bxo-latn-ng',
  bxp: 'bxp-latn-cm',
  bxq: 'bxq-latn-ng',
  bxs: 'bxs-latn-cm',
  bxu: 'bxu-mong-cn',
  'bxu-cyrl': 'bxu-cyrl-cn',
  'bxu-latn': 'bxu-latn-cn',
  bxv: 'bxv-latn-td',
  bxw: 'bxw-latn-ml',
  bxz: 'bxz-latn-pg',
  bya: 'bya-latn-ph',
  byb: 'byb-latn-cm',
  byc: 'byc-latn-ng',
  byd: 'byd-latn-id',
  bye: 'bye-latn-zz',
  byf: 'byf-latn-ng',
  byh: 'byh-deva-np',
  byi: 'byi-latn-cd',
  byj: 'byj-latn-ng',
  byk: 'byk-latn-cn',
  byl: 'byl-latn-id',
  bym: 'bym-latn-au',
  byn: 'byn-ethi-er',
  byp: 'byp-latn-ng',
  byr: 'byr-latn-zz',
  bys: 'bys-latn-zz',
  byv: 'byv-latn-cm',
  byw: 'byw-deva-np',
  byx: 'byx-latn-zz',
  byz: 'byz-latn-pg',
  bza: 'bza-latn-zz',
  bzb: 'bzb-latn-id',
  bzc: 'bzc-latn-mg',
  bzd: 'bzd-latn-cr',
  bze: 'bze-latn-ml',
  bzf: 'bzf-latn-zz',
  bzh: 'bzh-latn-zz',
  bzi: 'bzi-thai-th',
  bzj: 'bzj-latn-bz',
  bzk: 'bzk-latn-ni',
  bzl: 'bzl-latn-id',
  bzm: 'bzm-latn-cd',
  bzn: 'bzn-latn-id',
  bzo: 'bzo-latn-cd',
  bzp: 'bzp-latn-id',
  bzq: 'bzq-latn-id',
  bzr: 'bzr-latn-au',
  bzt: 'bzt-latn-001',
  bzu: 'bzu-latn-id',
  bzv: 'bzv-latn-cm',
  bzw: 'bzw-latn-zz',
  bzx: 'bzx-latn-ml',
  bzy: 'bzy-latn-ng',
  bzz: 'bzz-latn-ng',
  ca: 'ca-latn-es',
  caa: 'caa-latn-gt',
  cab: 'cab-latn-hn',
  cac: 'cac-latn-gt',
  cad: 'cad-latn-us',
  cae: 'cae-latn-sn',
  caf: 'caf-latn-ca',
  'caf-cans': 'caf-cans-ca',
  cag: 'cag-latn-py',
  cah: 'cah-latn-pe',
  caj: 'caj-latn-ar',
  cak: 'cak-latn-gt',
  cal: 'cal-latn-mp',
  cam: 'cam-latn-nc',
  can: 'can-latn-zz',
  cao: 'cao-latn-bo',
  cap: 'cap-latn-bo',
  caq: 'caq-latn-in',
  car: 'car-latn-ve',
  cas: 'cas-latn-bo',
  cav: 'cav-latn-bo',
  caw: 'caw-latn-bo',
  cax: 'cax-latn-bo',
  cay: 'cay-latn-ca',
  caz: 'caz-latn-bo',
  cbb: 'cbb-latn-co',
  cbc: 'cbc-latn-co',
  cbd: 'cbd-latn-co',
  cbg: 'cbg-latn-co',
  cbi: 'cbi-latn-ec',
  cbj: 'cbj-latn-zz',
  cbk: 'cbk-latn-ph',
  'cbk-brai': 'cbk-brai-ph',
  cbl: 'cbl-latn-mm',
  cbn: 'cbn-thai-th',
  cbo: 'cbo-latn-ng',
  cbq: 'cbq-latn-ng',
  cbr: 'cbr-latn-pe',
  cbs: 'cbs-latn-pe',
  cbt: 'cbt-latn-pe',
  cbu: 'cbu-latn-pe',
  cbv: 'cbv-latn-co',
  cbw: 'cbw-latn-ph',
  cby: 'cby-latn-co',
  ccc: 'ccc-latn-pe',
  ccd: 'ccd-latn-br',
  cce: 'cce-latn-mz',
  ccg: 'ccg-latn-ng',
  cch: 'cch-latn-ng',
  ccj: 'ccj-latn-gw',
  ccl: 'ccl-latn-tz',
  ccm: 'ccm-latn-my',
  cco: 'cco-latn-mx',
  ccp: 'ccp-cakm-bd',
  ccr: 'ccr-latn-sv',
  cde: 'cde-telu-in',
  cdf: 'cdf-latn-in',
  'cdf-beng': 'cdf-beng-in',
  cdh: 'cdh-deva-in',
  'cdh-takr': 'cdh-takr-in',
  cdi: 'cdi-gujr-in',
  cdj: 'cdj-deva-in',
  cdm: 'cdm-deva-np',
  'cdm-latn': 'cdm-latn-np',
  cdo: 'cdo-hans-cn',
  'cdo-hant': 'cdo-hant-cn',
  'cdo-latn': 'cdo-latn-cn',
  cdr: 'cdr-latn-ng',
  cdz: 'cdz-beng-in',
  ce: 'ce-cyrl-ru',
  cea: 'cea-latn-us',
  ceb: 'ceb-latn-ph',
  ceg: 'ceg-latn-py',
  cek: 'cek-latn-mm',
  cen: 'cen-latn-ng',
  cet: 'cet-latn-ng',
  cey: 'cey-latn-mm',
  cfa: 'cfa-latn-zz',
  cfd: 'cfd-latn-ng',
  cfg: 'cfg-latn-ng',
  cfm: 'cfm-latn-mm',
  'cfm-beng': 'cfm-beng-in',
  cga: 'cga-latn-pg',
  cgc: 'cgc-latn-ph',
  cgg: 'cgg-latn-ug',
  cgk: 'cgk-tibt-bt',
  ch: 'ch-latn-gu',
  chb: 'chb-latn-co',
  chd: 'chd-latn-mx',
  chf: 'chf-latn-mx',
  chg: 'chg-arab-tm',
  chh: 'chh-latn-us',
  chj: 'chj-latn-mx',
  chk: 'chk-latn-fm',
  chl: 'chl-latn-us',
  chm: 'chm-cyrl-ru',
  chn: 'chn-latn-us',
  'chn-dupl': 'chn-dupl-us',
  cho: 'cho-latn-us',
  chp: 'chp-latn-ca',
  chq: 'chq-latn-mx',
  chr: 'chr-cher-us',
  cht: 'cht-latn-pe',
  chw: 'chw-latn-mz',
  chx: 'chx-deva-np',
  chy: 'chy-latn-us',
  chz: 'chz-latn-mx',
  cia: 'cia-latn-id',
  'cia-arab': 'cia-arab-id',
  'cia-hang': 'cia-hang-id',
  cib: 'cib-latn-bj',
  cic: 'cic-latn-us',
  cie: 'cie-latn-ng',
  cih: 'cih-deva-in',
  cim: 'cim-latn-it',
  cin: 'cin-latn-br',
  cip: 'cip-latn-mx',
  cir: 'cir-latn-nc',
  ciw: 'ciw-latn-us',
  'ciw-cans': 'ciw-cans-us',
  ciy: 'ciy-latn-ve',
  cja: 'cja-arab-kh',
  cje: 'cje-latn-vn',
  cjh: 'cjh-latn-us',
  cji: 'cji-cyrl-ru',
  cjk: 'cjk-latn-ao',
  cjm: 'cjm-cham-vn',
  cjn: 'cjn-latn-pg',
  cjo: 'cjo-latn-pe',
  cjp: 'cjp-latn-cr',
  cjs: 'cjs-latn-ru',
  'cjs-cyrl': 'cjs-cyrl-ru',
  cjv: 'cjv-latn-zz',
  cjy: 'cjy-hans-cn',
  'cjy-hant': 'cjy-hant-cn',
  ckb: 'ckb-arab-iq',
  ckl: 'ckl-latn-zz',
  ckm: 'ckm-latn-hr',
  'ckm-glag': 'ckm-glag-hr',
  ckn: 'ckn-latn-mm',
  cko: 'cko-latn-zz',
  ckq: 'ckq-latn-td',
  ckr: 'ckr-latn-pg',
  cks: 'cks-latn-nc',
  ckt: 'ckt-cyrl-ru',
  cku: 'cku-latn-us',
  ckv: 'ckv-latn-tw',
  ckx: 'ckx-latn-cm',
  cky: 'cky-latn-zz',
  ckz: 'ckz-latn-gt',
  cla: 'cla-latn-zz',
  clc: 'clc-latn-ca',
  cle: 'cle-latn-mx',
  clh: 'clh-arab-pk',
  cli: 'cli-latn-gh',
  clj: 'clj-latn-mm',
  clk: 'clk-latn-in',
  'clk-tibt': 'clk-tibt-cn',
  cll: 'cll-latn-gh',
  clm: 'clm-latn-us',
  clo: 'clo-latn-mx',
  clt: 'clt-latn-mm',
  clu: 'clu-latn-ph',
  clw: 'clw-cyrl-ru',
  cly: 'cly-latn-mx',
  cma: 'cma-latn-vn',
  cme: 'cme-latn-zz',
  cmg: 'cmg-soyo-mn',
  cmi: 'cmi-latn-co',
  cml: 'cml-latn-id',
  cmo: 'cmo-latn-vn',
  'cmo-kh': 'cmo-latn-kh',
  'cmo-khmr': 'cmo-khmr-kh',
  cmr: 'cmr-latn-mm',
  cms: 'cms-latn-it',
  cmt: 'cmt-latn-za',
  cna: 'cna-tibt-in',
  cnb: 'cnb-latn-mm',
  cnc: 'cnc-latn-vn',
  cng: 'cng-latn-cn',
  cnh: 'cnh-latn-mm',
  cni: 'cni-latn-pe',
  cnk: 'cnk-latn-mm',
  cnl: 'cnl-latn-mx',
  cnp: 'cnp-hans-cn',
  'cnp-hant': 'cnp-hant-cn',
  cnq: 'cnq-latn-cm',
  cns: 'cns-latn-id',
  cnt: 'cnt-latn-mx',
  cnw: 'cnw-latn-mm',
  cnx: 'cnx-latn-gb',
  co: 'co-latn-fr',
  coa: 'coa-latn-au',
  cob: 'cob-latn-mx',
  coc: 'coc-latn-mx',
  cod: 'cod-latn-pe',
  coe: 'coe-latn-co',
  cof: 'cof-latn-ec',
  cog: 'cog-thai-th',
  coh: 'coh-latn-ke',
  coj: 'coj-latn-mx',
  cok: 'cok-latn-mx',
  col: 'col-latn-us',
  com: 'com-latn-us',
  coo: 'coo-latn-ca',
  cop: 'cop-copt-eg',
  coq: 'coq-latn-us',
  cot: 'cot-latn-pe',
  cou: 'cou-latn-sn',
  cox: 'cox-latn-pe',
  coz: 'coz-latn-mx',
  cpa: 'cpa-latn-mx',
  cpb: 'cpb-latn-pe',
  cpc: 'cpc-latn-pe',
  cpg: 'cpg-grek-gr',
  cpi: 'cpi-latn-nr',
  cpn: 'cpn-latn-gh',
  cpo: 'cpo-latn-bf',
  cps: 'cps-latn-ph',
  cpu: 'cpu-latn-pe',
  cpx: 'cpx-latn-cn',
  cpy: 'cpy-latn-pe',
  cqd: 'cqd-latn-cn',
  cr: 'cr-cans-ca',
  crb: 'crb-latn-vc',
  crc: 'crc-latn-vu',
  crd: 'crd-latn-us',
  crf: 'crf-latn-co',
  crg: 'crg-latn-ca',
  crh: 'crh-cyrl-ua',
  cri: 'cri-latn-st',
  crj: 'crj-cans-ca',
  'crj-latn': 'crj-latn-ca',
  crk: 'crk-cans-ca',
  crl: 'crl-cans-ca',
  crm: 'crm-cans-ca',
  crn: 'crn-latn-mx',
  cro: 'cro-latn-us',
  crq: 'crq-latn-ar',
  crs: 'crs-latn-sc',
  crt: 'crt-latn-ar',
  crv: 'crv-latn-in',
  crw: 'crw-latn-vn',
  crx: 'crx-latn-ca',
  'crx-cans': 'crx-cans-ca',
  cry: 'cry-latn-ng',
  crz: 'crz-latn-us',
  cs: 'cs-latn-cz',
  csa: 'csa-latn-mx',
  csb: 'csb-latn-pl',
  csh: 'csh-mymr-mm',
  'csh-latn': 'csh-latn-mm',
  csj: 'csj-latn-mm',
  csk: 'csk-latn-sn',
  csm: 'csm-latn-us',
  cso: 'cso-latn-mx',
  csp: 'csp-hans-cn',
  'csp-hant': 'csp-hant-cn',
  css: 'css-latn-us',
  cst: 'cst-latn-us',
  csv: 'csv-latn-mm',
  csw: 'csw-cans-ca',
  csy: 'csy-latn-mm',
  csz: 'csz-latn-us',
  cta: 'cta-latn-mx',
  ctc: 'ctc-latn-us',
  ctd: 'ctd-pauc-mm',
  cte: 'cte-latn-mx',
  ctg: 'ctg-beng-bd',
  'ctg-arab': 'ctg-arab-bd',
  'ctg-latn': 'ctg-latn-bd',
  cth: 'cth-latn-mm',
  ctl: 'ctl-latn-mx',
  ctm: 'ctm-latn-us',
  ctn: 'ctn-deva-np',
  cto: 'cto-latn-co',
  ctp: 'ctp-latn-mx',
  cts: 'cts-latn-ph',
  ctt: 'ctt-taml-in',
  ctu: 'ctu-latn-mx',
  ctz: 'ctz-latn-mx',
  cu: 'cu-cyrl-ru',
  'cu-glag': 'cu-glag-bg',
  cua: 'cua-latn-vn',
  cub: 'cub-latn-co',
  cuc: 'cuc-latn-mx',
  cuh: 'cuh-latn-ke',
  cui: 'cui-latn-co',
  cuj: 'cuj-latn-pe',
  cuk: 'cuk-latn-pa',
  cul: 'cul-latn-br',
  cuo: 'cuo-latn-ve',
  cup: 'cup-latn-us',
  cut: 'cut-latn-mx',
  cuu: 'cuu-lana-cn',
  cuv: 'cuv-latn-cm',
  cux: 'cux-latn-mx',
  cv: 'cv-cyrl-ru',
  cvg: 'cvg-latn-in',
  'cvg-tibt': 'cvg-tibt-in',
  cvn: 'cvn-latn-mx',
  cwa: 'cwa-latn-tz',
  cwb: 'cwb-latn-mz',
  cwe: 'cwe-latn-tz',
  cwg: 'cwg-latn-my',
  cwt: 'cwt-latn-sn',
  cy: 'cy-latn-gb',
  cya: 'cya-latn-mx',
  cyb: 'cyb-latn-bo',
  cyo: 'cyo-latn-ph',
  czh: 'czh-hans-cn',
  'czh-hant': 'czh-hant-cn',
  czk: 'czk-hebr-cz',
  czn: 'czn-latn-mx',
  czt: 'czt-latn-mm',
  da: 'da-latn-dk',
  daa: 'daa-latn-td',
  dac: 'dac-latn-pg',
  dad: 'dad-latn-zz',
  dae: 'dae-latn-cm',
  daf: 'daf-latn-ci',
  dag: 'dag-latn-zz',
  dah: 'dah-latn-zz',
  dai: 'dai-latn-td',
  daj: 'daj-latn-sd',
  dak: 'dak-latn-us',
  dal: 'dal-latn-ke',
  dam: 'dam-latn-ng',
  dao: 'dao-latn-mm',
  daq: 'daq-deva-in',
  dar: 'dar-cyrl-ru',
  das: 'das-latn-ci',
  dau: 'dau-latn-td',
  dav: 'dav-latn-ke',
  daw: 'daw-latn-ph',
  dax: 'dax-latn-au',
  daz: 'daz-latn-id',
  dba: 'dba-latn-ml',
  dbb: 'dbb-latn-ng',
  dbd: 'dbd-latn-zz',
  dbe: 'dbe-latn-id',
  dbf: 'dbf-latn-id',
  dbg: 'dbg-latn-ml',
  dbi: 'dbi-latn-ng',
  dbj: 'dbj-latn-my',
  'dbj-arab': 'dbj-arab-my',
  dbl: 'dbl-latn-au',
  dbm: 'dbm-latn-ng',
  dbn: 'dbn-latn-id',
  dbo: 'dbo-latn-ng',
  dbp: 'dbp-latn-ng',
  dbq: 'dbq-latn-zz',
  dbt: 'dbt-latn-ml',
  dbu: 'dbu-latn-ml',
  dbv: 'dbv-latn-ng',
  dbw: 'dbw-latn-ml',
  dby: 'dby-latn-pg',
  dcc: 'dcc-arab-in',
  dcr: 'dcr-latn-vi',
  dda: 'dda-latn-au',
  ddd: 'ddd-latn-ss',
  dde: 'dde-latn-cg',
  ddg: 'ddg-latn-tl',
  ddi: 'ddi-latn-pg',
  ddj: 'ddj-latn-au',
  ddn: 'ddn-latn-zz',
  ddo: 'ddo-cyrl-ru',
  ddr: 'ddr-latn-au',
  dds: 'dds-latn-ml',
  ddw: 'ddw-latn-id',
  de: 'de-latn-de',
  dec: 'dec-latn-sd',
  ded: 'ded-latn-zz',
  dee: 'dee-latn-lr',
  def: 'def-arab-ir',
  deg: 'deg-latn-ng',
  deh: 'deh-arab-pk',
  dei: 'dei-latn-id',
  dek: 'dek-latn-cm',
  del: 'del-latn-us',
  dem: 'dem-latn-id',
  den: 'den-latn-ca',
  deq: 'deq-latn-cf',
  der: 'der-beng-in',
  'der-latn': 'der-latn-in',
  des: 'des-latn-br',
  dev: 'dev-latn-pg',
  dez: 'dez-latn-cd',
  dga: 'dga-latn-zz',
  dgb: 'dgb-latn-ml',
  dgc: 'dgc-latn-ph',
  dgd: 'dgd-latn-bf',
  dge: 'dge-latn-pg',
  dgg: 'dgg-latn-pg',
  dgh: 'dgh-latn-zz',
  dgi: 'dgi-latn-zz',
  dgk: 'dgk-latn-cf',
  dgl: 'dgl-arab-zz',
  dgn: 'dgn-latn-au',
  dgr: 'dgr-latn-ca',
  dgs: 'dgs-latn-bf',
  dgt: 'dgt-latn-au',
  dgw: 'dgw-latn-au',
  dgx: 'dgx-latn-pg',
  dgz: 'dgz-latn-zz',
  dhg: 'dhg-latn-au',
  dhi: 'dhi-deva-np',
  dhl: 'dhl-latn-au',
  dhm: 'dhm-latn-ao',
  dhn: 'dhn-gujr-in',
  dho: 'dho-deva-in',
  dhr: 'dhr-latn-au',
  dhs: 'dhs-latn-tz',
  dhu: 'dhu-latn-au',
  dhv: 'dhv-latn-nc',
  dhw: 'dhw-deva-np',
  dhx: 'dhx-latn-au',
  dia: 'dia-latn-zz',
  dib: 'dib-latn-ss',
  dic: 'dic-latn-ci',
  did: 'did-latn-ss',
  dif: 'dif-latn-au',
  dig: 'dig-latn-ke',
  dih: 'dih-latn-mx',
  dii: 'dii-latn-cm',
  dij: 'dij-latn-id',
  dil: 'dil-latn-sd',
  din: 'din-latn-ss',
  'din-arab': 'din-arab-ss',
  dio: 'dio-latn-ng',
  dip: 'dip-latn-ss',
  dir: 'dir-latn-ng',
  dis: 'dis-latn-in',
  'dis-beng': 'dis-beng-in',
  diu: 'diu-latn-na',
  diw: 'diw-latn-ss',
  dix: 'dix-latn-vu',
  diy: 'diy-latn-id',
  diz: 'diz-latn-cd',
  dja: 'dja-latn-au',
  djb: 'djb-latn-au',
  djc: 'djc-latn-td',
  djd: 'djd-latn-au',
  dje: 'dje-latn-ne',
  djf: 'djf-latn-au',
  dji: 'dji-latn-au',
  djj: 'djj-latn-au',
  djk: 'djk-latn-sr',
  djm: 'djm-latn-ml',
  djn: 'djn-latn-au',
  djo: 'djo-latn-id',
  djr: 'djr-latn-au',
  dju: 'dju-latn-pg',
  djw: 'djw-latn-au',
  dka: 'dka-tibt-bt',
  dkg: 'dkg-latn-ng',
  dkk: 'dkk-latn-id',
  dkr: 'dkr-latn-my',
  dks: 'dks-latn-ss',
  dkx: 'dkx-latn-cm',
  dlg: 'dlg-cyrl-ru',
  dlm: 'dlm-latn-hr',
  dln: 'dln-latn-in',
  dma: 'dma-latn-ga',
  dmb: 'dmb-latn-ml',
  dmc: 'dmc-latn-pg',
  dmd: 'dmd-latn-au',
  dme: 'dme-latn-cm',
  dmf: 'dmf-medf-ng',
  dmg: 'dmg-latn-my',
  dmk: 'dmk-arab-pk',
  dml: 'dml-arab-pk',
  dmm: 'dmm-latn-cm',
  dmo: 'dmo-latn-cm',
  dmr: 'dmr-latn-id',
  dms: 'dms-latn-id',
  dmu: 'dmu-latn-id',
  dmv: 'dmv-latn-my',
  dmw: 'dmw-latn-au',
  dmx: 'dmx-latn-mz',
  dmy: 'dmy-latn-id',
  dna: 'dna-latn-id',
  dnd: 'dnd-latn-pg',
  dne: 'dne-latn-tz',
  dng: 'dng-cyrl-kg',
  'dng-arab': 'dng-arab-kg',
  dni: 'dni-latn-id',
  dnj: 'dnj-latn-ci',
  dnk: 'dnk-latn-id',
  dnn: 'dnn-latn-bf',
  dno: 'dno-latn-cd',
  dnr: 'dnr-latn-pg',
  dnt: 'dnt-latn-id',
  dnu: 'dnu-mymr-mm',
  dnv: 'dnv-mymr-mm',
  dnw: 'dnw-latn-id',
  dny: 'dny-latn-br',
  doa: 'doa-latn-pg',
  dob: 'dob-latn-zz',
  doc: 'doc-latn-cn',
  doe: 'doe-latn-tz',
  dof: 'dof-latn-pg',
  doh: 'doh-latn-ng',
  doi: 'doi-deva-in',
  dok: 'dok-latn-id',
  dol: 'dol-latn-pg',
  don: 'don-latn-pg',
  doo: 'doo-latn-cd',
  dop: 'dop-latn-zz',
  dor: 'dor-latn-sb',
  dos: 'dos-latn-bf',
  dot: 'dot-latn-ng',
  dov: 'dov-latn-zw',
  dow: 'dow-latn-zz',
  dox: 'dox-ethi-et',
  doy: 'doy-latn-gh',
  dpp: 'dpp-latn-my',
  drc: 'drc-latn-pt',
  dre: 'dre-tibt-np',
  drg: 'drg-latn-my',
  drh: 'drh-mong-cn',
  dri: 'dri-latn-zz',
  drl: 'drl-latn-au',
  drn: 'drn-latn-id',
  dro: 'dro-latn-my',
  drq: 'drq-deva-np',
  drs: 'drs-ethi-zz',
  drt: 'drt-latn-nl',
  dru: 'dru-latn-tw',
  dry: 'dry-deva-np',
  dsb: 'dsb-latn-de',
  dsh: 'dsh-latn-ke',
  dsi: 'dsi-latn-td',
  dsn: 'dsn-latn-id',
  dso: 'dso-orya-in',
  dsq: 'dsq-latn-ml',
  'dsq-arab': 'dsq-arab-ml',
  dta: 'dta-latn-cn',
  'dta-cyrl': 'dta-cyrl-cn',
  'dta-hans': 'dta-hans-cn',
  dtb: 'dtb-latn-my',
  dtd: 'dtd-latn-ca',
  dth: 'dth-latn-au',
  dti: 'dti-latn-ml',
  dtk: 'dtk-latn-ml',
  dtm: 'dtm-latn-ml',
  dto: 'dto-latn-ml',
  dtp: 'dtp-latn-my',
  dtr: 'dtr-latn-my',
  dts: 'dts-latn-zz',
  dtt: 'dtt-latn-ml',
  dtu: 'dtu-latn-ml',
  dty: 'dty-deva-np',
  dua: 'dua-latn-cm',
  dub: 'dub-gujr-in',
  duc: 'duc-latn-zz',
  dud: 'dud-latn-zz',
  due: 'due-latn-ph',
  duf: 'duf-latn-nc',
  dug: 'dug-latn-zz',
  duh: 'duh-deva-in',
  'duh-gujr': 'duh-gujr-in',
  dui: 'dui-latn-pg',
  duk: 'duk-latn-pg',
  dul: 'dul-latn-ph',
  dum: 'dum-latn-nl',
  dun: 'dun-latn-id',
  duo: 'duo-latn-ph',
  dup: 'dup-latn-id',
  duq: 'duq-latn-id',
  dur: 'dur-latn-cm',
  dus: 'dus-deva-np',
  duu: 'duu-latn-cn',
  duv: 'duv-latn-id',
  duw: 'duw-latn-id',
  dux: 'dux-latn-ml',
  duy: 'duy-latn-ph',
  duz: 'duz-latn-cm',
  dv: 'dv-thaa-mv',
  dva: 'dva-latn-zz',
  dwa: 'dwa-latn-ng',
  dwk: 'dwk-orya-in',
  dwr: 'dwr-latn-et',
  'dwr-ethi': 'dwr-ethi-et',
  dws: 'dws-latn-001',
  dwu: 'dwu-latn-au',
  dww: 'dww-latn-zz',
  dwy: 'dwy-latn-au',
  dwz: 'dwz-deva-np',
  dya: 'dya-latn-bf',
  dyb: 'dyb-latn-au',
  dyd: 'dyd-latn-au',
  dyg: 'dyg-latn-ph',
  dyi: 'dyi-latn-ci',
  dym: 'dym-latn-ml',
  dyn: 'dyn-latn-au',
  dyo: 'dyo-latn-sn',
  dyu: 'dyu-latn-bf',
  dyy: 'dyy-latn-au',
  dz: 'dz-tibt-bt',
  dza: 'dza-latn-ng',
  dze: 'dze-latn-au',
  dzg: 'dzg-latn-zz',
  dzl: 'dzl-tibt-bt',
  dzn: 'dzn-latn-cd',
  eaa: 'eaa-latn-au',
  ebc: 'ebc-latn-id',
  ebg: 'ebg-latn-ng',
  ebk: 'ebk-latn-ph',
  ebo: 'ebo-latn-cg',
  ebr: 'ebr-latn-ci',
  ebu: 'ebu-latn-ke',
  ecr: 'ecr-grek-gr',
  ecy: 'ecy-cprt-cy',
  ee: 'ee-latn-gh',
  efa: 'efa-latn-ng',
  efe: 'efe-latn-cd',
  efi: 'efi-latn-ng',
  ega: 'ega-latn-ci',
  egl: 'egl-latn-it',
  egm: 'egm-latn-tz',
  ego: 'ego-latn-ng',
  egy: 'egy-egyp-eg',
  ehu: 'ehu-latn-ng',
  eip: 'eip-latn-id',
  eit: 'eit-latn-pg',
  eiv: 'eiv-latn-pg',
  eja: 'eja-latn-gw',
  eka: 'eka-latn-zz',
  eke: 'eke-latn-ng',
  ekg: 'ekg-latn-id',
  eki: 'eki-latn-ng',
  ekl: 'ekl-latn-bd',
  ekm: 'ekm-latn-cm',
  eko: 'eko-latn-mz',
  'eko-arab': 'eko-arab-mz',
  ekp: 'ekp-latn-ng',
  ekr: 'ekr-latn-ng',
  eky: 'eky-kali-mm',
  el: 'el-grek-gr',
  ele: 'ele-latn-pg',
  elk: 'elk-latn-pg',
  elm: 'elm-latn-ng',
  elo: 'elo-latn-ke',
  elu: 'elu-latn-pg',
  ema: 'ema-latn-zz',
  emb: 'emb-latn-id',
  eme: 'eme-latn-gf',
  emg: 'emg-deva-np',
  emi: 'emi-latn-zz',
  emm: 'emm-latn-mx',
  emn: 'emn-latn-cm',
  emp: 'emp-latn-pa',
  ems: 'ems-latn-us',
  'ems-cyrl': 'ems-cyrl-us',
  emu: 'emu-deva-in',
  emw: 'emw-latn-id',
  emx: 'emx-latn-fr',
  emz: 'emz-latn-cm',
  en: 'en-latn-us',
  'en-shaw': 'en-shaw-gb',
  ena: 'ena-latn-pg',
  enb: 'enb-latn-ke',
  enc: 'enc-latn-vn',
  end: 'end-latn-id',
  enf: 'enf-cyrl-ru',
  enh: 'enh-cyrl-ru',
  enl: 'enl-latn-py',
  enm: 'enm-latn-gb',
  enn: 'enn-latn-zz',
  eno: 'eno-latn-id',
  enq: 'enq-latn-zz',
  enr: 'enr-latn-id',
  env: 'env-latn-ng',
  enw: 'enw-latn-ng',
  enx: 'enx-latn-py',
  eo: 'eo-latn-001',
  eot: 'eot-latn-ci',
  epi: 'epi-latn-ng',
  era: 'era-taml-in',
  erg: 'erg-latn-vu',
  erh: 'erh-latn-ng',
  eri: 'eri-latn-zz',
  erk: 'erk-latn-vu',
  err: 'err-latn-au',
  ert: 'ert-latn-id',
  erw: 'erw-latn-id',
  es: 'es-latn-es',
  ese: 'ese-latn-bo',
  esg: 'esg-gonm-in',
  esh: 'esh-arab-ir',
  esi: 'esi-latn-us',
  esm: 'esm-latn-ci',
  ess: 'ess-latn-us',
  'ess-cyrl': 'ess-cyrl-us',
  esu: 'esu-latn-us',
  esy: 'esy-latn-ph',
  et: 'et-latn-ee',
  etb: 'etb-latn-ng',
  etn: 'etn-latn-vu',
  eto: 'eto-latn-cm',
  etr: 'etr-latn-zz',
  ets: 'ets-latn-ng',
  ett: 'ett-ital-it',
  etu: 'etu-latn-zz',
  etx: 'etx-latn-zz',
  etz: 'etz-latn-id',
  eu: 'eu-latn-es',
  eve: 'eve-cyrl-ru',
  evh: 'evh-latn-ng',
  evn: 'evn-cyrl-ru',
  'evn-latn': 'evn-latn-cn',
  'evn-mong': 'evn-mong-cn',
  ewo: 'ewo-latn-cm',
  ext: 'ext-latn-es',
  eya: 'eya-latn-us',
  eyo: 'eyo-latn-ke',
  eza: 'eza-latn-zz',
  eze: 'eze-latn-ng',
  fa: 'fa-arab-ir',
  faa: 'faa-latn-zz',
  fab: 'fab-latn-zz',
  fad: 'fad-latn-pg',
  faf: 'faf-latn-sb',
  fag: 'fag-latn-zz',
  fah: 'fah-latn-ng',
  fai: 'fai-latn-zz',
  faj: 'faj-latn-pg',
  fak: 'fak-latn-cm',
  fal: 'fal-latn-cm',
  fam: 'fam-latn-ng',
  fan: 'fan-latn-gq',
  fap: 'fap-latn-sn',
  far: 'far-latn-sb',
  fau: 'fau-latn-id',
  fax: 'fax-latn-es',
  fay: 'fay-arab-ir',
  faz: 'faz-arab-ir',
  fbl: 'fbl-latn-ph',
  fer: 'fer-latn-ss',
  ff: 'ff-latn-sn',
  'ff-adlm': 'ff-adlm-gn',
  ffi: 'ffi-latn-zz',
  ffm: 'ffm-latn-ml',
  fgr: 'fgr-latn-td',
  fi: 'fi-latn-fi',
  fia: 'fia-arab-sd',
  fie: 'fie-latn-ng',
  fif: 'fif-latn-sa',
  fil: 'fil-latn-ph',
  fip: 'fip-latn-tz',
  fir: 'fir-latn-ng',
  fit: 'fit-latn-se',
  fiw: 'fiw-latn-pg',
  fj: 'fj-latn-fj',
  fkk: 'fkk-latn-ng',
  fkv: 'fkv-latn-no',
  fla: 'fla-latn-us',
  flh: 'flh-latn-id',
  fli: 'fli-latn-ng',
  fll: 'fll-latn-cm',
  fln: 'fln-latn-au',
  flr: 'flr-latn-zz',
  fly: 'fly-latn-za',
  fmp: 'fmp-latn-zz',
  fmu: 'fmu-deva-in',
  fnb: 'fnb-latn-vu',
  fng: 'fng-latn-za',
  fni: 'fni-latn-td',
  fo: 'fo-latn-fo',
  fod: 'fod-latn-zz',
  foi: 'foi-latn-pg',
  fom: 'fom-latn-cd',
  fon: 'fon-latn-bj',
  for: 'for-latn-zz',
  fos: 'fos-latn-tw',
  fpe: 'fpe-latn-zz',
  fqs: 'fqs-latn-zz',
  fr: 'fr-latn-fr',
  frc: 'frc-latn-us',
  frd: 'frd-latn-id',
  frk: 'frk-latn-de',
  frm: 'frm-latn-fr',
  fro: 'fro-latn-fr',
  frp: 'frp-latn-fr',
  frq: 'frq-latn-pg',
  frr: 'frr-latn-de',
  frs: 'frs-latn-de',
  frt: 'frt-latn-vu',
  fub: 'fub-arab-cm',
  fud: 'fud-latn-wf',
  fue: 'fue-latn-zz',
  fuf: 'fuf-latn-gn',
  fuh: 'fuh-latn-zz',
  fui: 'fui-latn-td',
  fum: 'fum-latn-ng',
  fun: 'fun-latn-br',
  fuq: 'fuq-latn-ne',
  fur: 'fur-latn-it',
  fut: 'fut-latn-vu',
  fuu: 'fuu-latn-cd',
  fuv: 'fuv-latn-ng',
  fuy: 'fuy-latn-zz',
  fvr: 'fvr-latn-sd',
  fwa: 'fwa-latn-nc',
  fwe: 'fwe-latn-na',
  fy: 'fy-latn-nl',
  ga: 'ga-latn-ie',
  gaa: 'gaa-latn-gh',
  gab: 'gab-latn-td',
  gac: 'gac-latn-in',
  'gac-deva': 'gac-deva-in',
  gad: 'gad-latn-ph',
  gae: 'gae-latn-ve',
  gaf: 'gaf-latn-zz',
  gag: 'gag-latn-md',
  gah: 'gah-latn-zz',
  gai: 'gai-latn-pg',
  gaj: 'gaj-latn-zz',
  gak: 'gak-latn-id',
  gal: 'gal-latn-tl',
  gam: 'gam-latn-zz',
  gan: 'gan-hans-cn',
  gao: 'gao-latn-pg',
  gap: 'gap-latn-pg',
  gaq: 'gaq-orya-in',
  gar: 'gar-latn-pg',
  gas: 'gas-gujr-in',
  gat: 'gat-latn-pg',
  gau: 'gau-telu-in',
  gaw: 'gaw-latn-zz',
  gax: 'gax-latn-et',
  'gax-ethi': 'gax-ethi-et',
  gay: 'gay-latn-id',
  gba: 'gba-latn-zz',
  gbb: 'gbb-latn-au',
  gbd: 'gbd-latn-au',
  gbe: 'gbe-latn-pg',
  gbf: 'gbf-latn-zz',
  gbg: 'gbg-latn-cf',
  gbh: 'gbh-latn-bj',
  gbi: 'gbi-latn-id',
  gbj: 'gbj-orya-in',
  gbk: 'gbk-deva-in',
  'gbk-takr': 'gbk-takr-in',
  gbl: 'gbl-gujr-in',
  'gbl-deva': 'gbl-deva-in',
  gbm: 'gbm-deva-in',
  gbn: 'gbn-latn-ss',
  gbp: 'gbp-latn-cf',
  gbq: 'gbq-latn-cf',
  gbr: 'gbr-latn-ng',
  gbs: 'gbs-latn-bj',
  gbu: 'gbu-latn-au',
  gbv: 'gbv-latn-cf',
  gbw: 'gbw-latn-au',
  gbx: 'gbx-latn-bj',
  gby: 'gby-latn-zz',
  gbz: 'gbz-arab-ir',
  gcc: 'gcc-latn-pg',
  gcd: 'gcd-latn-au',
  gcf: 'gcf-latn-gp',
  gcl: 'gcl-latn-gd',
  gcn: 'gcn-latn-pg',
  gcr: 'gcr-latn-gf',
  gct: 'gct-latn-ve',
  gd: 'gd-latn-gb',
  gdb: 'gdb-orya-in',
  'gdb-telu': 'gdb-telu-in',
  gdc: 'gdc-latn-au',
  gdd: 'gdd-latn-pg',
  gde: 'gde-latn-zz',
  gdf: 'gdf-latn-ng',
  gdg: 'gdg-latn-ph',
  gdh: 'gdh-latn-au',
  gdi: 'gdi-latn-cf',
  gdj: 'gdj-latn-au',
  gdk: 'gdk-latn-td',
  gdl: 'gdl-latn-et',
  'gdl-ethi': 'gdl-ethi-et',
  gdm: 'gdm-latn-td',
  gdn: 'gdn-latn-zz',
  gdo: 'gdo-cyrl-ru',
  gdq: 'gdq-latn-ye',
  gdr: 'gdr-latn-zz',
  gdt: 'gdt-latn-au',
  gdu: 'gdu-latn-ng',
  gdx: 'gdx-deva-in',
  gea: 'gea-latn-ng',
  geb: 'geb-latn-zz',
  gec: 'gec-latn-lr',
  ged: 'ged-latn-ng',
  gef: 'gef-latn-id',
  geg: 'geg-latn-ng',
  geh: 'geh-latn-ca',
  gei: 'gei-latn-id',
  gej: 'gej-latn-zz',
  gek: 'gek-latn-ng',
  gel: 'gel-latn-zz',
  geq: 'geq-latn-cf',
  ges: 'ges-latn-id',
  gev: 'gev-latn-ga',
  gew: 'gew-latn-ng',
  gex: 'gex-latn-so',
  gey: 'gey-latn-cd',
  gez: 'gez-ethi-et',
  gfk: 'gfk-latn-zz',
  gga: 'gga-latn-sb',
  ggb: 'ggb-latn-lr',
  ggd: 'ggd-latn-au',
  gge: 'gge-latn-au',
  ggg: 'ggg-arab-pk',
  ggk: 'ggk-latn-au',
  ggl: 'ggl-latn-pg',
  ggn: 'ggn-deva-np',
  ggt: 'ggt-latn-pg',
  ggu: 'ggu-latn-ci',
  ggw: 'ggw-latn-pg',
  gha: 'gha-arab-ly',
  'gha-latn': 'gha-latn-ly',
  'gha-tfng': 'gha-tfng-ly',
  ghc: 'ghc-latn-gb',
  ghe: 'ghe-deva-np',
  ghk: 'ghk-latn-mm',
  ghn: 'ghn-latn-sb',
  ghr: 'ghr-arab-pk',
  ghs: 'ghs-latn-zz',
  ght: 'ght-tibt-np',
  gia: 'gia-latn-au',
  gib: 'gib-latn-ng',
  gic: 'gic-latn-za',
  gid: 'gid-latn-cm',
  gie: 'gie-latn-ci',
  gig: 'gig-arab-pk',
  gih: 'gih-latn-au',
  gil: 'gil-latn-ki',
  gim: 'gim-latn-zz',
  gin: 'gin-cyrl-ru',
  gip: 'gip-latn-pg',
  giq: 'giq-latn-vn',
  gir: 'gir-latn-vn',
  gis: 'gis-latn-cm',
  git: 'git-latn-ca',
  gix: 'gix-latn-cd',
  giy: 'giy-latn-au',
  giz: 'giz-latn-cm',
  gjk: 'gjk-arab-pk',
  gjm: 'gjm-latn-au',
  gjn: 'gjn-latn-zz',
  gjr: 'gjr-latn-au',
  gju: 'gju-arab-pk',
  gka: 'gka-latn-pg',
  gkd: 'gkd-latn-pg',
  gke: 'gke-latn-cm',
  gkn: 'gkn-latn-zz',
  gko: 'gko-latn-au',
  gkp: 'gkp-latn-zz',
  gku: 'gku-latn-za',
  gl: 'gl-latn-es',
  glb: 'glb-latn-ng',
  glc: 'glc-latn-td',
  gld: 'gld-cyrl-ru',
  glh: 'glh-arab-af',
  glj: 'glj-latn-td',
  glk: 'glk-arab-ir',
  gll: 'gll-latn-au',
  glo: 'glo-latn-ng',
  glr: 'glr-latn-lr',
  glu: 'glu-latn-td',
  glw: 'glw-latn-ng',
  gma: 'gma-latn-au',
  gmb: 'gmb-latn-sb',
  gmd: 'gmd-latn-ng',
  gmg: 'gmg-latn-pg',
  gmh: 'gmh-latn-de',
  gmm: 'gmm-latn-zz',
  gmn: 'gmn-latn-cm',
  gmr: 'gmr-latn-au',
  gmu: 'gmu-latn-pg',
  gmv: 'gmv-ethi-zz',
  gmx: 'gmx-latn-tz',
  gmy: 'gmy-linb-gr',
  gmz: 'gmz-latn-ng',
  gn: 'gn-latn-py',
  gna: 'gna-latn-bf',
  gnb: 'gnb-latn-in',
  gnc: 'gnc-latn-es',
  gnd: 'gnd-latn-zz',
  gne: 'gne-latn-ng',
  gng: 'gng-latn-zz',
  gnh: 'gnh-latn-ng',
  gni: 'gni-latn-au',
  gnj: 'gnj-latn-ci',
  gnk: 'gnk-latn-bw',
  gnl: 'gnl-latn-au',
  gnm: 'gnm-latn-pg',
  gnn: 'gnn-latn-au',
  gnq: 'gnq-latn-my',
  gnr: 'gnr-latn-au',
  gnt: 'gnt-latn-pg',
  gnu: 'gnu-latn-pg',
  gnw: 'gnw-latn-bo',
  gnz: 'gnz-latn-cf',
  goa: 'goa-latn-ci',
  gob: 'gob-latn-co',
  goc: 'goc-latn-pg',
  god: 'god-latn-zz',
  goe: 'goe-tibt-bt',
  gof: 'gof-ethi-zz',
  gog: 'gog-latn-tz',
  goh: 'goh-latn-de',
  goi: 'goi-latn-zz',
  gok: 'gok-deva-in',
  gol: 'gol-latn-lr',
  gom: 'gom-deva-in',
  gon: 'gon-telu-in',
  goo: 'goo-latn-fj',
  gop: 'gop-latn-id',
  goq: 'goq-latn-id',
  gor: 'gor-latn-id',
  gos: 'gos-latn-nl',
  got: 'got-goth-ua',
  gou: 'gou-latn-cm',
  gov: 'gov-latn-ci',
  gow: 'gow-latn-tz',
  gox: 'gox-latn-cd',
  goy: 'goy-latn-td',
  gpa: 'gpa-latn-ng',
  gpe: 'gpe-latn-gh',
  gpn: 'gpn-latn-pg',
  gqa: 'gqa-latn-ng',
  gqn: 'gqn-latn-br',
  gqr: 'gqr-latn-td',
  gra: 'gra-deva-in',
  'gra-gujr': 'gra-gujr-in',
  grb: 'grb-latn-zz',
  grc: 'grc-cprt-cy',
  'grc-linb': 'grc-linb-gr',
  grd: 'grd-latn-ng',
  grg: 'grg-latn-pg',
  grh: 'grh-latn-ng',
  gri: 'gri-latn-sb',
  grj: 'grj-latn-lr',
  grm: 'grm-latn-my',
  grq: 'grq-latn-pg',
  grs: 'grs-latn-id',
  grt: 'grt-beng-in',
  gru: 'gru-ethi-et',
  'gru-latn': 'gru-latn-et',
  grv: 'grv-latn-lr',
  grw: 'grw-latn-zz',
  grx: 'grx-latn-pg',
  gry: 'gry-latn-lr',
  grz: 'grz-latn-pg',
  gsl: 'gsl-latn-sn',
  gsn: 'gsn-latn-pg',
  gso: 'gso-latn-cf',
  gsp: 'gsp-latn-pg',
  gsw: 'gsw-latn-ch',
  gta: 'gta-latn-br',
  gtu: 'gtu-latn-au',
  gu: 'gu-gujr-in',
  gua: 'gua-latn-ng',
  gub: 'gub-latn-br',
  guc: 'guc-latn-co',
  gud: 'gud-latn-zz',
  gue: 'gue-latn-au',
  guf: 'guf-latn-au',
  guh: 'guh-latn-co',
  gui: 'gui-latn-bo',
  guk: 'guk-latn-et',
  'guk-ethi': 'guk-ethi-et',
  gul: 'gul-latn-us',
  gum: 'gum-latn-co',
  gun: 'gun-latn-br',
  guo: 'guo-latn-co',
  gup: 'gup-latn-au',
  guq: 'guq-latn-py',
  gur: 'gur-latn-gh',
  gut: 'gut-latn-cr',
  guu: 'guu-latn-ve',
  guw: 'guw-latn-zz',
  gux: 'gux-latn-zz',
  guz: 'guz-latn-ke',
  gv: 'gv-latn-im',
  gva: 'gva-latn-py',
  gvc: 'gvc-latn-br',
  gve: 'gve-latn-pg',
  gvf: 'gvf-latn-zz',
  gvj: 'gvj-latn-br',
  gvl: 'gvl-latn-td',
  gvm: 'gvm-latn-ng',
  gvn: 'gvn-latn-au',
  gvo: 'gvo-latn-br',
  gvp: 'gvp-latn-br',
  gvr: 'gvr-deva-np',
  gvs: 'gvs-latn-zz',
  gvy: 'gvy-latn-au',
  gwa: 'gwa-latn-ci',
  gwb: 'gwb-latn-ng',
  gwc: 'gwc-arab-zz',
  gwd: 'gwd-latn-et',
  gwe: 'gwe-latn-tz',
  gwf: 'gwf-arab-pk',
  gwg: 'gwg-latn-ng',
  gwi: 'gwi-latn-ca',
  gwj: 'gwj-latn-bw',
  gwm: 'gwm-latn-au',
  gwn: 'gwn-latn-ng',
  gwr: 'gwr-latn-ug',
  gwt: 'gwt-arab-zz',
  gwu: 'gwu-latn-au',
  gww: 'gww-latn-au',
  gwx: 'gwx-latn-gh',
  gxx: 'gxx-latn-ci',
  gyb: 'gyb-latn-pg',
  gyd: 'gyd-latn-au',
  gye: 'gye-latn-ng',
  gyf: 'gyf-latn-au',
  gyg: 'gyg-latn-cf',
  gyi: 'gyi-latn-zz',
  gyl: 'gyl-latn-et',
  'gyl-ethi': 'gyl-ethi-et',
  gym: 'gym-latn-pa',
  gyn: 'gyn-latn-gy',
  gyo: 'gyo-deva-np',
  gyr: 'gyr-latn-bo',
  gyy: 'gyy-latn-au',
  gyz: 'gyz-latn-ng',
  gza: 'gza-latn-sd',
  gzi: 'gzi-arab-ir',
  gzn: 'gzn-latn-id',
  ha: 'ha-latn-ng',
  'ha-cm': 'ha-arab-cm',
  'ha-sd': 'ha-arab-sd',
  haa: 'haa-latn-us',
  hac: 'hac-arab-ir',
  had: 'had-latn-id',
  hae: 'hae-latn-et',
  hag: 'hag-latn-zz',
  hah: 'hah-latn-pg',
  hai: 'hai-latn-ca',
  haj: 'haj-latn-in',
  'haj-beng': 'haj-beng-in',
  hak: 'hak-hans-cn',
  hal: 'hal-latn-vn',
  ham: 'ham-latn-zz',
  han: 'han-latn-tz',
  hao: 'hao-latn-pg',
  hap: 'hap-latn-id',
  haq: 'haq-latn-tz',
  har: 'har-ethi-et',
  'har-arab': 'har-arab-et',
  'har-latn': 'har-latn-et',
  has: 'has-latn-ca',
  hav: 'hav-latn-cd',
  haw: 'haw-latn-us',
  hax: 'hax-latn-ca',
  hay: 'hay-latn-tz',
  haz: 'haz-arab-af',
  hba: 'hba-latn-cd',
  hbb: 'hbb-latn-zz',
  hbn: 'hbn-latn-sd',
  hbo: 'hbo-hebr-il',
  hbu: 'hbu-latn-tl',
  hch: 'hch-latn-mx',
  hdy: 'hdy-ethi-zz',
  he: 'he-hebr-il',
  hed: 'hed-latn-td',
  heg: 'heg-latn-id',
  heh: 'heh-latn-tz',
  hei: 'hei-latn-ca',
  hem: 'hem-latn-cd',
  hgm: 'hgm-latn-na',
  hgw: 'hgw-latn-pg',
  hhi: 'hhi-latn-pg',
  hhr: 'hhr-latn-sn',
  hhy: 'hhy-latn-zz',
  hi: 'hi-deva-in',
  'hi-latn': 'hi-latn-in',
  hia: 'hia-latn-zz',
  hib: 'hib-latn-pe',
  hid: 'hid-latn-us',
  hif: 'hif-latn-fj',
  hig: 'hig-latn-zz',
  hih: 'hih-latn-zz',
  hii: 'hii-takr-in',
  'hii-deva': 'hii-deva-in',
  hij: 'hij-latn-cm',
  hik: 'hik-latn-id',
  hil: 'hil-latn-ph',
  hio: 'hio-latn-bw',
  hir: 'hir-latn-br',
  hit: 'hit-xsux-tr',
  hiw: 'hiw-latn-vu',
  hix: 'hix-latn-br',
  hji: 'hji-latn-id',
  hka: 'hka-latn-tz',
  hke: 'hke-latn-cd',
  hkh: 'hkh-arab-in',
  'hkh-deva': 'hkh-deva-in',
  'hkh-latn': 'hkh-latn-in',
  hkk: 'hkk-latn-pg',
  hla: 'hla-latn-zz',
  hlb: 'hlb-deva-in',
  hld: 'hld-latn-vn',
  hlt: 'hlt-latn-mm',
  hlu: 'hlu-hluw-tr',
  hma: 'hma-latn-cn',
  hmb: 'hmb-latn-ml',
  hmd: 'hmd-plrd-cn',
  hmf: 'hmf-latn-vn',
  hmj: 'hmj-bopo-cn',
  hmm: 'hmm-latn-cn',
  hmn: 'hmn-latn-cn',
  'hmn-bopo': 'hmn-bopo-cn',
  'hmn-hmng': 'hmn-hmng-cn',
  hmp: 'hmp-latn-cn',
  hmq: 'hmq-bopo-cn',
  hmr: 'hmr-latn-in',
  hms: 'hms-latn-cn',
  hmt: 'hmt-latn-zz',
  hmu: 'hmu-latn-id',
  hmv: 'hmv-latn-vn',
  hmw: 'hmw-latn-cn',
  hmy: 'hmy-latn-cn',
  hmz: 'hmz-latn-cn',
  'hmz-plrd': 'hmz-plrd-cn',
  hna: 'hna-latn-cm',
  hnd: 'hnd-arab-pk',
  hne: 'hne-deva-in',
  hng: 'hng-latn-ao',
  hnh: 'hnh-latn-bw',
  hni: 'hni-latn-cn',
  hnj: 'hnj-hmnp-us',
  'hnj-au': 'hnj-laoo-au',
  'hnj-cn': 'hnj-laoo-cn',
  'hnj-fr': 'hnj-laoo-fr',
  'hnj-gf': 'hnj-laoo-gf',
  'hnj-la': 'hnj-laoo-la',
  'hnj-laoo': 'hnj-laoo-la',
  'hnj-mm': 'hnj-laoo-mm',
  'hnj-sr': 'hnj-laoo-sr',
  'hnj-th': 'hnj-laoo-th',
  'hnj-us': 'hnj-hmnp-us',
  'hnj-vn': 'hnj-laoo-vn',
  hnn: 'hnn-latn-ph',
  hno: 'hno-arab-pk',
  hns: 'hns-latn-sr',
  ho: 'ho-latn-pg',
  hoa: 'hoa-latn-sb',
  hob: 'hob-latn-pg',
  hoc: 'hoc-deva-in',
  hod: 'hod-latn-ng',
  hoe: 'hoe-latn-ng',
  hoh: 'hoh-arab-om',
  hoi: 'hoi-latn-us',
  hoj: 'hoj-deva-in',
  hol: 'hol-latn-ao',
  hom: 'hom-latn-ss',
  hoo: 'hoo-latn-cd',
  hop: 'hop-latn-us',
  hor: 'hor-latn-td',
  hot: 'hot-latn-zz',
  hov: 'hov-latn-id',
  how: 'how-hani-cn',
  hoy: 'hoy-deva-in',
  hpo: 'hpo-mymr-mm',
  hr: 'hr-latn-hr',
  hra: 'hra-latn-in',
  hrc: 'hrc-latn-pg',
  hre: 'hre-latn-vn',
  hrk: 'hrk-latn-id',
  hrm: 'hrm-latn-cn',
  'hrm-hmng': 'hrm-hmng-cn',
  hro: 'hro-latn-vn',
  hrp: 'hrp-latn-au',
  hrt: 'hrt-syrc-tr',
  hru: 'hru-latn-in',
  hrw: 'hrw-latn-pg',
  hrx: 'hrx-latn-br',
  hrz: 'hrz-arab-ir',
  hsb: 'hsb-latn-de',
  hsn: 'hsn-hans-cn',
  hss: 'hss-arab-om',
  ht: 'ht-latn-ht',
  hti: 'hti-latn-id',
  hto: 'hto-latn-co',
  hts: 'hts-latn-tz',
  htu: 'htu-latn-id',
  htx: 'htx-xsux-tr',
  hu: 'hu-latn-hu',
  hub: 'hub-latn-pe',
  huc: 'huc-latn-bw',
  hud: 'hud-latn-id',
  hue: 'hue-latn-mx',
  huf: 'huf-latn-pg',
  hug: 'hug-latn-pe',
  huh: 'huh-latn-cl',
  hui: 'hui-latn-zz',
  huk: 'huk-latn-id',
  hul: 'hul-latn-pg',
  hum: 'hum-latn-cd',
  hup: 'hup-latn-us',
  hur: 'hur-latn-ca',
  hus: 'hus-latn-mx',
  hut: 'hut-deva-np',
  'hut-tibt': 'hut-tibt-np',
  huu: 'huu-latn-pe',
  huv: 'huv-latn-mx',
  huw: 'huw-latn-id',
  hux: 'hux-latn-pe',
  huy: 'huy-hebr-il',
  huz: 'huz-cyrl-ru',
  hvc: 'hvc-latn-ht',
  hve: 'hve-latn-mx',
  hvk: 'hvk-latn-nc',
  hvn: 'hvn-latn-id',
  hvv: 'hvv-latn-mx',
  hwa: 'hwa-latn-ci',
  hwc: 'hwc-latn-us',
  hwo: 'hwo-latn-ng',
  hy: 'hy-armn-am',
  hya: 'hya-latn-cm',
  hyw: 'hyw-armn-am',
  hz: 'hz-latn-na',
  ia: 'ia-latn-001',
  iai: 'iai-latn-nc',
  ian: 'ian-latn-zz',
  iar: 'iar-latn-zz',
  iba: 'iba-latn-my',
  ibb: 'ibb-latn-ng',
  ibd: 'ibd-latn-au',
  ibe: 'ibe-latn-ng',
  ibg: 'ibg-latn-ph',
  ibh: 'ibh-latn-vn',
  ibl: 'ibl-latn-ph',
  ibm: 'ibm-latn-ng',
  ibn: 'ibn-latn-ng',
  ibr: 'ibr-latn-ng',
  ibu: 'ibu-latn-id',
  iby: 'iby-latn-zz',
  ica: 'ica-latn-zz',
  ich: 'ich-latn-zz',
  icr: 'icr-latn-co',
  id: 'id-latn-id',
  ida: 'ida-latn-ke',
  idb: 'idb-latn-in',
  idc: 'idc-latn-ng',
  idd: 'idd-latn-zz',
  ide: 'ide-latn-ng',
  idi: 'idi-latn-zz',
  idr: 'idr-latn-ss',
  ids: 'ids-latn-ng',
  idt: 'idt-latn-tl',
  idu: 'idu-latn-zz',
  ie: 'ie-latn-001',
  ifa: 'ifa-latn-ph',
  ifb: 'ifb-latn-ph',
  ife: 'ife-latn-tg',
  iff: 'iff-latn-vu',
  ifk: 'ifk-latn-ph',
  ifm: 'ifm-latn-cg',
  ifu: 'ifu-latn-ph',
  ify: 'ify-latn-ph',
  ig: 'ig-latn-ng',
  igb: 'igb-latn-zz',
  ige: 'ige-latn-zz',
  igg: 'igg-latn-pg',
  igl: 'igl-latn-ng',
  igm: 'igm-latn-pg',
  ign: 'ign-latn-bo',
  igo: 'igo-latn-pg',
  igs: 'igs-latn-001',
  'igs-grek': 'igs-grek-001',
  igw: 'igw-latn-ng',
  ihb: 'ihb-latn-id',
  ihi: 'ihi-latn-ng',
  ihp: 'ihp-latn-id',
  ihw: 'ihw-latn-au',
  ii: 'ii-yiii-cn',
  iin: 'iin-latn-au',
  ijc: 'ijc-latn-ng',
  ije: 'ije-latn-ng',
  ijj: 'ijj-latn-zz',
  ijn: 'ijn-latn-ng',
  ijs: 'ijs-latn-ng',
  ik: 'ik-latn-us',
  iki: 'iki-latn-ng',
  ikk: 'ikk-latn-zz',
  ikl: 'ikl-latn-ng',
  iko: 'iko-latn-ng',
  ikp: 'ikp-latn-ng',
  ikr: 'ikr-latn-au',
  ikt: 'ikt-latn-ca',
  'ikt-cans': 'ikt-cans-ca',
  ikv: 'ikv-latn-ng',
  ikw: 'ikw-latn-zz',
  ikx: 'ikx-latn-zz',
  ikz: 'ikz-latn-tz',
  ila: 'ila-latn-id',
  ilb: 'ilb-latn-zm',
  ilg: 'ilg-latn-au',
  ili: 'ili-latn-cn',
  'ili-arab': 'ili-arab-cn',
  'ili-cyrl': 'ili-cyrl-kz',
  ilk: 'ilk-latn-ph',
  ilm: 'ilm-latn-my',
  ilo: 'ilo-latn-ph',
  ilp: 'ilp-latn-ph',
  ilu: 'ilu-latn-id',
  ilv: 'ilv-latn-ng',
  imi: 'imi-latn-pg',
  iml: 'iml-latn-us',
  imn: 'imn-latn-pg',
  imo: 'imo-latn-zz',
  imr: 'imr-latn-id',
  ims: 'ims-latn-it',
  imt: 'imt-latn-ss',
  imy: 'imy-lyci-tr',
  in: 'in-latn-id',
  inb: 'inb-latn-co',
  ing: 'ing-latn-us',
  inh: 'inh-cyrl-ru',
  inj: 'inj-latn-co',
  inn: 'inn-latn-ph',
  ino: 'ino-latn-pg',
  inp: 'inp-latn-pe',
  int: 'int-mymr-mm',
  io: 'io-latn-001',
  ior: 'ior-ethi-et',
  iou: 'iou-latn-zz',
  iow: 'iow-latn-us',
  ipi: 'ipi-latn-pg',
  ipo: 'ipo-latn-pg',
  iqu: 'iqu-latn-pe',
  iqw: 'iqw-latn-ng',
  ire: 'ire-latn-id',
  irh: 'irh-latn-id',
  iri: 'iri-latn-zz',
  irk: 'irk-latn-tz',
  irn: 'irn-latn-br',
  iru: 'iru-taml-in',
  'iru-mlym': 'iru-mlym-in',
  irx: 'irx-latn-id',
  iry: 'iry-latn-ph',
  is: 'is-latn-is',
  isa: 'isa-latn-pg',
  isc: 'isc-latn-pe',
  isd: 'isd-latn-ph',
  ish: 'ish-latn-ng',
  isi: 'isi-latn-ng',
  isk: 'isk-arab-af',
  'isk-cyrl': 'isk-cyrl-tj',
  ism: 'ism-latn-id',
  isn: 'isn-latn-tz',
  iso: 'iso-latn-ng',
  ist: 'ist-latn-hr',
  isu: 'isu-latn-cm',
  it: 'it-latn-it',
  itb: 'itb-latn-ph',
  itd: 'itd-latn-id',
  ite: 'ite-latn-bo',
  iti: 'iti-latn-ph',
  itk: 'itk-hebr-it',
  itl: 'itl-cyrl-ru',
  itm: 'itm-latn-ng',
  ito: 'ito-latn-bo',
  itr: 'itr-latn-pg',
  its: 'its-latn-ng',
  itt: 'itt-latn-ph',
  itv: 'itv-latn-ph',
  itw: 'itw-latn-ng',
  itx: 'itx-latn-id',
  ity: 'ity-latn-ph',
  itz: 'itz-latn-gt',
  iu: 'iu-cans-ca',
  ium: 'ium-latn-cn',
  'ium-hani': 'ium-hani-cn',
  'ium-laoo': 'ium-laoo-la',
  'ium-thai': 'ium-thai-th',
  ivb: 'ivb-latn-ph',
  ivv: 'ivv-latn-ph',
  iw: 'iw-hebr-il',
  iwk: 'iwk-latn-ph',
  iwm: 'iwm-latn-zz',
  iwo: 'iwo-latn-id',
  iws: 'iws-latn-zz',
  ixc: 'ixc-latn-mx',
  ixl: 'ixl-latn-gt',
  iya: 'iya-latn-ng',
  iyo: 'iyo-latn-cm',
  iyx: 'iyx-latn-cg',
  izh: 'izh-latn-ru',
  izi: 'izi-latn-zz',
  izr: 'izr-latn-ng',
  izz: 'izz-latn-ng',
  ja: 'ja-jpan-jp',
  jaa: 'jaa-latn-br',
  jab: 'jab-latn-zz',
  jac: 'jac-latn-gt',
  jad: 'jad-arab-gn',
  jae: 'jae-latn-pg',
  jaf: 'jaf-latn-ng',
  jah: 'jah-latn-my',
  jaj: 'jaj-latn-sb',
  jak: 'jak-latn-my',
  jal: 'jal-latn-id',
  jam: 'jam-latn-jm',
  jan: 'jan-latn-au',
  jao: 'jao-latn-au',
  jaq: 'jaq-latn-id',
  jar: 'jar-latn-zz',
  jas: 'jas-latn-nc',
  jat: 'jat-arab-af',
  jau: 'jau-latn-id',
  jax: 'jax-latn-id',
  jay: 'jay-latn-au',
  jaz: 'jaz-latn-nc',
  jbe: 'jbe-hebr-il',
  jbi: 'jbi-latn-au',
  jbj: 'jbj-latn-id',
  jbk: 'jbk-latn-pg',
  jbm: 'jbm-latn-ng',
  jbn: 'jbn-arab-ly',
  jbo: 'jbo-latn-001',
  jbr: 'jbr-latn-id',
  jbt: 'jbt-latn-br',
  jbu: 'jbu-latn-zz',
  jbw: 'jbw-latn-au',
  jct: 'jct-cyrl-ua',
  'jct-latn': 'jct-latn-ua',
  jda: 'jda-tibt-in',
  jdg: 'jdg-arab-pk',
  jdt: 'jdt-cyrl-ru',
  'jdt-hebr': 'jdt-hebr-ru',
  'jdt-latn': 'jdt-latn-az',
  jeb: 'jeb-latn-pe',
  jee: 'jee-deva-np',
  jeh: 'jeh-latn-vn',
  'jeh-laoo': 'jeh-laoo-la',
  jei: 'jei-latn-id',
  jek: 'jek-latn-ci',
  jel: 'jel-latn-id',
  jen: 'jen-latn-zz',
  jer: 'jer-latn-ng',
  jet: 'jet-latn-pg',
  jeu: 'jeu-latn-td',
  jgb: 'jgb-latn-cd',
  jge: 'jge-geor-ge',
  'jge-hebr': 'jge-hebr-il',
  jgk: 'jgk-latn-zz',
  jgo: 'jgo-latn-cm',
  jhi: 'jhi-latn-my',
  ji: 'ji-hebr-ua',
  jia: 'jia-latn-cm',
  jib: 'jib-latn-zz',
  jic: 'jic-latn-hn',
  jid: 'jid-latn-ng',
  jie: 'jie-latn-ng',
  jig: 'jig-latn-au',
  jil: 'jil-latn-pg',
  jim: 'jim-latn-cm',
  jit: 'jit-latn-tz',
  jiu: 'jiu-latn-cn',
  jiv: 'jiv-latn-ec',
  jiy: 'jiy-latn-cn',
  jje: 'jje-hang-kr',
  jjr: 'jjr-latn-ng',
  jka: 'jka-latn-id',
  jkm: 'jkm-mymr-mm',
  'jkm-brai': 'jkm-brai-mm',
  'jkm-latn': 'jkm-latn-mm',
  jko: 'jko-latn-pg',
  jku: 'jku-latn-ng',
  jle: 'jle-latn-sd',
  jma: 'jma-latn-pg',
  jmb: 'jmb-latn-ng',
  jmc: 'jmc-latn-tz',
  jmd: 'jmd-latn-id',
  jmi: 'jmi-latn-ng',
  jml: 'jml-deva-np',
  jmn: 'jmn-latn-mm',
  jmr: 'jmr-latn-gh',
  jms: 'jms-latn-ng',
  jmw: 'jmw-latn-pg',
  jmx: 'jmx-latn-mx',
  jna: 'jna-takr-in',
  jnd: 'jnd-arab-pk',
  jng: 'jng-latn-au',
  jni: 'jni-latn-ng',
  jnj: 'jnj-latn-et',
  'jnj-ethi': 'jnj-ethi-et',
  jnl: 'jnl-deva-in',
  jns: 'jns-deva-in',
  'jns-latn': 'jns-latn-in',
  'jns-takr': 'jns-takr-in',
  job: 'job-latn-cd',
  jod: 'jod-latn-ci',
  jog: 'jog-arab-pk',
  jor: 'jor-latn-bo',
  jow: 'jow-latn-ml',
  jpa: 'jpa-hebr-ps',
  jpr: 'jpr-hebr-il',
  jqr: 'jqr-latn-pe',
  jra: 'jra-latn-zz',
  jrr: 'jrr-latn-ng',
  jrt: 'jrt-latn-ng',
  jru: 'jru-latn-ve',
  jua: 'jua-latn-br',
  jub: 'jub-latn-ng',
  jud: 'jud-latn-ci',
  juh: 'juh-latn-ng',
  jui: 'jui-latn-au',
  juk: 'juk-latn-ng',
  jul: 'jul-deva-np',
  jum: 'jum-latn-sd',
  jun: 'jun-orya-in',
  juo: 'juo-latn-ng',
  jup: 'jup-latn-br',
  jur: 'jur-latn-br',
  jut: 'jut-latn-dk',
  juu: 'juu-latn-ng',
  juw: 'juw-latn-ng',
  juy: 'juy-orya-in',
  jv: 'jv-latn-id',
  jvd: 'jvd-latn-id',
  jvn: 'jvn-latn-sr',
  jw: 'jw-latn-id',
  jwi: 'jwi-latn-gh',
  jya: 'jya-tibt-cn',
  jye: 'jye-hebr-il',
  jyy: 'jyy-latn-td',
  ka: 'ka-geor-ge',
  kaa: 'kaa-cyrl-uz',
  kab: 'kab-latn-dz',
  kac: 'kac-latn-mm',
  kad: 'kad-latn-zz',
  kag: 'kag-latn-my',
  kah: 'kah-latn-cf',
  kai: 'kai-latn-zz',
  kaj: 'kaj-latn-ng',
  kak: 'kak-latn-ph',
  kam: 'kam-latn-ke',
  kao: 'kao-latn-ml',
  kap: 'kap-cyrl-ru',
  kaq: 'kaq-latn-pe',
  kav: 'kav-latn-br',
  kaw: 'kaw-kawi-id',
  kax: 'kax-latn-id',
  kay: 'kay-latn-br',
  kba: 'kba-latn-au',
  kbb: 'kbb-latn-br',
  kbc: 'kbc-latn-br',
  kbd: 'kbd-cyrl-ru',
  kbe: 'kbe-latn-au',
  kbh: 'kbh-latn-co',
  kbi: 'kbi-latn-id',
  kbj: 'kbj-latn-cd',
  kbk: 'kbk-latn-pg',
  kbl: 'kbl-latn-td',
  kbm: 'kbm-latn-zz',
  kbn: 'kbn-latn-cf',
  kbo: 'kbo-latn-ss',
  kbp: 'kbp-latn-zz',
  kbq: 'kbq-latn-zz',
  kbr: 'kbr-latn-et',
  'kbr-ethi': 'kbr-ethi-et',
  kbs: 'kbs-latn-ga',
  kbt: 'kbt-latn-pg',
  kbu: 'kbu-arab-pk',
  kbv: 'kbv-latn-id',
  kbw: 'kbw-latn-pg',
  kbx: 'kbx-latn-zz',
  kby: 'kby-arab-ne',
  kbz: 'kbz-latn-ng',
  kca: 'kca-cyrl-ru',
  kcb: 'kcb-latn-pg',
  kcc: 'kcc-latn-ng',
  kcd: 'kcd-latn-id',
  kce: 'kce-latn-ng',
  kcf: 'kcf-latn-ng',
  kcg: 'kcg-latn-ng',
  kch: 'kch-latn-ng',
  kci: 'kci-latn-ng',
  kcj: 'kcj-latn-gw',
  kck: 'kck-latn-zw',
  kcl: 'kcl-latn-zz',
  kcm: 'kcm-latn-cf',
  kcn: 'kcn-latn-ug',
  kco: 'kco-latn-pg',
  kcp: 'kcp-latn-sd',
  kcq: 'kcq-latn-ng',
  kcs: 'kcs-latn-ng',
  kct: 'kct-latn-zz',
  kcu: 'kcu-latn-tz',
  kcv: 'kcv-latn-cd',
  kcw: 'kcw-latn-cd',
  kcz: 'kcz-latn-tz',
  kda: 'kda-latn-au',
  kdc: 'kdc-latn-tz',
  kdd: 'kdd-latn-au',
  kde: 'kde-latn-tz',
  kdf: 'kdf-latn-pg',
  kdg: 'kdg-latn-cd',
  kdh: 'kdh-latn-tg',
  kdi: 'kdi-latn-ug',
  kdj: 'kdj-latn-ug',
  kdk: 'kdk-latn-nc',
  kdl: 'kdl-latn-zz',
  kdm: 'kdm-latn-ng',
  kdn: 'kdn-latn-zw',
  kdp: 'kdp-latn-ng',
  kdq: 'kdq-beng-in',
  kdr: 'kdr-latn-lt',
  'kdr-cyrl': 'kdr-cyrl-ua',
  kdt: 'kdt-thai-th',
  kdw: 'kdw-latn-id',
  kdx: 'kdx-latn-ng',
  kdy: 'kdy-latn-id',
  kdz: 'kdz-latn-cm',
  kea: 'kea-latn-cv',
  keb: 'keb-latn-ga',
  kec: 'kec-latn-sd',
  ked: 'ked-latn-tz',
  kee: 'kee-latn-us',
  kef: 'kef-latn-tg',
  keg: 'keg-latn-sd',
  keh: 'keh-latn-pg',
  kei: 'kei-latn-id',
  kek: 'kek-latn-gt',
  kel: 'kel-latn-cd',
  kem: 'kem-latn-tl',
  ken: 'ken-latn-cm',
  keo: 'keo-latn-ug',
  ker: 'ker-latn-td',
  kes: 'kes-latn-ng',
  ket: 'ket-cyrl-ru',
  keu: 'keu-latn-tg',
  kew: 'kew-latn-pg',
  kex: 'kex-deva-in',
  'kex-gujr': 'kex-gujr-in',
  key: 'key-telu-in',
  kez: 'kez-latn-zz',
  kfa: 'kfa-knda-in',
  kfb: 'kfb-deva-in',
  kfc: 'kfc-telu-in',
  kfd: 'kfd-knda-in',
  kfe: 'kfe-taml-in',
  kff: 'kff-latn-in',
  'kff-deva': 'kff-deva-in',
  'kff-orya': 'kff-orya-in',
  'kff-telu': 'kff-telu-in',
  kfh: 'kfh-mlym-in',
  kfi: 'kfi-taml-in',
  'kfi-knda': 'kfi-knda-in',
  kfk: 'kfk-deva-in',
  'kfk-takr': 'kfk-takr-in',
  kfl: 'kfl-latn-cm',
  kfm: 'kfm-arab-ir',
  kfn: 'kfn-latn-cm',
  kfo: 'kfo-latn-ci',
  kfp: 'kfp-deva-in',
  kfq: 'kfq-deva-in',
  kfr: 'kfr-deva-in',
  kfs: 'kfs-deva-in',
  kfv: 'kfv-latn-in',
  kfw: 'kfw-latn-in',
  kfx: 'kfx-deva-in',
  'kfx-takr': 'kfx-takr-in',
  kfy: 'kfy-deva-in',
  kfz: 'kfz-latn-bf',
  kg: 'kg-latn-cd',
  kga: 'kga-latn-ci',
  kgb: 'kgb-latn-id',
  kge: 'kge-latn-id',
  kgf: 'kgf-latn-zz',
  kgj: 'kgj-deva-np',
  kgk: 'kgk-latn-br',
  kgl: 'kgl-latn-au',
  kgm: 'kgm-latn-br',
  kgo: 'kgo-latn-sd',
  kgp: 'kgp-latn-br',
  kgq: 'kgq-latn-id',
  kgr: 'kgr-latn-id',
  kgs: 'kgs-latn-au',
  kgt: 'kgt-latn-ng',
  kgu: 'kgu-latn-pg',
  kgv: 'kgv-latn-id',
  kgw: 'kgw-latn-id',
  kgx: 'kgx-latn-id',
  kgy: 'kgy-deva-np',
  kha: 'kha-latn-in',
  khb: 'khb-talu-cn',
  khc: 'khc-latn-id',
  khd: 'khd-latn-id',
  khe: 'khe-latn-id',
  khf: 'khf-thai-la',
  khg: 'khg-tibt-cn',
  khh: 'khh-latn-id',
  khj: 'khj-latn-ng',
  khl: 'khl-latn-pg',
  khn: 'khn-deva-in',
  khp: 'khp-latn-id',
  khq: 'khq-latn-ml',
  khr: 'khr-latn-in',
  'khr-deva': 'khr-deva-in',
  khs: 'khs-latn-zz',
  kht: 'kht-mymr-in',
  khu: 'khu-latn-ao',
  khv: 'khv-cyrl-ru',
  khw: 'khw-arab-pk',
  khx: 'khx-latn-cd',
  khy: 'khy-latn-cd',
  khz: 'khz-latn-zz',
  ki: 'ki-latn-ke',
  kia: 'kia-latn-td',
  kib: 'kib-latn-sd',
  kic: 'kic-latn-us',
  kid: 'kid-latn-cm',
  kie: 'kie-latn-td',
  kif: 'kif-deva-np',
  kig: 'kig-latn-id',
  kih: 'kih-latn-pg',
  kij: 'kij-latn-zz',
  kil: 'kil-latn-ng',
  kim: 'kim-cyrl-ru',
  kio: 'kio-latn-us',
  kip: 'kip-deva-np',
  kiq: 'kiq-latn-id',
  kis: 'kis-latn-pg',
  kit: 'kit-latn-pg',
  kiu: 'kiu-latn-tr',
  kiv: 'kiv-latn-tz',
  kiw: 'kiw-latn-zz',
  kix: 'kix-latn-in',
  kiy: 'kiy-latn-id',
  kiz: 'kiz-latn-tz',
  kj: 'kj-latn-na',
  kja: 'kja-latn-id',
  kjb: 'kjb-latn-gt',
  kjc: 'kjc-latn-id',
  kjd: 'kjd-latn-zz',
  kje: 'kje-latn-id',
  kjg: 'kjg-laoo-la',
  kjh: 'kjh-cyrl-ru',
  kji: 'kji-latn-sb',
  kjj: 'kjj-latn-az',
  kjk: 'kjk-latn-id',
  kjl: 'kjl-deva-np',
  kjm: 'kjm-latn-vn',
  kjn: 'kjn-latn-au',
  kjo: 'kjo-deva-in',
  kjp: 'kjp-mymr-mm',
  'kjp-thai': 'kjp-thai-th',
  kjq: 'kjq-latn-us',
  kjr: 'kjr-latn-id',
  kjs: 'kjs-latn-zz',
  kjt: 'kjt-thai-th',
  kju: 'kju-latn-us',
  kjx: 'kjx-latn-pg',
  kjy: 'kjy-latn-zz',
  kk: 'kk-cyrl-kz',
  'kk-af': 'kk-arab-af',
  'kk-arab': 'kk-arab-cn',
  'kk-cn': 'kk-arab-cn',
  'kk-ir': 'kk-arab-ir',
  'kk-mn': 'kk-arab-mn',
  kka: 'kka-latn-ng',
  kkb: 'kkb-latn-id',
  kkc: 'kkc-latn-zz',
  kkd: 'kkd-latn-ng',
  kke: 'kke-latn-gn',
  'kke-arab': 'kke-arab-gn',
  kkf: 'kkf-tibt-in',
  kkg: 'kkg-latn-ph',
  kkh: 'kkh-lana-mm',
  kki: 'kki-latn-tz',
  kkj: 'kkj-latn-cm',
  kkk: 'kkk-latn-sb',
  kkl: 'kkl-latn-id',
  kkm: 'kkm-latn-ng',
  kko: 'kko-latn-sd',
  kkp: 'kkp-latn-au',
  kkq: 'kkq-latn-cd',
  kkr: 'kkr-latn-ng',
  kks: 'kks-latn-ng',
  kkt: 'kkt-deva-np',
  kku: 'kku-latn-ng',
  kkv: 'kkv-latn-id',
  kkw: 'kkw-latn-cg',
  kkx: 'kkx-latn-id',
  kky: 'kky-latn-au',
  kkz: 'kkz-latn-ca',
  kl: 'kl-latn-gl',
  kla: 'kla-latn-us',
  klb: 'klb-latn-mx',
  klc: 'klc-latn-cm',
  kld: 'kld-latn-au',
  kle: 'kle-deva-np',
  klf: 'klf-latn-td',
  klg: 'klg-latn-ph',
  klh: 'klh-latn-pg',
  kli: 'kli-latn-id',
  klj: 'klj-arab-ir',
  klk: 'klk-latn-ng',
  kll: 'kll-latn-ph',
  klm: 'klm-latn-pg',
  kln: 'kln-latn-ke',
  klo: 'klo-latn-ng',
  klp: 'klp-latn-pg',
  klq: 'klq-latn-zz',
  klr: 'klr-deva-np',
  kls: 'kls-latn-pk',
  'kls-arab': 'kls-arab-pk',
  klt: 'klt-latn-zz',
  klu: 'klu-latn-lr',
  klv: 'klv-latn-vu',
  klw: 'klw-latn-id',
  klx: 'klx-latn-zz',
  kly: 'kly-latn-id',
  klz: 'klz-latn-id',
  km: 'km-khmr-kh',
  kma: 'kma-latn-gh',
  kmb: 'kmb-latn-ao',
  kmc: 'kmc-latn-cn',
  'kmc-hani': 'kmc-hani-cn',
  kmd: 'kmd-latn-ph',
  kme: 'kme-latn-cm',
  kmf: 'kmf-latn-pg',
  kmg: 'kmg-latn-pg',
  kmh: 'kmh-latn-zz',
  kmi: 'kmi-latn-ng',
  kmj: 'kmj-deva-in',
  kmk: 'kmk-latn-ph',
  kml: 'kml-latn-ph',
  kmm: 'kmm-latn-in',
  kmn: 'kmn-latn-pg',
  kmo: 'kmo-latn-zz',
  kmp: 'kmp-latn-cm',
  kmq: 'kmq-latn-et',
  kms: 'kms-latn-zz',
  kmt: 'kmt-latn-id',
  kmu: 'kmu-latn-zz',
  kmv: 'kmv-latn-br',
  kmw: 'kmw-latn-zz',
  kmx: 'kmx-latn-pg',
  kmy: 'kmy-latn-ng',
  kmz: 'kmz-arab-ir',
  kn: 'kn-knda-in',
  kna: 'kna-latn-ng',
  knb: 'knb-latn-ph',
  knd: 'knd-latn-id',
  kne: 'kne-latn-ph',
  knf: 'knf-latn-gw',
  kni: 'kni-latn-ng',
  knj: 'knj-latn-gt',
  knk: 'knk-latn-sl',
  'knk-arab': 'knk-arab-sl',
  knl: 'knl-latn-id',
  knm: 'knm-latn-br',
  kno: 'kno-latn-sl',
  knp: 'knp-latn-zz',
  knq: 'knq-latn-my',
  knr: 'knr-latn-pg',
  kns: 'kns-latn-my',
  'kns-thai': 'kns-thai-th',
  knt: 'knt-latn-br',
  knu: 'knu-latn-gn',
  knv: 'knv-latn-pg',
  knw: 'knw-latn-na',
  knx: 'knx-latn-id',
  kny: 'kny-latn-cd',
  knz: 'knz-latn-bf',
  ko: 'ko-kore-kr',
  koa: 'koa-latn-pg',
  koc: 'koc-latn-ng',
  kod: 'kod-latn-id',
  koe: 'koe-latn-ss',
  kof: 'kof-latn-ng',
  kog: 'kog-latn-co',
  koh: 'koh-latn-cg',
  koi: 'koi-cyrl-ru',
  kok: 'kok-deva-in',
  kol: 'kol-latn-zz',
  koo: 'koo-latn-ug',
  kop: 'kop-latn-pg',
  koq: 'koq-latn-ga',
  kos: 'kos-latn-fm',
  kot: 'kot-latn-cm',
  kou: 'kou-latn-td',
  kov: 'kov-latn-ng',
  kow: 'kow-latn-ng',
  koy: 'koy-latn-us',
  koz: 'koz-latn-zz',
  kpa: 'kpa-latn-ng',
  kpc: 'kpc-latn-co',
  kpd: 'kpd-latn-id',
  kpe: 'kpe-latn-lr',
  kpf: 'kpf-latn-zz',
  kpg: 'kpg-latn-fm',
  kph: 'kph-latn-gh',
  kpi: 'kpi-latn-id',
  kpj: 'kpj-latn-br',
  kpk: 'kpk-latn-ng',
  kpl: 'kpl-latn-cd',
  kpm: 'kpm-latn-vn',
  kpn: 'kpn-latn-br',
  kpo: 'kpo-latn-zz',
  kpq: 'kpq-latn-id',
  kpr: 'kpr-latn-zz',
  kps: 'kps-latn-id',
  kpt: 'kpt-cyrl-ru',
  kpu: 'kpu-latn-id',
  kpw: 'kpw-latn-pg',
  kpx: 'kpx-latn-zz',
  kpy: 'kpy-cyrl-ru',
  kpz: 'kpz-latn-ug',
  kqa: 'kqa-latn-pg',
  kqb: 'kqb-latn-zz',
  kqc: 'kqc-latn-pg',
  kqd: 'kqd-syrc-iq',
  kqe: 'kqe-latn-ph',
  kqf: 'kqf-latn-zz',
  kqg: 'kqg-latn-bf',
  kqh: 'kqh-latn-tz',
  kqi: 'kqi-latn-pg',
  kqj: 'kqj-latn-pg',
  kqk: 'kqk-latn-bj',
  kql: 'kql-latn-pg',
  kqm: 'kqm-latn-ci',
  kqn: 'kqn-latn-zm',
  kqo: 'kqo-latn-lr',
  kqp: 'kqp-latn-td',
  kqq: 'kqq-latn-br',
  kqr: 'kqr-latn-my',
  kqs: 'kqs-latn-zz',
  kqt: 'kqt-latn-my',
  kqu: 'kqu-latn-za',
  kqv: 'kqv-latn-id',
  kqw: 'kqw-latn-pg',
  kqx: 'kqx-latn-cm',
  kqy: 'kqy-ethi-zz',
  kqz: 'kqz-latn-za',
  kr: 'kr-latn-zz',
  kra: 'kra-deva-np',
  krb: 'krb-latn-us',
  krc: 'krc-cyrl-ru',
  krd: 'krd-latn-tl',
  kre: 'kre-latn-br',
  krf: 'krf-latn-vu',
  krh: 'krh-latn-ng',
  kri: 'kri-latn-sl',
  krj: 'krj-latn-ph',
  krk: 'krk-cyrl-ru',
  krl: 'krl-latn-ru',
  krn: 'krn-latn-lr',
  krp: 'krp-latn-ng',
  krr: 'krr-khmr-kh',
  krs: 'krs-latn-zz',
  krt: 'krt-latn-ne',
  kru: 'kru-deva-in',
  krv: 'krv-khmr-kh',
  krw: 'krw-latn-lr',
  krx: 'krx-latn-sn',
  kry: 'kry-latn-az',
  krz: 'krz-latn-id',
  ks: 'ks-arab-in',
  ksa: 'ksa-latn-ng',
  ksb: 'ksb-latn-tz',
  ksc: 'ksc-latn-ph',
  ksd: 'ksd-latn-zz',
  kse: 'kse-latn-pg',
  ksf: 'ksf-latn-cm',
  ksg: 'ksg-latn-sb',
  ksh: 'ksh-latn-de',
  ksi: 'ksi-latn-pg',
  ksj: 'ksj-latn-zz',
  ksk: 'ksk-latn-us',
  ksl: 'ksl-latn-pg',
  ksm: 'ksm-latn-ng',
  ksn: 'ksn-latn-ph',
  kso: 'kso-latn-ng',
  ksp: 'ksp-latn-cf',
  ksq: 'ksq-latn-ng',
  ksr: 'ksr-latn-zz',
  kss: 'kss-latn-lr',
  kst: 'kst-latn-bf',
  ksu: 'ksu-mymr-in',
  ksv: 'ksv-latn-cd',
  ksw: 'ksw-mymr-mm',
  'ksw-latn': 'ksw-latn-mm',
  ksx: 'ksx-latn-id',
  ksz: 'ksz-deva-in',
  kta: 'kta-latn-vn',
  ktb: 'ktb-ethi-zz',
  ktc: 'ktc-latn-ng',
  ktd: 'ktd-latn-au',
  ktf: 'ktf-latn-cd',
  ktg: 'ktg-latn-au',
  kth: 'kth-latn-td',
  kti: 'kti-latn-id',
  ktj: 'ktj-latn-ci',
  ktk: 'ktk-latn-pg',
  ktl: 'ktl-arab-ir',
  ktm: 'ktm-latn-zz',
  ktn: 'ktn-latn-br',
  kto: 'kto-latn-zz',
  ktp: 'ktp-plrd-cn',
  ktq: 'ktq-latn-ph',
  ktr: 'ktr-latn-my',
  kts: 'kts-latn-id',
  ktt: 'ktt-latn-id',
  ktu: 'ktu-latn-cd',
  ktv: 'ktv-latn-vn',
  ktw: 'ktw-latn-us',
  ktx: 'ktx-latn-br',
  kty: 'kty-latn-cd',
  ktz: 'ktz-latn-na',
  ku: 'ku-latn-tr',
  'ku-arab': 'ku-arab-iq',
  'ku-lb': 'ku-arab-lb',
  'ku-yezi': 'ku-yezi-ge',
  kub: 'kub-latn-zz',
  kuc: 'kuc-latn-id',
  kud: 'kud-latn-zz',
  kue: 'kue-latn-zz',
  kuf: 'kuf-laoo-la',
  kug: 'kug-latn-ng',
  kuh: 'kuh-latn-ng',
  kui: 'kui-latn-br',
  kuj: 'kuj-latn-zz',
  kuk: 'kuk-latn-id',
  kul: 'kul-latn-ng',
  kum: 'kum-cyrl-ru',
  kun: 'kun-latn-zz',
  kuo: 'kuo-latn-pg',
  kup: 'kup-latn-zz',
  kuq: 'kuq-latn-br',
  kus: 'kus-latn-zz',
  kut: 'kut-latn-ca',
  kuu: 'kuu-latn-us',
  kuv: 'kuv-latn-id',
  kuw: 'kuw-latn-cf',
  kux: 'kux-latn-au',
  kuy: 'kuy-latn-au',
  kuz: 'kuz-latn-cl',
  kv: 'kv-cyrl-ru',
  kva: 'kva-cyrl-ru',
  kvb: 'kvb-latn-id',
  kvc: 'kvc-latn-pg',
  kvd: 'kvd-latn-id',
  kve: 'kve-latn-my',
  kvf: 'kvf-latn-td',
  kvg: 'kvg-latn-zz',
  kvh: 'kvh-latn-id',
  kvi: 'kvi-latn-td',
  kvj: 'kvj-latn-cm',
  kvl: 'kvl-latn-mm',
  kvm: 'kvm-latn-cm',
  kvn: 'kvn-latn-co',
  kvo: 'kvo-latn-id',
  kvp: 'kvp-latn-id',
  kvq: 'kvq-mymr-mm',
  'kvq-latn': 'kvq-latn-mm',
  kvr: 'kvr-latn-id',
  kvt: 'kvt-mymr-mm',
  kvv: 'kvv-latn-id',
  kvw: 'kvw-latn-id',
  kvx: 'kvx-arab-pk',
  kvy: 'kvy-kali-mm',
  kvz: 'kvz-latn-id',
  kw: 'kw-latn-gb',
  kwa: 'kwa-latn-br',
  kwb: 'kwb-latn-ng',
  kwc: 'kwc-latn-cg',
  kwd: 'kwd-latn-sb',
  kwe: 'kwe-latn-id',
  kwf: 'kwf-latn-sb',
  kwg: 'kwg-latn-td',
  kwh: 'kwh-latn-id',
  kwi: 'kwi-latn-co',
  kwj: 'kwj-latn-zz',
  kwk: 'kwk-latn-ca',
  kwl: 'kwl-latn-ng',
  kwm: 'kwm-latn-na',
  kwn: 'kwn-latn-na',
  kwo: 'kwo-latn-zz',
  kwp: 'kwp-latn-ci',
  kwq: 'kwq-latn-zz',
  kwr: 'kwr-latn-id',
  kws: 'kws-latn-cd',
  kwt: 'kwt-latn-id',
  kwu: 'kwu-latn-cm',
  kwv: 'kwv-latn-td',
  kww: 'kww-latn-sr',
  kwy: 'kwy-latn-cd',
  kwz: 'kwz-latn-ao',
  kxa: 'kxa-latn-zz',
  kxb: 'kxb-latn-ci',
  kxc: 'kxc-ethi-zz',
  kxd: 'kxd-latn-bn',
  'kxd-arab': 'kxd-arab-bn',
  kxe: 'kxe-latn-zz',
  kxf: 'kxf-mymr-mm',
  'kxf-latn': 'kxf-latn-mm',
  kxi: 'kxi-latn-my',
  kxj: 'kxj-latn-td',
  kxk: 'kxk-mymr-mm',
  kxl: 'kxl-deva-in',
  kxm: 'kxm-thai-th',
  kxn: 'kxn-latn-my',
  kxo: 'kxo-latn-br',
  kxp: 'kxp-arab-pk',
  kxq: 'kxq-latn-id',
  kxr: 'kxr-latn-pg',
  kxt: 'kxt-latn-pg',
  kxv: 'kxv-orya-in',
  'kxv-latn': 'kxv-latn-in',
  'kxv-telu': 'kxv-telu-in',
  kxw: 'kxw-latn-zz',
  kxx: 'kxx-latn-cg',
  kxy: 'kxy-latn-vn',
  kxz: 'kxz-latn-zz',
  ky: 'ky-cyrl-kg',
  'ky-arab': 'ky-arab-cn',
  'ky-cn': 'ky-arab-cn',
  'ky-latn': 'ky-latn-tr',
  'ky-tr': 'ky-latn-tr',
  kya: 'kya-latn-tz',
  kyb: 'kyb-latn-ph',
  kyc: 'kyc-latn-pg',
  kyd: 'kyd-latn-id',
  kye: 'kye-latn-zz',
  kyf: 'kyf-latn-ci',
  kyg: 'kyg-latn-pg',
  kyh: 'kyh-latn-us',
  kyi: 'kyi-latn-my',
  kyj: 'kyj-latn-ph',
  kyk: 'kyk-latn-ph',
  kyl: 'kyl-latn-us',
  kym: 'kym-latn-cf',
  kyn: 'kyn-latn-ph',
  kyo: 'kyo-latn-id',
  kyq: 'kyq-latn-td',
  kyr: 'kyr-latn-br',
  kys: 'kys-latn-my',
  kyt: 'kyt-latn-id',
  kyu: 'kyu-kali-mm',
  'kyu-latn': 'kyu-latn-mm',
  'kyu-mymr': 'kyu-mymr-mm',
  kyv: 'kyv-deva-np',
  kyw: 'kyw-deva-in',
  'kyw-beng': 'kyw-beng-in',
  'kyw-orya': 'kyw-orya-in',
  kyx: 'kyx-latn-zz',
  kyy: 'kyy-latn-pg',
  kyz: 'kyz-latn-br',
  kza: 'kza-latn-bf',
  kzb: 'kzb-latn-id',
  kzc: 'kzc-latn-ci',
  kzd: 'kzd-latn-id',
  kze: 'kze-latn-pg',
  kzf: 'kzf-latn-id',
  kzh: 'kzh-arab-zz',
  kzi: 'kzi-latn-my',
  kzj: 'kzj-latn-my',
  kzk: 'kzk-latn-sb',
  kzl: 'kzl-latn-id',
  kzm: 'kzm-latn-id',
  kzn: 'kzn-latn-mw',
  kzo: 'kzo-latn-ga',
  kzp: 'kzp-latn-id',
  kzr: 'kzr-latn-zz',
  kzs: 'kzs-latn-my',
  kzt: 'kzt-latn-my',
  kzu: 'kzu-latn-id',
  kzv: 'kzv-latn-id',
  kzw: 'kzw-latn-br',
  kzx: 'kzx-latn-id',
  kzy: 'kzy-latn-cd',
  kzz: 'kzz-latn-id',
  la: 'la-latn-va',
  laa: 'laa-latn-ph',
  lab: 'lab-lina-gr',
  lac: 'lac-latn-mx',
  lad: 'lad-hebr-il',
  lae: 'lae-deva-in',
  'lae-tibt': 'lae-tibt-in',
  lag: 'lag-latn-tz',
  lah: 'lah-arab-pk',
  lai: 'lai-latn-mw',
  laj: 'laj-latn-ug',
  lal: 'lal-latn-cd',
  lam: 'lam-latn-zm',
  lan: 'lan-latn-ng',
  lap: 'lap-latn-td',
  laq: 'laq-latn-vn',
  lar: 'lar-latn-gh',
  las: 'las-latn-zz',
  lau: 'lau-latn-id',
  law: 'law-latn-id',
  lax: 'lax-latn-in',
  'lax-beng': 'lax-beng-in',
  laz: 'laz-latn-pg',
  lb: 'lb-latn-lu',
  lbb: 'lbb-latn-pg',
  lbc: 'lbc-lisu-cn',
  lbe: 'lbe-cyrl-ru',
  lbf: 'lbf-deva-in',
  'lbf-tibt': 'lbf-tibt-cn',
  lbi: 'lbi-latn-cm',
  lbj: 'lbj-tibt-in',
  'lbj-arab': 'lbj-arab-in',
  lbl: 'lbl-latn-ph',
  lbm: 'lbm-deva-in',
  lbn: 'lbn-latn-la',
  'lbn-laoo': 'lbn-laoo-la',
  lbo: 'lbo-laoo-la',
  'lbo-latn': 'lbo-latn-us',
  lbq: 'lbq-latn-pg',
  lbr: 'lbr-deva-np',
  lbt: 'lbt-latn-vn',
  lbu: 'lbu-latn-zz',
  lbv: 'lbv-latn-pg',
  lbw: 'lbw-latn-id',
  lbx: 'lbx-latn-id',
  lby: 'lby-latn-au',
  lbz: 'lbz-latn-au',
  lcc: 'lcc-latn-id',
  lcd: 'lcd-latn-id',
  lce: 'lce-latn-id',
  lcf: 'lcf-latn-id',
  lch: 'lch-latn-ao',
  lcl: 'lcl-latn-id',
  lcm: 'lcm-latn-zz',
  lcp: 'lcp-thai-cn',
  lcq: 'lcq-latn-id',
  lcs: 'lcs-latn-id',
  lda: 'lda-latn-ci',
  ldb: 'ldb-latn-zz',
  ldd: 'ldd-latn-ng',
  ldg: 'ldg-latn-ng',
  ldh: 'ldh-latn-ng',
  ldi: 'ldi-latn-cg',
  ldj: 'ldj-latn-ng',
  ldk: 'ldk-latn-ng',
  ldl: 'ldl-latn-ng',
  ldm: 'ldm-latn-gn',
  ldn: 'ldn-latn-001',
  ldo: 'ldo-latn-ng',
  ldp: 'ldp-latn-ng',
  ldq: 'ldq-latn-ng',
  lea: 'lea-latn-cd',
  leb: 'leb-latn-zm',
  lec: 'lec-latn-bo',
  led: 'led-latn-zz',
  lee: 'lee-latn-zz',
  lef: 'lef-latn-gh',
  leh: 'leh-latn-zm',
  lei: 'lei-latn-pg',
  lej: 'lej-latn-cd',
  lek: 'lek-latn-pg',
  lel: 'lel-latn-cd',
  lem: 'lem-latn-zz',
  len: 'len-latn-hn',
  leo: 'leo-latn-cm',
  lep: 'lep-lepc-in',
  leq: 'leq-latn-zz',
  ler: 'ler-latn-pg',
  les: 'les-latn-cd',
  let: 'let-latn-pg',
  leu: 'leu-latn-zz',
  lev: 'lev-latn-id',
  lew: 'lew-latn-id',
  lex: 'lex-latn-id',
  ley: 'ley-latn-id',
  lez: 'lez-cyrl-ru',
  lfa: 'lfa-latn-cm',
  lfn: 'lfn-latn-001',
  'lfn-cyrl': 'lfn-cyrl-001',
  lg: 'lg-latn-ug',
  lga: 'lga-latn-sb',
  lgb: 'lgb-latn-sb',
  lgg: 'lgg-latn-zz',
  lgh: 'lgh-latn-vn',
  lgi: 'lgi-latn-id',
  lgk: 'lgk-latn-vu',
  lgl: 'lgl-latn-sb',
  lgm: 'lgm-latn-cd',
  lgn: 'lgn-latn-et',
  lgo: 'lgo-latn-ss',
  lgq: 'lgq-latn-gh',
  lgr: 'lgr-latn-sb',
  lgt: 'lgt-latn-pg',
  lgu: 'lgu-latn-sb',
  lgz: 'lgz-latn-cd',
  lha: 'lha-latn-vn',
  lhh: 'lhh-latn-id',
  lhi: 'lhi-latn-cn',
  lhm: 'lhm-deva-np',
  lhn: 'lhn-latn-my',
  lhs: 'lhs-syrc-sy',
  lht: 'lht-latn-vu',
  lhu: 'lhu-latn-cn',
  li: 'li-latn-nl',
  lia: 'lia-latn-zz',
  lib: 'lib-latn-pg',
  lic: 'lic-latn-cn',
  lid: 'lid-latn-zz',
  lie: 'lie-latn-cd',
  lif: 'lif-deva-np',
  'lif-limb': 'lif-limb-in',
  lig: 'lig-latn-zz',
  lih: 'lih-latn-zz',
  lij: 'lij-latn-it',
  lik: 'lik-latn-cd',
  lil: 'lil-latn-ca',
  lio: 'lio-latn-id',
  lip: 'lip-latn-gh',
  liq: 'liq-latn-et',
  lir: 'lir-latn-lr',
  lis: 'lis-lisu-cn',
  liu: 'liu-latn-sd',
  liv: 'liv-latn-lv',
  liw: 'liw-latn-id',
  lix: 'lix-latn-id',
  liy: 'liy-latn-cf',
  liz: 'liz-latn-cd',
  lja: 'lja-latn-au',
  lje: 'lje-latn-id',
  lji: 'lji-latn-id',
  ljl: 'ljl-latn-id',
  ljp: 'ljp-latn-id',
  ljw: 'ljw-latn-au',
  ljx: 'ljx-latn-au',
  lka: 'lka-latn-tl',
  lkb: 'lkb-latn-ke',
  lkc: 'lkc-latn-vn',
  lkd: 'lkd-latn-br',
  lke: 'lke-latn-ug',
  lkh: 'lkh-tibt-bt',
  lki: 'lki-arab-ir',
  lkj: 'lkj-latn-my',
  lkl: 'lkl-latn-pg',
  lkm: 'lkm-latn-au',
  lkn: 'lkn-latn-vu',
  lko: 'lko-latn-ke',
  lkr: 'lkr-latn-ss',
  lks: 'lks-latn-ke',
  lkt: 'lkt-latn-us',
  lku: 'lku-latn-au',
  lky: 'lky-latn-ss',
  lla: 'lla-latn-ng',
  llb: 'llb-latn-mz',
  llc: 'llc-latn-gn',
  lld: 'lld-latn-it',
  lle: 'lle-latn-zz',
  llf: 'llf-latn-pg',
  llg: 'llg-latn-id',
  lli: 'lli-latn-cg',
  llj: 'llj-latn-au',
  llk: 'llk-latn-my',
  lll: 'lll-latn-pg',
  llm: 'llm-latn-id',
  lln: 'lln-latn-zz',
  llp: 'llp-latn-vu',
  llq: 'llq-latn-id',
  llu: 'llu-latn-sb',
  llx: 'llx-latn-fj',
  lma: 'lma-latn-gn',
  lmb: 'lmb-latn-vu',
  lmc: 'lmc-latn-au',
  lmd: 'lmd-latn-sd',
  lme: 'lme-latn-td',
  lmf: 'lmf-latn-id',
  lmg: 'lmg-latn-pg',
  lmh: 'lmh-deva-np',
  lmi: 'lmi-latn-cd',
  lmj: 'lmj-latn-id',
  lmk: 'lmk-latn-in',
  'lmk-mymr': 'lmk-mymr-in',
  lml: 'lml-latn-vu',
  lmn: 'lmn-telu-in',
  lmo: 'lmo-latn-it',
  lmp: 'lmp-latn-zz',
  lmq: 'lmq-latn-id',
  lmr: 'lmr-latn-id',
  lmu: 'lmu-latn-vu',
  lmv: 'lmv-latn-fj',
  lmw: 'lmw-latn-us',
  lmx: 'lmx-latn-cm',
  lmy: 'lmy-latn-id',
  ln: 'ln-latn-cd',
  lna: 'lna-latn-cf',
  lnb: 'lnb-latn-na',
  lnd: 'lnd-latn-id',
  lnh: 'lnh-latn-my',
  lni: 'lni-latn-pg',
  lnj: 'lnj-latn-au',
  lnl: 'lnl-latn-cf',
  lnm: 'lnm-latn-pg',
  lnn: 'lnn-latn-vu',
  lns: 'lns-latn-zz',
  lnu: 'lnu-latn-zz',
  lnw: 'lnw-latn-au',
  lnz: 'lnz-latn-cd',
  lo: 'lo-laoo-la',
  loa: 'loa-latn-id',
  lob: 'lob-latn-bf',
  loc: 'loc-latn-ph',
  loe: 'loe-latn-id',
  log: 'log-latn-cd',
  loh: 'loh-latn-ss',
  loi: 'loi-latn-ci',
  loj: 'loj-latn-zz',
  lok: 'lok-latn-zz',
  lol: 'lol-latn-cd',
  lom: 'lom-latn-lr',
  lon: 'lon-latn-mw',
  loo: 'loo-latn-cd',
  lop: 'lop-latn-ng',
  loq: 'loq-latn-cd',
  lor: 'lor-latn-zz',
  los: 'los-latn-zz',
  lot: 'lot-latn-ss',
  'lot-arab': 'lot-arab-ss',
  lou: 'lou-latn-us',
  low: 'low-latn-my',
  lox: 'lox-latn-id',
  loy: 'loy-deva-np',
  'loy-tibt': 'loy-tibt-np',
  loz: 'loz-latn-zm',
  lpa: 'lpa-latn-vu',
  lpe: 'lpe-latn-id',
  lpn: 'lpn-latn-mm',
  lpo: 'lpo-plrd-cn',
  'lpo-lisu': 'lpo-lisu-cn',
  lpx: 'lpx-latn-ss',
  lqr: 'lqr-latn-ss',
  lra: 'lra-latn-my',
  lrc: 'lrc-arab-ir',
  lrg: 'lrg-latn-au',
  lri: 'lri-latn-ke',
  lrk: 'lrk-arab-pk',
  lrl: 'lrl-arab-ir',
  lrm: 'lrm-latn-ke',
  lrn: 'lrn-latn-id',
  lro: 'lro-latn-sd',
  lrt: 'lrt-latn-id',
  lrv: 'lrv-latn-vu',
  lrz: 'lrz-latn-vu',
  lsa: 'lsa-arab-ir',
  lsd: 'lsd-hebr-il',
  lse: 'lse-latn-cd',
  lsi: 'lsi-latn-mm',
  lsm: 'lsm-latn-ug',
  lsr: 'lsr-latn-pg',
  lss: 'lss-arab-pk',
  lt: 'lt-latn-lt',
  ltg: 'ltg-latn-lv',
  lth: 'lth-latn-ug',
  lti: 'lti-latn-id',
  ltn: 'ltn-latn-br',
  lto: 'lto-latn-ke',
  lts: 'lts-latn-ke',
  ltu: 'ltu-latn-id',
  lu: 'lu-latn-cd',
  lua: 'lua-latn-cd',
  luc: 'luc-latn-ug',
  lud: 'lud-latn-ru',
  lue: 'lue-latn-zm',
  luf: 'luf-latn-pg',
  lui: 'lui-latn-us',
  luj: 'luj-latn-cd',
  luk: 'luk-tibt-bt',
  lul: 'lul-latn-ss',
  lum: 'lum-latn-ao',
  lun: 'lun-latn-zm',
  luo: 'luo-latn-ke',
  lup: 'lup-latn-ga',
  luq: 'luq-latn-cu',
  lur: 'lur-latn-id',
  lus: 'lus-latn-in',
  'lus-beng': 'lus-beng-bd',
  'lus-brai': 'lus-brai-in',
  lut: 'lut-latn-us',
  luu: 'luu-deva-np',
  luv: 'luv-arab-om',
  luw: 'luw-latn-cm',
  luy: 'luy-latn-ke',
  luz: 'luz-arab-ir',
  lv: 'lv-latn-lv',
  lva: 'lva-latn-tl',
  lvi: 'lvi-latn-la',
  lvk: 'lvk-latn-sb',
  lvu: 'lvu-latn-id',
  lwa: 'lwa-latn-cd',
  lwe: 'lwe-latn-id',
  lwg: 'lwg-latn-ke',
  lwh: 'lwh-latn-vn',
  lwl: 'lwl-thai-th',
  lwm: 'lwm-thai-cn',
  lwo: 'lwo-latn-ss',
  'lwo-za': 'lwo-latn-za',
  lwt: 'lwt-latn-id',
  lww: 'lww-latn-vu',
  lxm: 'lxm-latn-pg',
  lya: 'lya-tibt-bt',
  lyn: 'lyn-latn-zm',
  lzh: 'lzh-hans-cn',
  lzl: 'lzl-latn-vu',
  lzn: 'lzn-latn-mm',
  lzz: 'lzz-latn-tr',
  maa: 'maa-latn-mx',
  mab: 'mab-latn-mx',
  mad: 'mad-latn-id',
  mae: 'mae-latn-ng',
  maf: 'maf-latn-cm',
  mag: 'mag-deva-in',
  mai: 'mai-deva-in',
  maj: 'maj-latn-mx',
  mak: 'mak-latn-id',
  mam: 'mam-latn-gt',
  man: 'man-latn-gm',
  'man-gn': 'man-nkoo-gn',
  'man-nkoo': 'man-nkoo-gn',
  maq: 'maq-latn-mx',
  mas: 'mas-latn-ke',
  mat: 'mat-latn-mx',
  mau: 'mau-latn-mx',
  mav: 'mav-latn-br',
  maw: 'maw-latn-zz',
  max: 'max-latn-id',
  maz: 'maz-latn-mx',
  mba: 'mba-latn-ph',
  mbb: 'mbb-latn-ph',
  mbc: 'mbc-latn-br',
  mbd: 'mbd-latn-ph',
  mbf: 'mbf-latn-sg',
  mbh: 'mbh-latn-zz',
  mbi: 'mbi-latn-ph',
  mbj: 'mbj-latn-br',
  mbk: 'mbk-latn-pg',
  mbl: 'mbl-latn-br',
  mbm: 'mbm-latn-cg',
  mbn: 'mbn-latn-co',
  mbo: 'mbo-latn-zz',
  mbp: 'mbp-latn-co',
  mbq: 'mbq-latn-zz',
  mbr: 'mbr-latn-co',
  mbs: 'mbs-latn-ph',
  mbt: 'mbt-latn-ph',
  mbu: 'mbu-latn-zz',
  mbv: 'mbv-latn-gn',
  mbw: 'mbw-latn-zz',
  mbx: 'mbx-latn-pg',
  mby: 'mby-arab-pk',
  mbz: 'mbz-latn-mx',
  mca: 'mca-latn-py',
  mcb: 'mcb-latn-pe',
  mcc: 'mcc-latn-pg',
  mcd: 'mcd-latn-pe',
  mce: 'mce-latn-mx',
  mcf: 'mcf-latn-pe',
  mcg: 'mcg-latn-ve',
  mch: 'mch-latn-ve',
  mci: 'mci-latn-zz',
  mcj: 'mcj-latn-ng',
  mck: 'mck-latn-ao',
  mcl: 'mcl-latn-co',
  mcm: 'mcm-latn-my',
  mcn: 'mcn-latn-td',
  mco: 'mco-latn-mx',
  mcp: 'mcp-latn-zz',
  mcq: 'mcq-latn-zz',
  mcr: 'mcr-latn-zz',
  mcs: 'mcs-latn-cm',
  mct: 'mct-latn-cm',
  mcu: 'mcu-latn-zz',
  mcv: 'mcv-latn-pg',
  mcw: 'mcw-latn-td',
  mcx: 'mcx-latn-cf',
  mcy: 'mcy-latn-pg',
  mcz: 'mcz-latn-pg',
  mda: 'mda-latn-zz',
  mdb: 'mdb-latn-pg',
  mdc: 'mdc-latn-pg',
  mdd: 'mdd-latn-cm',
  mde: 'mde-arab-zz',
  mdf: 'mdf-cyrl-ru',
  mdg: 'mdg-latn-td',
  mdh: 'mdh-latn-ph',
  mdi: 'mdi-latn-cd',
  mdj: 'mdj-latn-zz',
  mdk: 'mdk-latn-cd',
  mdm: 'mdm-latn-cd',
  mdn: 'mdn-latn-cf',
  mdp: 'mdp-latn-cd',
  mdq: 'mdq-latn-cd',
  mdr: 'mdr-latn-id',
  mds: 'mds-latn-pg',
  mdt: 'mdt-latn-cg',
  mdu: 'mdu-latn-cg',
  mdv: 'mdv-latn-mx',
  mdw: 'mdw-latn-cg',
  mdx: 'mdx-ethi-zz',
  mdy: 'mdy-ethi-et',
  'mdy-latn': 'mdy-latn-et',
  mdz: 'mdz-latn-br',
  mea: 'mea-latn-cm',
  meb: 'meb-latn-pg',
  mec: 'mec-latn-au',
  med: 'med-latn-zz',
  mee: 'mee-latn-zz',
  meh: 'meh-latn-mx',
  mej: 'mej-latn-id',
  mek: 'mek-latn-zz',
  mel: 'mel-latn-my',
  mem: 'mem-latn-au',
  men: 'men-latn-sl',
  meo: 'meo-latn-my',
  'meo-arab': 'meo-arab-my',
  mep: 'mep-latn-au',
  meq: 'meq-latn-cm',
  mer: 'mer-latn-ke',
  mes: 'mes-latn-td',
  met: 'met-latn-zz',
  meu: 'meu-latn-zz',
  mev: 'mev-latn-lr',
  mew: 'mew-latn-ng',
  mey: 'mey-latn-mr',
  'mey-arab': 'mey-arab-mr',
  mez: 'mez-latn-us',
  mfa: 'mfa-arab-th',
  mfb: 'mfb-latn-id',
  mfc: 'mfc-latn-cd',
  mfd: 'mfd-latn-cm',
  mfe: 'mfe-latn-mu',
  mff: 'mff-latn-cm',
  mfg: 'mfg-latn-gn',
  'mfg-arab': 'mfg-arab-gn',
  mfh: 'mfh-latn-cm',
  mfi: 'mfi-arab-cm',
  'mfi-latn': 'mfi-latn-cm',
  mfj: 'mfj-latn-cm',
  mfk: 'mfk-latn-cm',
  mfl: 'mfl-latn-ng',
  mfm: 'mfm-latn-ng',
  mfn: 'mfn-latn-zz',
  mfo: 'mfo-latn-zz',
  mfp: 'mfp-latn-id',
  mfq: 'mfq-latn-zz',
  mfr: 'mfr-latn-au',
  mft: 'mft-latn-pg',
  mfu: 'mfu-latn-ao',
  mfv: 'mfv-latn-gw',
  mfw: 'mfw-latn-pg',
  mfx: 'mfx-latn-et',
  'mfx-ethi': 'mfx-ethi-et',
  mfy: 'mfy-latn-mx',
  mfz: 'mfz-latn-ss',
  mg: 'mg-latn-mg',
  mgb: 'mgb-latn-td',
  mgc: 'mgc-latn-ss',
  mgd: 'mgd-latn-ss',
  'mgd-arab': 'mgd-arab-ss',
  mge: 'mge-latn-td',
  mgf: 'mgf-latn-id',
  mgg: 'mgg-latn-cm',
  mgh: 'mgh-latn-mz',
  mgi: 'mgi-latn-ng',
  mgj: 'mgj-latn-ng',
  mgk: 'mgk-latn-id',
  mgl: 'mgl-latn-zz',
  mgm: 'mgm-latn-tl',
  mgn: 'mgn-latn-cf',
  mgo: 'mgo-latn-cm',
  mgp: 'mgp-deva-np',
  mgq: 'mgq-latn-tz',
  mgr: 'mgr-latn-zm',
  mgs: 'mgs-latn-tz',
  mgt: 'mgt-latn-pg',
  mgu: 'mgu-latn-pg',
  mgv: 'mgv-latn-tz',
  mgw: 'mgw-latn-tz',
  mgy: 'mgy-latn-tz',
  mgz: 'mgz-latn-tz',
  mh: 'mh-latn-mh',
  mhb: 'mhb-latn-ga',
  mhc: 'mhc-latn-mx',
  mhd: 'mhd-latn-tz',
  mhe: 'mhe-latn-my',
  mhf: 'mhf-latn-pg',
  mhg: 'mhg-latn-au',
  mhi: 'mhi-latn-zz',
  mhj: 'mhj-arab-af',
  mhk: 'mhk-latn-cm',
  mhl: 'mhl-latn-zz',
  mhm: 'mhm-latn-mz',
  mhn: 'mhn-latn-it',
  mho: 'mho-latn-zm',
  mhp: 'mhp-latn-id',
  mhq: 'mhq-latn-us',
  mhs: 'mhs-latn-id',
  mht: 'mht-latn-ve',
  mhu: 'mhu-latn-in',
  mhw: 'mhw-latn-bw',
  mhx: 'mhx-latn-mm',
  mhy: 'mhy-latn-id',
  mhz: 'mhz-latn-id',
  mi: 'mi-latn-nz',
  mia: 'mia-latn-us',
  mib: 'mib-latn-mx',
  mic: 'mic-latn-ca',
  mid: 'mid-mand-iq',
  mie: 'mie-latn-mx',
  mif: 'mif-latn-zz',
  mig: 'mig-latn-mx',
  mih: 'mih-latn-mx',
  mii: 'mii-latn-mx',
  mij: 'mij-latn-cm',
  mik: 'mik-latn-us',
  mil: 'mil-latn-mx',
  mim: 'mim-latn-mx',
  min: 'min-latn-id',
  mio: 'mio-latn-mx',
  mip: 'mip-latn-mx',
  miq: 'miq-latn-ni',
  mir: 'mir-latn-mx',
  mit: 'mit-latn-mx',
  miu: 'miu-latn-mx',
  miw: 'miw-latn-zz',
  mix: 'mix-latn-mx',
  miy: 'miy-latn-mx',
  miz: 'miz-latn-mx',
  mjb: 'mjb-latn-tl',
  mjc: 'mjc-latn-mx',
  mjd: 'mjd-latn-us',
  mje: 'mje-latn-td',
  mjg: 'mjg-latn-cn',
  mjh: 'mjh-latn-tz',
  mji: 'mji-latn-cn',
  mjj: 'mjj-latn-pg',
  mjk: 'mjk-latn-pg',
  mjl: 'mjl-deva-in',
  'mjl-takr': 'mjl-takr-in',
  mjm: 'mjm-latn-pg',
  mjn: 'mjn-latn-pg',
  mjq: 'mjq-mlym-in',
  mjr: 'mjr-mlym-in',
  mjs: 'mjs-latn-ng',
  mjt: 'mjt-deva-in',
  'mjt-beng': 'mjt-beng-bd',
  mju: 'mju-telu-in',
  mjv: 'mjv-mlym-in',
  mjw: 'mjw-latn-in',
  mjx: 'mjx-latn-bd',
  'mjx-beng': 'mjx-beng-bd',
  mjy: 'mjy-latn-us',
  mjz: 'mjz-deva-np',
  mk: 'mk-cyrl-mk',
  mka: 'mka-latn-ci',
  mkb: 'mkb-deva-in',
  mkc: 'mkc-latn-pg',
  mke: 'mke-deva-in',
  mkf: 'mkf-latn-ng',
  mki: 'mki-arab-zz',
  mkj: 'mkj-latn-fm',
  mkk: 'mkk-latn-cm',
  mkl: 'mkl-latn-zz',
  mkm: 'mkm-thai-th',
  mkn: 'mkn-latn-id',
  mko: 'mko-latn-ng',
  mkp: 'mkp-latn-zz',
  mkr: 'mkr-latn-pg',
  mks: 'mks-latn-mx',
  mkt: 'mkt-latn-nc',
  mku: 'mku-latn-gn',
  mkv: 'mkv-latn-vu',
  mkw: 'mkw-latn-zz',
  mkx: 'mkx-latn-ph',
  mky: 'mky-latn-id',
  mkz: 'mkz-latn-tl',
  ml: 'ml-mlym-in',
  mla: 'mla-latn-vu',
  mlb: 'mlb-latn-cm',
  mlc: 'mlc-latn-vn',
  mle: 'mle-latn-zz',
  mlf: 'mlf-thai-la',
  'mlf-latn': 'mlf-latn-la',
  mlh: 'mlh-latn-pg',
  mli: 'mli-latn-id',
  mlj: 'mlj-latn-td',
  mlk: 'mlk-latn-ke',
  mll: 'mll-latn-vu',
  mln: 'mln-latn-sb',
  mlo: 'mlo-latn-sn',
  mlp: 'mlp-latn-zz',
  mlq: 'mlq-latn-sn',
  'mlq-arab': 'mlq-arab-sn',
  mlr: 'mlr-latn-cm',
  mls: 'mls-latn-sd',
  mlu: 'mlu-latn-sb',
  mlv: 'mlv-latn-vu',
  mlw: 'mlw-latn-cm',
  mlx: 'mlx-latn-vu',
  mlz: 'mlz-latn-ph',
  mma: 'mma-latn-ng',
  mmb: 'mmb-latn-id',
  mmc: 'mmc-latn-mx',
  mmd: 'mmd-latn-cn',
  'mmd-hans': 'mmd-hans-cn',
  'mmd-hant': 'mmd-hant-cn',
  mme: 'mme-latn-vu',
  mmf: 'mmf-latn-ng',
  mmg: 'mmg-latn-vu',
  mmh: 'mmh-latn-br',
  mmi: 'mmi-latn-pg',
  mmm: 'mmm-latn-vu',
  mmn: 'mmn-latn-ph',
  mmo: 'mmo-latn-zz',
  mmp: 'mmp-latn-pg',
  mmq: 'mmq-latn-pg',
  mmr: 'mmr-latn-cn',
  mmt: 'mmt-latn-pg',
  mmu: 'mmu-latn-zz',
  mmv: 'mmv-latn-br',
  mmw: 'mmw-latn-vu',
  mmx: 'mmx-latn-zz',
  mmy: 'mmy-latn-td',
  mmz: 'mmz-latn-cd',
  mn: 'mn-cyrl-mn',
  'mn-cn': 'mn-mong-cn',
  'mn-mong': 'mn-mong-cn',
  mna: 'mna-latn-zz',
  mnb: 'mnb-latn-id',
  mnd: 'mnd-latn-br',
  mne: 'mne-latn-td',
  mnf: 'mnf-latn-zz',
  mng: 'mng-latn-vn',
  mnh: 'mnh-latn-cd',
  mni: 'mni-beng-in',
  mnj: 'mnj-arab-af',
  mnl: 'mnl-latn-vu',
  mnm: 'mnm-latn-pg',
  mnn: 'mnn-latn-vn',
  mnp: 'mnp-latn-cn',
  mnq: 'mnq-latn-my',
  mnr: 'mnr-latn-us',
  mns: 'mns-cyrl-ru',
  mnu: 'mnu-latn-id',
  mnv: 'mnv-latn-sb',
  mnw: 'mnw-mymr-mm',
  mnx: 'mnx-latn-id',
  mny: 'mny-latn-mz',
  mnz: 'mnz-latn-id',
  mo: 'mo-latn-ro',
  moa: 'moa-latn-zz',
  moc: 'moc-latn-ar',
  mod: 'mod-latn-us',
  moe: 'moe-latn-ca',
  mog: 'mog-latn-id',
  moh: 'moh-latn-ca',
  moi: 'moi-latn-ng',
  moj: 'moj-latn-cg',
  mok: 'mok-latn-id',
  mom: 'mom-latn-ni',
  moo: 'moo-latn-vn',
  mop: 'mop-latn-bz',
  moq: 'moq-latn-id',
  mor: 'mor-latn-sd',
  mos: 'mos-latn-bf',
  mot: 'mot-latn-co',
  mou: 'mou-latn-td',
  mov: 'mov-latn-us',
  mow: 'mow-latn-cg',
  mox: 'mox-latn-zz',
  moy: 'moy-latn-et',
  'moy-ethi': 'moy-ethi-et',
  moz: 'moz-latn-td',
  mpa: 'mpa-latn-tz',
  mpb: 'mpb-latn-au',
  mpc: 'mpc-latn-au',
  mpd: 'mpd-latn-br',
  mpe: 'mpe-latn-et',
  'mpe-ethi': 'mpe-ethi-et',
  mpg: 'mpg-latn-td',
  mph: 'mph-latn-au',
  mpi: 'mpi-latn-cm',
  mpj: 'mpj-latn-au',
  mpk: 'mpk-latn-td',
  mpl: 'mpl-latn-pg',
  mpm: 'mpm-latn-mx',
  mpn: 'mpn-latn-pg',
  mpo: 'mpo-latn-pg',
  mpp: 'mpp-latn-zz',
  mpq: 'mpq-latn-br',
  mpr: 'mpr-latn-sb',
  mps: 'mps-latn-zz',
  mpt: 'mpt-latn-zz',
  mpu: 'mpu-latn-br',
  mpv: 'mpv-latn-pg',
  mpw: 'mpw-latn-br',
  mpx: 'mpx-latn-zz',
  mpy: 'mpy-latn-id',
  mpz: 'mpz-thai-th',
  mqa: 'mqa-latn-id',
  mqb: 'mqb-latn-cm',
  mqc: 'mqc-latn-id',
  mqe: 'mqe-latn-pg',
  mqf: 'mqf-latn-id',
  mqg: 'mqg-latn-id',
  mqh: 'mqh-latn-mx',
  mqi: 'mqi-latn-id',
  mqj: 'mqj-latn-id',
  mqk: 'mqk-latn-ph',
  mql: 'mql-latn-zz',
  mqm: 'mqm-latn-pf',
  mqn: 'mqn-latn-id',
  mqo: 'mqo-latn-id',
  mqp: 'mqp-latn-id',
  mqq: 'mqq-latn-my',
  mqr: 'mqr-latn-id',
  mqs: 'mqs-latn-id',
  mqu: 'mqu-latn-ss',
  mqv: 'mqv-latn-pg',
  mqw: 'mqw-latn-pg',
  mqx: 'mqx-latn-id',
  'mqx-bugi': 'mqx-bugi-id',
  mqy: 'mqy-latn-id',
  mqz: 'mqz-latn-pg',
  mr: 'mr-deva-in',
  mra: 'mra-thai-th',
  mrb: 'mrb-latn-vu',
  mrc: 'mrc-latn-us',
  mrd: 'mrd-deva-np',
  mrf: 'mrf-latn-id',
  mrg: 'mrg-latn-in',
  'mrg-beng': 'mrg-beng-in',
  'mrg-deva': 'mrg-deva-in',
  mrh: 'mrh-latn-in',
  mrj: 'mrj-cyrl-ru',
  mrk: 'mrk-latn-nc',
  mrl: 'mrl-latn-fm',
  mrm: 'mrm-latn-vu',
  mrn: 'mrn-latn-sb',
  mro: 'mro-mroo-bd',
  mrp: 'mrp-latn-vu',
  mrq: 'mrq-latn-pf',
  mrr: 'mrr-deva-in',
  mrs: 'mrs-latn-vu',
  mrt: 'mrt-latn-ng',
  mru: 'mru-latn-cm',
  mrv: 'mrv-latn-pf',
  mrw: 'mrw-latn-ph',
  'mrw-arab': 'mrw-arab-ph',
  mrx: 'mrx-latn-id',
  mry: 'mry-latn-ph',
  mrz: 'mrz-latn-id',
  ms: 'ms-latn-my',
  'ms-cc': 'ms-arab-cc',
  msb: 'msb-latn-ph',
  msc: 'msc-latn-gn',
  mse: 'mse-latn-td',
  msf: 'msf-latn-id',
  msg: 'msg-latn-id',
  msh: 'msh-latn-mg',
  msi: 'msi-latn-my',
  msj: 'msj-latn-cd',
  msk: 'msk-latn-ph',
  msl: 'msl-latn-id',
  msm: 'msm-latn-ph',
  msn: 'msn-latn-vu',
  mso: 'mso-latn-id',
  msp: 'msp-latn-br',
  msq: 'msq-latn-nc',
  mss: 'mss-latn-id',
  msu: 'msu-latn-pg',
  msv: 'msv-latn-cm',
  msw: 'msw-latn-gw',
  msx: 'msx-latn-pg',
  msy: 'msy-latn-pg',
  msz: 'msz-latn-pg',
  mt: 'mt-latn-mt',
  mta: 'mta-latn-ph',
  mtb: 'mtb-latn-ci',
  mtc: 'mtc-latn-zz',
  mtd: 'mtd-latn-id',
  mte: 'mte-latn-sb',
  mtf: 'mtf-latn-zz',
  mtg: 'mtg-latn-id',
  mth: 'mth-latn-id',
  mti: 'mti-latn-zz',
  mtj: 'mtj-latn-id',
  mtk: 'mtk-latn-cm',
  mtl: 'mtl-latn-ng',
  mtm: 'mtm-cyrl-ru',
  mtn: 'mtn-latn-ni',
  mto: 'mto-latn-mx',
  mtp: 'mtp-latn-bo',
  mtq: 'mtq-latn-vn',
  mtr: 'mtr-deva-in',
  mts: 'mts-latn-pe',
  mtt: 'mtt-latn-vu',
  mtu: 'mtu-latn-mx',
  mtv: 'mtv-latn-pg',
  mtw: 'mtw-latn-ph',
  mtx: 'mtx-latn-mx',
  mty: 'mty-latn-pg',
  mua: 'mua-latn-cm',
  mub: 'mub-latn-td',
  muc: 'muc-latn-cm',
  mud: 'mud-cyrl-ru',
  mue: 'mue-latn-ec',
  mug: 'mug-latn-cm',
  muh: 'muh-latn-ss',
  mui: 'mui-latn-id',
  muj: 'muj-latn-td',
  muk: 'muk-tibt-np',
  mum: 'mum-latn-pg',
  muo: 'muo-latn-cm',
  muq: 'muq-latn-cn',
  mur: 'mur-latn-zz',
  mus: 'mus-latn-us',
  mut: 'mut-deva-in',
  muu: 'muu-latn-ke',
  muv: 'muv-taml-in',
  mux: 'mux-latn-pg',
  muy: 'muy-latn-cm',
  muz: 'muz-ethi-et',
  'muz-latn': 'muz-latn-et',
  mva: 'mva-latn-zz',
  mvd: 'mvd-latn-id',
  mvf: 'mvf-mong-cn',
  'mvf-phag': 'mvf-phag-cn',
  mvg: 'mvg-latn-mx',
  mvh: 'mvh-latn-td',
  mvk: 'mvk-latn-pg',
  mvl: 'mvl-latn-au',
  mvn: 'mvn-latn-zz',
  mvo: 'mvo-latn-sb',
  mvp: 'mvp-latn-id',
  mvq: 'mvq-latn-pg',
  mvr: 'mvr-latn-id',
  mvs: 'mvs-latn-id',
  mvt: 'mvt-latn-vu',
  mvu: 'mvu-latn-td',
  mvv: 'mvv-latn-my',
  mvw: 'mvw-latn-tz',
  mvx: 'mvx-latn-id',
  mvy: 'mvy-arab-pk',
  mvz: 'mvz-ethi-et',
  'mvz-arab': 'mvz-arab-et',
  mwa: 'mwa-latn-pg',
  mwb: 'mwb-latn-pg',
  mwc: 'mwc-latn-pg',
  mwe: 'mwe-latn-tz',
  mwf: 'mwf-latn-au',
  mwg: 'mwg-latn-pg',
  mwh: 'mwh-latn-pg',
  mwi: 'mwi-latn-vu',
  mwk: 'mwk-latn-ml',
  mwl: 'mwl-latn-pt',
  mwm: 'mwm-latn-td',
  mwn: 'mwn-latn-zm',
  mwo: 'mwo-latn-vu',
  mwp: 'mwp-latn-au',
  mwq: 'mwq-latn-mm',
  mwr: 'mwr-deva-in',
  mws: 'mws-latn-ke',
  mwt: 'mwt-mymr-mm',
  'mwt-thai': 'mwt-thai-th',
  mwu: 'mwu-latn-ss',
  mwv: 'mwv-latn-id',
  mww: 'mww-hmnp-us',
  mwz: 'mwz-latn-cd',
  mxa: 'mxa-latn-mx',
  mxb: 'mxb-latn-mx',
  mxc: 'mxc-latn-zw',
  mxd: 'mxd-latn-id',
  mxe: 'mxe-latn-vu',
  mxf: 'mxf-latn-cm',
  mxg: 'mxg-latn-ao',
  mxh: 'mxh-latn-cd',
  mxi: 'mxi-latn-es',
  mxj: 'mxj-latn-in',
  mxk: 'mxk-latn-pg',
  mxl: 'mxl-latn-bj',
  mxm: 'mxm-latn-zz',
  mxn: 'mxn-latn-id',
  mxo: 'mxo-latn-zm',
  mxp: 'mxp-latn-mx',
  mxq: 'mxq-latn-mx',
  mxr: 'mxr-latn-my',
  mxs: 'mxs-latn-mx',
  mxt: 'mxt-latn-mx',
  mxu: 'mxu-latn-cm',
  mxv: 'mxv-latn-mx',
  mxw: 'mxw-latn-pg',
  mxx: 'mxx-latn-ci',
  mxy: 'mxy-latn-mx',
  mxz: 'mxz-latn-id',
  my: 'my-mymr-mm',
  myb: 'myb-latn-td',
  myc: 'myc-latn-cd',
  mye: 'mye-latn-ga',
  myf: 'myf-latn-et',
  myg: 'myg-latn-cm',
  myh: 'myh-latn-us',
  myj: 'myj-latn-ss',
  myk: 'myk-latn-zz',
  myl: 'myl-latn-id',
  mym: 'mym-ethi-zz',
  myp: 'myp-latn-br',
  myr: 'myr-latn-pe',
  myu: 'myu-latn-br',
  myv: 'myv-cyrl-ru',
  myw: 'myw-latn-zz',
  myx: 'myx-latn-ug',
  myy: 'myy-latn-co',
  myz: 'myz-mand-ir',
  mza: 'mza-latn-mx',
  mzd: 'mzd-latn-cm',
  mze: 'mze-latn-pg',
  mzh: 'mzh-latn-ar',
  mzi: 'mzi-latn-mx',
  mzj: 'mzj-latn-lr',
  mzk: 'mzk-latn-zz',
  mzl: 'mzl-latn-mx',
  mzm: 'mzm-latn-zz',
  mzn: 'mzn-arab-ir',
  mzo: 'mzo-latn-br',
  mzp: 'mzp-latn-zz',
  mzq: 'mzq-latn-id',
  mzr: 'mzr-latn-br',
  mzt: 'mzt-latn-my',
  mzu: 'mzu-latn-pg',
  mzv: 'mzv-latn-cf',
  mzw: 'mzw-latn-zz',
  mzx: 'mzx-latn-gy',
  mzz: 'mzz-latn-zz',
  na: 'na-latn-nr',
  naa: 'naa-latn-id',
  nab: 'nab-latn-br',
  nac: 'nac-latn-zz',
  nae: 'nae-latn-id',
  naf: 'naf-latn-zz',
  nag: 'nag-latn-in',
  naj: 'naj-latn-gn',
  nak: 'nak-latn-zz',
  nal: 'nal-latn-pg',
  nam: 'nam-latn-au',
  nan: 'nan-hans-cn',
  nao: 'nao-deva-np',
  nap: 'nap-latn-it',
  naq: 'naq-latn-na',
  nar: 'nar-latn-ng',
  nas: 'nas-latn-zz',
  nat: 'nat-latn-ng',
  naw: 'naw-latn-gh',
  nax: 'nax-latn-pg',
  nay: 'nay-latn-au',
  naz: 'naz-latn-mx',
  nb: 'nb-latn-no',
  nba: 'nba-latn-ao',
  nbb: 'nbb-latn-ng',
  nbc: 'nbc-latn-in',
  nbd: 'nbd-latn-cd',
  nbe: 'nbe-latn-in',
  nbh: 'nbh-latn-ng',
  nbi: 'nbi-latn-in',
  nbj: 'nbj-latn-au',
  nbk: 'nbk-latn-pg',
  nbm: 'nbm-latn-cf',
  nbn: 'nbn-latn-id',
  nbo: 'nbo-latn-ng',
  nbp: 'nbp-latn-ng',
  nbq: 'nbq-latn-id',
  nbr: 'nbr-latn-ng',
  nbt: 'nbt-latn-in',
  'nbt-deva': 'nbt-deva-in',
  nbu: 'nbu-latn-in',
  nbv: 'nbv-latn-cm',
  nbw: 'nbw-latn-cd',
  nby: 'nby-latn-pg',
  nca: 'nca-latn-zz',
  ncb: 'ncb-latn-in',
  'ncb-deva': 'ncb-deva-in',
  ncc: 'ncc-latn-pg',
  ncd: 'ncd-deva-np',
  nce: 'nce-latn-zz',
  ncf: 'ncf-latn-zz',
  ncg: 'ncg-latn-ca',
  nch: 'nch-latn-mx',
  nci: 'nci-latn-mx',
  ncj: 'ncj-latn-mx',
  nck: 'nck-latn-au',
  ncl: 'ncl-latn-mx',
  ncm: 'ncm-latn-pg',
  ncn: 'ncn-latn-pg',
  nco: 'nco-latn-zz',
  ncq: 'ncq-laoo-la',
  'ncq-thai': 'ncq-thai-la',
  ncr: 'ncr-latn-cm',
  nct: 'nct-latn-in',
  'nct-beng': 'nct-beng-in',
  ncu: 'ncu-latn-zz',
  ncx: 'ncx-latn-mx',
  ncz: 'ncz-latn-us',
  nd: 'nd-latn-zw',
  nda: 'nda-latn-cg',
  ndb: 'ndb-latn-cm',
  ndc: 'ndc-latn-mz',
  ndd: 'ndd-latn-ng',
  ndf: 'ndf-cyrl-ru',
  ndg: 'ndg-latn-tz',
  ndh: 'ndh-latn-tz',
  ndi: 'ndi-latn-ng',
  ndj: 'ndj-latn-tz',
  ndk: 'ndk-latn-cd',
  ndl: 'ndl-latn-cd',
  ndm: 'ndm-latn-td',
  ndn: 'ndn-latn-cg',
  ndp: 'ndp-latn-ug',
  ndq: 'ndq-latn-ao',
  ndr: 'ndr-latn-ng',
  nds: 'nds-latn-de',
  ndt: 'ndt-latn-cd',
  ndu: 'ndu-latn-cm',
  ndv: 'ndv-latn-sn',
  ndw: 'ndw-latn-cd',
  ndx: 'ndx-latn-id',
  ndy: 'ndy-latn-cf',
  'ndy-td': 'ndy-latn-td',
  ndz: 'ndz-latn-ss',
  ne: 'ne-deva-np',
  nea: 'nea-latn-id',
  neb: 'neb-latn-zz',
  nec: 'nec-latn-id',
  ned: 'ned-latn-ng',
  nee: 'nee-latn-nc',
  neg: 'neg-cyrl-ru',
  neh: 'neh-tibt-bt',
  nei: 'nei-xsux-tr',
  nej: 'nej-latn-pg',
  nek: 'nek-latn-nc',
  nem: 'nem-latn-nc',
  nen: 'nen-latn-nc',
  neo: 'neo-latn-vn',
  neq: 'neq-latn-mx',
  ner: 'ner-latn-id',
  net: 'net-latn-pg',
  neu: 'neu-latn-001',
  new: 'new-deva-np',
  nex: 'nex-latn-zz',
  ney: 'ney-latn-ci',
  nez: 'nez-latn-us',
  nfa: 'nfa-latn-id',
  nfd: 'nfd-latn-ng',
  nfl: 'nfl-latn-sb',
  nfr: 'nfr-latn-zz',
  nfu: 'nfu-latn-cm',
  ng: 'ng-latn-na',
  nga: 'nga-latn-zz',
  ngb: 'ngb-latn-zz',
  ngc: 'ngc-latn-cd',
  ngd: 'ngd-latn-cf',
  nge: 'nge-latn-cm',
  ngg: 'ngg-latn-cf',
  ngh: 'ngh-latn-za',
  ngi: 'ngi-latn-ng',
  ngj: 'ngj-latn-cm',
  ngk: 'ngk-latn-au',
  ngl: 'ngl-latn-mz',
  ngm: 'ngm-latn-fm',
  ngn: 'ngn-latn-cm',
  ngp: 'ngp-latn-tz',
  ngq: 'ngq-latn-tz',
  ngr: 'ngr-latn-sb',
  ngs: 'ngs-latn-ng',
  ngt: 'ngt-laoo-la',
  ngu: 'ngu-latn-mx',
  ngv: 'ngv-latn-cm',
  ngw: 'ngw-latn-ng',
  ngx: 'ngx-latn-ng',
  ngy: 'ngy-latn-cm',
  ngz: 'ngz-latn-cg',
  nha: 'nha-latn-au',
  nhb: 'nhb-latn-zz',
  nhc: 'nhc-latn-mx',
  nhd: 'nhd-latn-py',
  nhe: 'nhe-latn-mx',
  nhf: 'nhf-latn-au',
  nhg: 'nhg-latn-mx',
  nhi: 'nhi-latn-mx',
  nhk: 'nhk-latn-mx',
  nhm: 'nhm-latn-mx',
  nhn: 'nhn-latn-mx',
  nho: 'nho-latn-pg',
  nhp: 'nhp-latn-mx',
  nhq: 'nhq-latn-mx',
  nhr: 'nhr-latn-bw',
  nht: 'nht-latn-mx',
  nhu: 'nhu-latn-cm',
  nhv: 'nhv-latn-mx',
  nhw: 'nhw-latn-mx',
  nhx: 'nhx-latn-mx',
  nhy: 'nhy-latn-mx',
  nhz: 'nhz-latn-mx',
  nia: 'nia-latn-id',
  nib: 'nib-latn-pg',
  nid: 'nid-latn-au',
  nie: 'nie-latn-td',
  nif: 'nif-latn-zz',
  nig: 'nig-latn-au',
  nih: 'nih-latn-tz',
  nii: 'nii-latn-zz',
  nij: 'nij-latn-id',
  nil: 'nil-latn-id',
  nim: 'nim-latn-tz',
  nin: 'nin-latn-zz',
  nio: 'nio-cyrl-ru',
  niq: 'niq-latn-ke',
  nir: 'nir-latn-id',
  nis: 'nis-latn-pg',
  nit: 'nit-telu-in',
  niu: 'niu-latn-nu',
  niv: 'niv-cyrl-ru',
  'niv-latn': 'niv-latn-ru',
  niw: 'niw-latn-pg',
  nix: 'nix-latn-cd',
  niy: 'niy-latn-zz',
  niz: 'niz-latn-zz',
  nja: 'nja-latn-ng',
  njb: 'njb-latn-in',
  njd: 'njd-latn-tz',
  njh: 'njh-latn-in',
  nji: 'nji-latn-au',
  njj: 'njj-latn-cm',
  njl: 'njl-latn-ss',
  njm: 'njm-latn-in',
  njn: 'njn-latn-in',
  njo: 'njo-latn-in',
  njr: 'njr-latn-ng',
  njs: 'njs-latn-id',
  njt: 'njt-latn-sr',
  nju: 'nju-latn-au',
  njx: 'njx-latn-cg',
  njy: 'njy-latn-cm',
  njz: 'njz-latn-in',
  'njz-beng': 'njz-beng-in',
  nka: 'nka-latn-zm',
  nkb: 'nkb-latn-in',
  nkc: 'nkc-latn-cm',
  nkd: 'nkd-latn-in',
  nke: 'nke-latn-sb',
  nkf: 'nkf-latn-in',
  nkg: 'nkg-latn-zz',
  nkh: 'nkh-latn-in',
  nki: 'nki-latn-in',
  'nki-beng': 'nki-beng-in',
  nkj: 'nkj-latn-id',
  nkk: 'nkk-latn-vu',
  nkm: 'nkm-latn-pg',
  nkn: 'nkn-latn-ao',
  nko: 'nko-latn-zz',
  nkq: 'nkq-latn-gh',
  nkr: 'nkr-latn-fm',
  nks: 'nks-latn-id',
  nkt: 'nkt-latn-tz',
  nku: 'nku-latn-ci',
  nkv: 'nkv-latn-mw',
  nkw: 'nkw-latn-cd',
  nkx: 'nkx-latn-ng',
  nkz: 'nkz-latn-ng',
  nl: 'nl-latn-nl',
  nla: 'nla-latn-cm',
  nlc: 'nlc-latn-id',
  nle: 'nle-latn-ke',
  nlg: 'nlg-latn-sb',
  nli: 'nli-arab-af',
  nlj: 'nlj-latn-cd',
  nlk: 'nlk-latn-id',
  nlm: 'nlm-arab-pk',
  nlo: 'nlo-latn-cd',
  nlq: 'nlq-latn-mm',
  nlu: 'nlu-latn-gh',
  nlv: 'nlv-latn-mx',
  nlw: 'nlw-latn-au',
  nlx: 'nlx-deva-in',
  nly: 'nly-latn-au',
  nlz: 'nlz-latn-sb',
  nma: 'nma-latn-in',
  nmb: 'nmb-latn-vu',
  nmc: 'nmc-latn-td',
  nmd: 'nmd-latn-ga',
  nme: 'nme-latn-in',
  nmf: 'nmf-latn-in',
  nmg: 'nmg-latn-cm',
  nmh: 'nmh-latn-in',
  nmi: 'nmi-latn-ng',
  nmj: 'nmj-latn-cf',
  nmk: 'nmk-latn-vu',
  nml: 'nml-latn-cm',
  nmm: 'nmm-deva-np',
  'nmm-tibt': 'nmm-tibt-np',
  nmn: 'nmn-latn-bw',
  nmo: 'nmo-latn-in',
  'nmo-beng': 'nmo-beng-in',
  nmp: 'nmp-latn-au',
  nmq: 'nmq-latn-zw',
  nmr: 'nmr-latn-cm',
  nms: 'nms-latn-vu',
  nmt: 'nmt-latn-fm',
  nmu: 'nmu-latn-us',
  nmv: 'nmv-latn-au',
  nmw: 'nmw-latn-pg',
  nmx: 'nmx-latn-pg',
  nmz: 'nmz-latn-zz',
  nn: 'nn-latn-no',
  nna: 'nna-latn-au',
  nnb: 'nnb-latn-cd',
  nnc: 'nnc-latn-td',
  nnd: 'nnd-latn-vu',
  nne: 'nne-latn-ao',
  nnf: 'nnf-latn-zz',
  nng: 'nng-latn-in',
  'nng-beng': 'nng-beng-in',
  nnh: 'nnh-latn-cm',
  nni: 'nni-latn-id',
  nnj: 'nnj-latn-et',
  nnk: 'nnk-latn-zz',
  nnl: 'nnl-latn-in',
  nnm: 'nnm-latn-zz',
  nnn: 'nnn-latn-td',
  nnp: 'nnp-wcho-in',
  nnq: 'nnq-latn-tz',
  nnr: 'nnr-latn-au',
  nnt: 'nnt-latn-us',
  nnu: 'nnu-latn-gh',
  nnv: 'nnv-latn-au',
  nnw: 'nnw-latn-bf',
  nny: 'nny-latn-au',
  nnz: 'nnz-latn-cm',
  no: 'no-latn-no',
  noa: 'noa-latn-co',
  noc: 'noc-latn-pg',
  nod: 'nod-lana-th',
  noe: 'noe-deva-in',
  nof: 'nof-latn-pg',
  nog: 'nog-cyrl-ru',
  noh: 'noh-latn-pg',
  noi: 'noi-deva-in',
  noj: 'noj-latn-co',
  nok: 'nok-latn-us',
  nom: 'nom-latn-pe',
  non: 'non-runr-se',
  nop: 'nop-latn-zz',
  noq: 'noq-latn-cd',
  nos: 'nos-yiii-cn',
  not: 'not-latn-pe',
  nou: 'nou-latn-zz',
  nov: 'nov-latn-001',
  now: 'now-latn-tz',
  noy: 'noy-latn-td',
  npb: 'npb-tibt-bt',
  npg: 'npg-latn-mm',
  nph: 'nph-latn-in',
  npl: 'npl-latn-mx',
  npn: 'npn-latn-pg',
  npo: 'npo-latn-in',
  nps: 'nps-latn-id',
  npu: 'npu-latn-in',
  npx: 'npx-latn-sb',
  npy: 'npy-latn-id',
  nqg: 'nqg-latn-bj',
  nqk: 'nqk-latn-bj',
  nql: 'nql-latn-ao',
  nqm: 'nqm-latn-id',
  nqn: 'nqn-latn-pg',
  nqo: 'nqo-nkoo-gn',
  nqq: 'nqq-latn-mm',
  nqt: 'nqt-latn-ng',
  nqy: 'nqy-latn-mm',
  nr: 'nr-latn-za',
  nra: 'nra-latn-ga',
  nrb: 'nrb-latn-zz',
  nre: 'nre-latn-in',
  nrf: 'nrf-latn-je',
  nrg: 'nrg-latn-vu',
  nri: 'nri-latn-in',
  nrk: 'nrk-latn-au',
  nrl: 'nrl-latn-au',
  nrm: 'nrm-latn-my',
  nrp: 'nrp-latn-it',
  nru: 'nru-latn-cn',
  'nru-hans': 'nru-hans-cn',
  'nru-hant': 'nru-hant-cn',
  nrx: 'nrx-latn-au',
  nrz: 'nrz-latn-pg',
  nsa: 'nsa-latn-in',
  nsb: 'nsb-latn-za',
  nsc: 'nsc-latn-ng',
  nsd: 'nsd-yiii-cn',
  nse: 'nse-latn-zm',
  nsf: 'nsf-yiii-cn',
  nsg: 'nsg-latn-tz',
  nsh: 'nsh-latn-cm',
  nsk: 'nsk-cans-ca',
  nsm: 'nsm-latn-in',
  nsn: 'nsn-latn-zz',
  nso: 'nso-latn-za',
  nsq: 'nsq-latn-us',
  nss: 'nss-latn-zz',
  nst: 'nst-tnsa-in',
  nsu: 'nsu-latn-mx',
  nsv: 'nsv-yiii-cn',
  nsw: 'nsw-latn-vu',
  nsx: 'nsx-latn-ao',
  nsy: 'nsy-latn-id',
  nsz: 'nsz-latn-us',
  ntd: 'ntd-latn-my',
  nte: 'nte-latn-mz',
  ntg: 'ntg-latn-au',
  nti: 'nti-latn-bf',
  ntj: 'ntj-latn-au',
  ntk: 'ntk-latn-tz',
  ntm: 'ntm-latn-zz',
  nto: 'nto-latn-cd',
  ntp: 'ntp-latn-mx',
  ntr: 'ntr-latn-zz',
  ntu: 'ntu-latn-sb',
  ntx: 'ntx-latn-mm',
  nty: 'nty-yiii-vn',
  ntz: 'ntz-arab-ir',
  nua: 'nua-latn-nc',
  nuc: 'nuc-latn-br',
  nud: 'nud-latn-pg',
  nue: 'nue-latn-cd',
  nuf: 'nuf-latn-cn',
  nug: 'nug-latn-au',
  nuh: 'nuh-latn-ng',
  nui: 'nui-latn-zz',
  nuj: 'nuj-latn-ug',
  nuk: 'nuk-latn-ca',
  num: 'num-latn-to',
  nun: 'nun-latn-mm',
  nuo: 'nuo-latn-vn',
  nup: 'nup-latn-zz',
  nuq: 'nuq-latn-pg',
  nur: 'nur-latn-pg',
  nus: 'nus-latn-ss',
  nut: 'nut-latn-vn',
  nuu: 'nuu-latn-cd',
  nuv: 'nuv-latn-zz',
  nuw: 'nuw-latn-fm',
  nux: 'nux-latn-zz',
  nuy: 'nuy-latn-au',
  nuz: 'nuz-latn-mx',
  nv: 'nv-latn-us',
  nvh: 'nvh-latn-vu',
  nvm: 'nvm-latn-pg',
  nvo: 'nvo-latn-cm',
  nwb: 'nwb-latn-zz',
  nwc: 'nwc-newa-np',
  'nwc-brah': 'nwc-brah-np',
  'nwc-deva': 'nwc-deva-np',
  'nwc-sidd': 'nwc-sidd-np',
  nwe: 'nwe-latn-cm',
  nwg: 'nwg-latn-au',
  nwi: 'nwi-latn-vu',
  nwm: 'nwm-latn-ss',
  nwo: 'nwo-latn-au',
  nwr: 'nwr-latn-pg',
  nww: 'nww-latn-tz',
  nwx: 'nwx-deva-np',
  nxa: 'nxa-latn-tl',
  nxd: 'nxd-latn-cd',
  nxe: 'nxe-latn-id',
  nxg: 'nxg-latn-id',
  nxi: 'nxi-latn-tz',
  nxl: 'nxl-latn-id',
  nxn: 'nxn-latn-au',
  nxo: 'nxo-latn-ga',
  nxq: 'nxq-latn-cn',
  nxr: 'nxr-latn-zz',
  nxx: 'nxx-latn-id',
  ny: 'ny-latn-mw',
  nyb: 'nyb-latn-gh',
  nyc: 'nyc-latn-cd',
  nyd: 'nyd-latn-ke',
  nye: 'nye-latn-ao',
  nyf: 'nyf-latn-ke',
  nyg: 'nyg-latn-cd',
  nyh: 'nyh-latn-au',
  nyi: 'nyi-latn-sd',
  nyj: 'nyj-latn-cd',
  nyk: 'nyk-latn-ao',
  nyl: 'nyl-thai-th',
  nym: 'nym-latn-tz',
  nyn: 'nyn-latn-ug',
  nyo: 'nyo-latn-ug',
  nyp: 'nyp-latn-ug',
  nyq: 'nyq-arab-ir',
  nyr: 'nyr-latn-mw',
  nys: 'nys-latn-au',
  nyt: 'nyt-latn-au',
  nyu: 'nyu-latn-mz',
  nyv: 'nyv-latn-au',
  nyx: 'nyx-latn-au',
  nyy: 'nyy-latn-tz',
  nza: 'nza-latn-cm',
  nzb: 'nzb-latn-ga',
  nzd: 'nzd-latn-cd',
  nzi: 'nzi-latn-gh',
  nzk: 'nzk-latn-cf',
  nzm: 'nzm-latn-in',
  nzu: 'nzu-latn-cg',
  nzy: 'nzy-latn-td',
  nzz: 'nzz-latn-ml',
  oaa: 'oaa-cyrl-ru',
  oac: 'oac-cyrl-ru',
  oar: 'oar-syrc-sy',
  oav: 'oav-geor-ge',
  obi: 'obi-latn-us',
  obk: 'obk-latn-ph',
  obl: 'obl-latn-cm',
  obm: 'obm-phnx-jo',
  obo: 'obo-latn-ph',
  obr: 'obr-mymr-mm',
  obt: 'obt-latn-fr',
  obu: 'obu-latn-ng',
  oc: 'oc-latn-fr',
  oca: 'oca-latn-pe',
  oco: 'oco-latn-gb',
  ocu: 'ocu-latn-mx',
  oda: 'oda-latn-ng',
  odk: 'odk-arab-pk',
  odt: 'odt-latn-nl',
  odu: 'odu-latn-ng',
  ofu: 'ofu-latn-ng',
  ogb: 'ogb-latn-ng',
  ogc: 'ogc-latn-zz',
  ogg: 'ogg-latn-ng',
  ogo: 'ogo-latn-ng',
  ogu: 'ogu-latn-ng',
  oht: 'oht-xsux-tr',
  oia: 'oia-latn-id',
  oie: 'oie-latn-ss',
  oin: 'oin-latn-pg',
  oj: 'oj-cans-ca',
  ojb: 'ojb-latn-ca',
  'ojb-cans': 'ojb-cans-ca',
  ojc: 'ojc-latn-ca',
  ojs: 'ojs-cans-ca',
  ojv: 'ojv-latn-sb',
  ojw: 'ojw-latn-ca',
  'ojw-cans': 'ojw-cans-ca',
  oka: 'oka-latn-ca',
  okb: 'okb-latn-ng',
  okc: 'okc-latn-cd',
  okd: 'okd-latn-ng',
  oke: 'oke-latn-ng',
  okg: 'okg-latn-au',
  oki: 'oki-latn-ke',
  okk: 'okk-latn-pg',
  okm: 'okm-hang-kr',
  oko: 'oko-hani-kr',
  okr: 'okr-latn-zz',
  oks: 'oks-latn-ng',
  oku: 'oku-latn-cm',
  okv: 'okv-latn-zz',
  okx: 'okx-latn-ng',
  okz: 'okz-khmr-kh',
  ola: 'ola-deva-np',
  'ola-tibt': 'ola-tibt-cn',
  old: 'old-latn-tz',
  ole: 'ole-tibt-bt',
  olk: 'olk-latn-au',
  olm: 'olm-latn-ng',
  olo: 'olo-latn-ru',
  olr: 'olr-latn-vu',
  olt: 'olt-latn-lt',
  olu: 'olu-latn-ao',
  om: 'om-latn-et',
  oma: 'oma-latn-us',
  omb: 'omb-latn-vu',
  omc: 'omc-latn-pe',
  omg: 'omg-latn-pe',
  omi: 'omi-latn-cd',
  omk: 'omk-cyrl-ru',
  oml: 'oml-latn-cd',
  omo: 'omo-latn-pg',
  omp: 'omp-mtei-in',
  omr: 'omr-modi-in',
  omt: 'omt-latn-ke',
  omu: 'omu-latn-pe',
  omw: 'omw-latn-pg',
  ona: 'ona-latn-ar',
  one: 'one-latn-ca',
  ong: 'ong-latn-zz',
  oni: 'oni-latn-id',
  onj: 'onj-latn-pg',
  onk: 'onk-latn-pg',
  onn: 'onn-latn-zz',
  ono: 'ono-latn-ca',
  onp: 'onp-latn-in',
  'onp-deva': 'onp-deva-in',
  onr: 'onr-latn-pg',
  ons: 'ons-latn-zz',
  ont: 'ont-latn-pg',
  onu: 'onu-latn-vu',
  onx: 'onx-latn-id',
  ood: 'ood-latn-us',
  oon: 'oon-deva-in',
  oor: 'oor-latn-za',
  opa: 'opa-latn-ng',
  opk: 'opk-latn-id',
  opm: 'opm-latn-zz',
  opo: 'opo-latn-pg',
  opt: 'opt-latn-mx',
  opy: 'opy-latn-br',
  or: 'or-orya-in',
  ora: 'ora-latn-sb',
  orc: 'orc-latn-ke',
  ore: 'ore-latn-pe',
  org: 'org-latn-ng',
  orn: 'orn-latn-my',
  oro: 'oro-latn-zz',
  orr: 'orr-latn-ng',
  ors: 'ors-latn-my',
  ort: 'ort-telu-in',
  oru: 'oru-arab-zz',
  orv: 'orv-cyrl-ru',
  orw: 'orw-latn-br',
  orx: 'orx-latn-ng',
  orz: 'orz-latn-id',
  os: 'os-cyrl-ge',
  osa: 'osa-osge-us',
  osc: 'osc-ital-it',
  'osc-latn': 'osc-latn-it',
  osi: 'osi-java-id',
  oso: 'oso-latn-ng',
  osp: 'osp-latn-es',
  ost: 'ost-latn-cm',
  osu: 'osu-latn-pg',
  osx: 'osx-latn-de',
  ota: 'ota-arab-zz',
  otb: 'otb-tibt-cn',
  otd: 'otd-latn-id',
  ote: 'ote-latn-mx',
  oti: 'oti-latn-br',
  otk: 'otk-orkh-mn',
  otl: 'otl-latn-mx',
  otm: 'otm-latn-mx',
  otn: 'otn-latn-mx',
  otq: 'otq-latn-mx',
  otr: 'otr-latn-sd',
  ots: 'ots-latn-mx',
  ott: 'ott-latn-mx',
  otu: 'otu-latn-br',
  otw: 'otw-latn-ca',
  otx: 'otx-latn-mx',
  oty: 'oty-gran-in',
  otz: 'otz-latn-mx',
  oub: 'oub-latn-lr',
  oue: 'oue-latn-pg',
  oui: 'oui-ougr-143',
  oum: 'oum-latn-pg',
  ovd: 'ovd-latn-se',
  owi: 'owi-latn-pg',
  owl: 'owl-latn-gb',
  oyd: 'oyd-latn-et',
  oym: 'oym-latn-br',
  oyy: 'oyy-latn-pg',
  ozm: 'ozm-latn-zz',
  pa: 'pa-guru-in',
  'pa-arab': 'pa-arab-pk',
  'pa-pk': 'pa-arab-pk',
  pab: 'pab-latn-br',
  pac: 'pac-latn-vn',
  pad: 'pad-latn-br',
  pae: 'pae-latn-cd',
  paf: 'paf-latn-br',
  pag: 'pag-latn-ph',
  pah: 'pah-latn-br',
  pai: 'pai-latn-ng',
  pak: 'pak-latn-br',
  pal: 'pal-phli-ir',
  'pal-phlp': 'pal-phlp-cn',
  pam: 'pam-latn-ph',
  pao: 'pao-latn-us',
  pap: 'pap-latn-cw',
  paq: 'paq-cyrl-tj',
  par: 'par-latn-us',
  pas: 'pas-latn-id',
  pau: 'pau-latn-pw',
  pav: 'pav-latn-br',
  paw: 'paw-latn-us',
  pax: 'pax-latn-br',
  pay: 'pay-latn-hn',
  paz: 'paz-latn-br',
  pbb: 'pbb-latn-co',
  pbc: 'pbc-latn-gy',
  pbe: 'pbe-latn-mx',
  pbf: 'pbf-latn-mx',
  pbg: 'pbg-latn-ve',
  pbh: 'pbh-latn-ve',
  pbi: 'pbi-latn-zz',
  pbl: 'pbl-latn-ng',
  pbm: 'pbm-latn-mx',
  pbn: 'pbn-latn-ng',
  pbo: 'pbo-latn-gw',
  pbp: 'pbp-latn-gn',
  pbr: 'pbr-latn-tz',
  pbs: 'pbs-latn-mx',
  pbt: 'pbt-arab-af',
  pbv: 'pbv-latn-in',
  pby: 'pby-latn-pg',
  pca: 'pca-latn-mx',
  pcb: 'pcb-khmr-kh',
  pcc: 'pcc-latn-cn',
  'pcc-hani': 'pcc-hani-cn',
  pcd: 'pcd-latn-fr',
  pce: 'pce-mymr-mm',
  'pce-thai': 'pce-thai-th',
  pcf: 'pcf-mlym-in',
  pcg: 'pcg-mlym-in',
  'pcg-knda': 'pcg-knda-in',
  'pcg-taml': 'pcg-taml-in',
  pch: 'pch-deva-in',
  pci: 'pci-deva-in',
  'pci-orya': 'pci-orya-in',
  pcj: 'pcj-telu-in',
  pck: 'pck-latn-in',
  pcm: 'pcm-latn-ng',
  pcn: 'pcn-latn-ng',
  pcp: 'pcp-latn-bo',
  pcw: 'pcw-latn-ng',
  pda: 'pda-latn-pg',
  pdc: 'pdc-latn-us',
  pdn: 'pdn-latn-id',
  pdo: 'pdo-latn-id',
  pdt: 'pdt-latn-ca',
  pdu: 'pdu-latn-mm',
  'pdu-mymr': 'pdu-mymr-mm',
  pea: 'pea-latn-id',
  peb: 'peb-latn-us',
  ped: 'ped-latn-zz',
  pee: 'pee-latn-id',
  peg: 'peg-orya-in',
  pei: 'pei-latn-mx',
  pek: 'pek-latn-pg',
  pel: 'pel-latn-id',
  pem: 'pem-latn-cd',
  peo: 'peo-xpeo-ir',
  pep: 'pep-latn-pg',
  peq: 'peq-latn-us',
  pev: 'pev-latn-ve',
  pex: 'pex-latn-zz',
  pey: 'pey-latn-id',
  pez: 'pez-latn-my',
  pfa: 'pfa-latn-fm',
  pfe: 'pfe-latn-cm',
  pfl: 'pfl-latn-de',
  pga: 'pga-latn-ss',
  pgd: 'pgd-khar-pk',
  pgg: 'pgg-deva-in',
  pgi: 'pgi-latn-pg',
  pgk: 'pgk-latn-vu',
  pgl: 'pgl-ogam-ie',
  pgn: 'pgn-ital-it',
  pgs: 'pgs-latn-ng',
  pgu: 'pgu-latn-id',
  phd: 'phd-deva-in',
  phg: 'phg-latn-vn',
  phh: 'phh-latn-vn',
  phk: 'phk-mymr-in',
  phl: 'phl-arab-zz',
  phm: 'phm-latn-mz',
  phn: 'phn-phnx-lb',
  pho: 'pho-laoo-la',
  phr: 'phr-arab-pk',
  pht: 'pht-thai-th',
  phv: 'phv-arab-af',
  phw: 'phw-deva-np',
  pi: 'pi-sinh-in',
  'pi-brah': 'pi-brah-in',
  'pi-deva': 'pi-deva-in',
  'pi-khar': 'pi-khar-in',
  'pi-khmr': 'pi-khmr-in',
  'pi-mymr': 'pi-mymr-in',
  'pi-thai': 'pi-thai-in',
  pia: 'pia-latn-mx',
  pib: 'pib-latn-pe',
  pic: 'pic-latn-ga',
  pid: 'pid-latn-ve',
  pif: 'pif-latn-fm',
  pig: 'pig-latn-pe',
  pih: 'pih-latn-nf',
  pij: 'pij-latn-co',
  pil: 'pil-latn-zz',
  pim: 'pim-latn-us',
  pin: 'pin-latn-pg',
  pio: 'pio-latn-co',
  pip: 'pip-latn-zz',
  pir: 'pir-latn-br',
  pis: 'pis-latn-sb',
  pit: 'pit-latn-au',
  piu: 'piu-latn-au',
  piv: 'piv-latn-sb',
  piw: 'piw-latn-tz',
  pix: 'pix-latn-pg',
  piy: 'piy-latn-ng',
  piz: 'piz-latn-nc',
  pjt: 'pjt-latn-au',
  pka: 'pka-brah-in',
  pkb: 'pkb-latn-ke',
  pkg: 'pkg-latn-pg',
  pkh: 'pkh-latn-bd',
  'pkh-deva': 'pkh-deva-bd',
  pkn: 'pkn-latn-au',
  pko: 'pko-latn-ke',
  pkp: 'pkp-latn-ck',
  pkr: 'pkr-mlym-in',
  pku: 'pku-latn-id',
  pl: 'pl-latn-pl',
  pla: 'pla-latn-zz',
  plb: 'plb-latn-vu',
  plc: 'plc-latn-ph',
  pld: 'pld-latn-gb',
  ple: 'ple-latn-id',
  plg: 'plg-latn-ar',
  plh: 'plh-latn-id',
  plj: 'plj-latn-ng',
  plk: 'plk-arab-pk',
  pll: 'pll-mymr-mm',
  pln: 'pln-latn-co',
  plo: 'plo-latn-mx',
  plr: 'plr-latn-ci',
  pls: 'pls-latn-mx',
  plu: 'plu-latn-br',
  plv: 'plv-latn-ph',
  plw: 'plw-latn-ph',
  plz: 'plz-latn-my',
  pma: 'pma-latn-vu',
  pmb: 'pmb-latn-cd',
  pmd: 'pmd-latn-au',
  pme: 'pme-latn-nc',
  pmf: 'pmf-latn-id',
  pmh: 'pmh-brah-in',
  pmi: 'pmi-latn-cn',
  pmj: 'pmj-latn-cn',
  pml: 'pml-latn-tn',
  pmm: 'pmm-latn-cm',
  pmn: 'pmn-latn-cm',
  pmo: 'pmo-latn-id',
  pmq: 'pmq-latn-mx',
  pmr: 'pmr-latn-pg',
  pms: 'pms-latn-it',
  pmt: 'pmt-latn-pf',
  pmw: 'pmw-latn-us',
  pmx: 'pmx-latn-in',
  pmy: 'pmy-latn-id',
  pmz: 'pmz-latn-mx',
  pna: 'pna-latn-my',
  pnc: 'pnc-latn-id',
  pnd: 'pnd-latn-ao',
  pne: 'pne-latn-my',
  png: 'png-latn-zz',
  pnh: 'pnh-latn-ck',
  pni: 'pni-latn-id',
  pnj: 'pnj-latn-au',
  pnk: 'pnk-latn-bo',
  pnl: 'pnl-latn-bf',
  pnm: 'pnm-latn-my',
  pnn: 'pnn-latn-zz',
  pno: 'pno-latn-pe',
  pnp: 'pnp-latn-id',
  pnq: 'pnq-latn-bf',
  pnr: 'pnr-latn-pg',
  pns: 'pns-latn-id',
  pnt: 'pnt-grek-gr',
  pnv: 'pnv-latn-au',
  pnw: 'pnw-latn-au',
  pny: 'pny-latn-cm',
  pnz: 'pnz-latn-cf',
  poc: 'poc-latn-gt',
  poe: 'poe-latn-mx',
  pof: 'pof-latn-cd',
  pog: 'pog-latn-br',
  poh: 'poh-latn-gt',
  poi: 'poi-latn-mx',
  pok: 'pok-latn-br',
  pom: 'pom-latn-us',
  pon: 'pon-latn-fm',
  poo: 'poo-latn-us',
  pop: 'pop-latn-nc',
  poq: 'poq-latn-mx',
  pos: 'pos-latn-mx',
  pot: 'pot-latn-us',
  pov: 'pov-latn-gw',
  pow: 'pow-latn-mx',
  poy: 'poy-latn-tz',
  ppa: 'ppa-deva-in',
  ppe: 'ppe-latn-pg',
  ppi: 'ppi-latn-mx',
  ppk: 'ppk-latn-id',
  ppl: 'ppl-latn-sv',
  ppm: 'ppm-latn-id',
  ppn: 'ppn-latn-pg',
  ppo: 'ppo-latn-zz',
  ppp: 'ppp-latn-cd',
  ppq: 'ppq-latn-pg',
  pps: 'pps-latn-mx',
  ppt: 'ppt-latn-pg',
  pqa: 'pqa-latn-ng',
  pqm: 'pqm-latn-ca',
  pra: 'pra-khar-pk',
  prc: 'prc-arab-af',
  prd: 'prd-arab-ir',
  pre: 'pre-latn-st',
  prf: 'prf-latn-ph',
  prg: 'prg-latn-001',
  prh: 'prh-latn-ph',
  pri: 'pri-latn-nc',
  prk: 'prk-latn-mm',
  prm: 'prm-latn-pg',
  pro: 'pro-latn-fr',
  prp: 'prp-gujr-in',
  prq: 'prq-latn-pe',
  prr: 'prr-latn-br',
  prt: 'prt-thai-th',
  pru: 'pru-latn-id',
  prw: 'prw-latn-pg',
  prx: 'prx-arab-in',
  'prx-tibt': 'prx-tibt-in',
  ps: 'ps-arab-af',
  psa: 'psa-latn-id',
  pse: 'pse-latn-id',
  psh: 'psh-arab-af',
  psi: 'psi-arab-af',
  psm: 'psm-latn-bo',
  psn: 'psn-latn-id',
  psq: 'psq-latn-pg',
  pss: 'pss-latn-zz',
  pst: 'pst-arab-pk',
  psw: 'psw-latn-vu',
  pt: 'pt-latn-br',
  pta: 'pta-latn-py',
  pth: 'pth-latn-br',
  pti: 'pti-latn-au',
  ptn: 'ptn-latn-id',
  pto: 'pto-latn-br',
  ptp: 'ptp-latn-zz',
  ptr: 'ptr-latn-vu',
  ptt: 'ptt-latn-id',
  ptu: 'ptu-latn-id',
  ptv: 'ptv-latn-vu',
  pua: 'pua-latn-mx',
  pub: 'pub-latn-in',
  puc: 'puc-latn-id',
  pud: 'pud-latn-id',
  pue: 'pue-latn-ar',
  puf: 'puf-latn-id',
  pug: 'pug-latn-bf',
  pui: 'pui-latn-co',
  puj: 'puj-latn-id',
  pum: 'pum-deva-np',
  puo: 'puo-latn-vn',
  pup: 'pup-latn-pg',
  puq: 'puq-latn-pe',
  pur: 'pur-latn-br',
  put: 'put-latn-id',
  puu: 'puu-latn-ga',
  puw: 'puw-latn-fm',
  pux: 'pux-latn-pg',
  puy: 'puy-latn-us',
  pwa: 'pwa-latn-zz',
  pwb: 'pwb-latn-ng',
  pwg: 'pwg-latn-pg',
  pwm: 'pwm-latn-ph',
  pwn: 'pwn-latn-tw',
  pwo: 'pwo-mymr-mm',
  pwr: 'pwr-deva-in',
  pww: 'pww-thai-th',
  pxm: 'pxm-latn-mx',
  pye: 'pye-latn-ci',
  pym: 'pym-latn-ng',
  pyn: 'pyn-latn-br',
  pyu: 'pyu-latn-tw',
  'pyu-hani': 'pyu-hani-tw',
  pyx: 'pyx-mymr-mm',
  pyy: 'pyy-latn-mm',
  pzh: 'pzh-latn-tw',
  pzn: 'pzn-latn-mm',
  qu: 'qu-latn-pe',
  qua: 'qua-latn-us',
  qub: 'qub-latn-pe',
  quc: 'quc-latn-gt',
  qud: 'qud-latn-ec',
  quf: 'quf-latn-pe',
  qug: 'qug-latn-ec',
  qui: 'qui-latn-us',
  quk: 'quk-latn-pe',
  qul: 'qul-latn-bo',
  qum: 'qum-latn-gt',
  qun: 'qun-latn-us',
  qup: 'qup-latn-pe',
  quq: 'quq-latn-es',
  qur: 'qur-latn-pe',
  qus: 'qus-latn-ar',
  quv: 'quv-latn-gt',
  quw: 'quw-latn-ec',
  qux: 'qux-latn-pe',
  quy: 'quy-latn-pe',
  qva: 'qva-latn-pe',
  qvc: 'qvc-latn-pe',
  qve: 'qve-latn-pe',
  qvh: 'qvh-latn-pe',
  qvi: 'qvi-latn-ec',
  qvj: 'qvj-latn-ec',
  qvl: 'qvl-latn-pe',
  qvm: 'qvm-latn-pe',
  qvn: 'qvn-latn-pe',
  qvo: 'qvo-latn-pe',
  qvp: 'qvp-latn-pe',
  qvs: 'qvs-latn-pe',
  qvw: 'qvw-latn-pe',
  qvz: 'qvz-latn-ec',
  qwa: 'qwa-latn-pe',
  qwc: 'qwc-latn-pe',
  qwh: 'qwh-latn-pe',
  qwm: 'qwm-latn-ru',
  'qwm-cyrl': 'qwm-cyrl-ru',
  'qwm-runr': 'qwm-runr-ru',
  qws: 'qws-latn-pe',
  qwt: 'qwt-latn-us',
  qxa: 'qxa-latn-pe',
  qxc: 'qxc-latn-pe',
  qxh: 'qxh-latn-pe',
  qxl: 'qxl-latn-ec',
  qxn: 'qxn-latn-pe',
  qxo: 'qxo-latn-pe',
  qxp: 'qxp-latn-pe',
  qxq: 'qxq-arab-ir',
  qxr: 'qxr-latn-ec',
  qxt: 'qxt-latn-pe',
  qxu: 'qxu-latn-pe',
  qxw: 'qxw-latn-pe',
  qya: 'qya-latn-001',
  qyp: 'qyp-latn-us',
  raa: 'raa-deva-np',
  rab: 'rab-deva-np',
  rac: 'rac-latn-id',
  rad: 'rad-latn-vn',
  raf: 'raf-deva-np',
  rag: 'rag-latn-ke',
  rah: 'rah-beng-in',
  'rah-latn': 'rah-latn-in',
  rai: 'rai-latn-zz',
  raj: 'raj-deva-in',
  rak: 'rak-latn-pg',
  ram: 'ram-latn-br',
  ran: 'ran-latn-id',
  rao: 'rao-latn-zz',
  rap: 'rap-latn-cl',
  rar: 'rar-latn-ck',
  rav: 'rav-deva-np',
  raw: 'raw-latn-mm',
  rax: 'rax-latn-ng',
  ray: 'ray-latn-pf',
  raz: 'raz-latn-id',
  rbb: 'rbb-mymr-mm',
  rbk: 'rbk-latn-ph',
  rbl: 'rbl-latn-ph',
  rbp: 'rbp-latn-au',
  rcf: 'rcf-latn-re',
  rdb: 'rdb-arab-ir',
  rea: 'rea-latn-pg',
  reb: 'reb-latn-id',
  ree: 'ree-latn-my',
  reg: 'reg-latn-tz',
  rei: 'rei-orya-in',
  'rei-telu': 'rei-telu-in',
  rej: 'rej-latn-id',
  rel: 'rel-latn-zz',
  rem: 'rem-latn-pe',
  ren: 'ren-latn-vn',
  res: 'res-latn-zz',
  ret: 'ret-latn-id',
  rey: 'rey-latn-bo',
  rga: 'rga-latn-vu',
  rgn: 'rgn-latn-it',
  rgr: 'rgr-latn-pe',
  rgs: 'rgs-latn-vn',
  rgu: 'rgu-latn-id',
  rhg: 'rhg-rohg-mm',
  rhp: 'rhp-latn-pg',
  ria: 'ria-latn-in',
  rif: 'rif-latn-ma',
  ril: 'ril-latn-mm',
  rim: 'rim-latn-tz',
  rin: 'rin-latn-ng',
  rir: 'rir-latn-id',
  rit: 'rit-latn-au',
  riu: 'riu-latn-id',
  rjg: 'rjg-latn-id',
  rji: 'rji-deva-np',
  rjs: 'rjs-deva-np',
  rka: 'rka-khmr-kh',
  rkb: 'rkb-latn-br',
  rkh: 'rkh-latn-ck',
  rki: 'rki-mymr-mm',
  rkm: 'rkm-latn-bf',
  rkt: 'rkt-beng-bd',
  rkw: 'rkw-latn-au',
  rm: 'rm-latn-ch',
  rma: 'rma-latn-ni',
  rmb: 'rmb-latn-au',
  rmc: 'rmc-latn-sk',
  rmd: 'rmd-latn-dk',
  rme: 'rme-latn-gb',
  rmf: 'rmf-latn-fi',
  rmg: 'rmg-latn-no',
  rmh: 'rmh-latn-id',
  rmi: 'rmi-armn-am',
  rmk: 'rmk-latn-pg',
  rml: 'rml-latn-pl',
  'rml-cyrl': 'rml-cyrl-by',
  rmm: 'rmm-latn-id',
  rmn: 'rmn-latn-rs',
  'rmn-cyrl': 'rmn-cyrl-bg',
  'rmn-grek': 'rmn-grek-gr',
  rmo: 'rmo-latn-ch',
  rmp: 'rmp-latn-pg',
  rmq: 'rmq-latn-es',
  rmt: 'rmt-arab-ir',
  rmu: 'rmu-latn-se',
  rmw: 'rmw-latn-gb',
  rmx: 'rmx-latn-vn',
  rmz: 'rmz-mymr-in',
  rn: 'rn-latn-bi',
  rna: 'rna-latn-zz',
  rnd: 'rnd-latn-cd',
  rng: 'rng-latn-mz',
  rnl: 'rnl-latn-in',
  rnn: 'rnn-latn-id',
  rnr: 'rnr-latn-au',
  rnw: 'rnw-latn-tz',
  ro: 'ro-latn-ro',
  rob: 'rob-latn-id',
  roc: 'roc-latn-vn',
  rod: 'rod-latn-ng',
  roe: 'roe-latn-pg',
  rof: 'rof-latn-tz',
  rog: 'rog-latn-vn',
  rol: 'rol-latn-ph',
  rom: 'rom-latn-ro',
  'rom-cyrl': 'rom-cyrl-ro',
  roo: 'roo-latn-zz',
  rop: 'rop-latn-au',
  ror: 'ror-latn-id',
  rou: 'rou-latn-td',
  row: 'row-latn-id',
  rpn: 'rpn-latn-vu',
  rpt: 'rpt-latn-pg',
  rri: 'rri-latn-sb',
  rro: 'rro-latn-zz',
  rrt: 'rrt-latn-au',
  rsk: 'rsk-cyrl-rs',
  rtc: 'rtc-latn-mm',
  rth: 'rth-latn-id',
  rtm: 'rtm-latn-fj',
  rtw: 'rtw-deva-in',
  ru: 'ru-cyrl-ru',
  rub: 'rub-latn-ug',
  ruc: 'ruc-latn-ug',
  rue: 'rue-cyrl-ua',
  ruf: 'ruf-latn-tz',
  rug: 'rug-latn-sb',
  rui: 'rui-latn-tz',
  ruk: 'ruk-latn-ng',
  ruo: 'ruo-latn-hr',
  rup: 'rup-latn-ro',
  'rup-grek': 'rup-grek-gr',
  ruq: 'ruq-latn-gr',
  rut: 'rut-cyrl-ru',
  'rut-latn': 'rut-latn-az',
  ruu: 'ruu-latn-my',
  ruy: 'ruy-latn-ng',
  ruz: 'ruz-latn-ng',
  rw: 'rw-latn-rw',
  rwa: 'rwa-latn-pg',
  rwk: 'rwk-latn-tz',
  rwl: 'rwl-latn-tz',
  rwm: 'rwm-latn-ug',
  rwo: 'rwo-latn-zz',
  rwr: 'rwr-deva-in',
  rxd: 'rxd-latn-au',
  rxw: 'rxw-latn-au',
  ryu: 'ryu-kana-jp',
  sa: 'sa-deva-in',
  saa: 'saa-latn-td',
  sab: 'sab-latn-pa',
  sac: 'sac-latn-us',
  sad: 'sad-latn-tz',
  sae: 'sae-latn-br',
  saf: 'saf-latn-gh',
  sah: 'sah-cyrl-ru',
  saj: 'saj-latn-id',
  sak: 'sak-latn-ga',
  sam: 'sam-samr-ps',
  'sam-hebr': 'sam-hebr-ps',
  'sam-syrc': 'sam-syrc-ps',
  sao: 'sao-latn-id',
  saq: 'saq-latn-ke',
  sar: 'sar-latn-bo',
  sas: 'sas-latn-id',
  sat: 'sat-olck-in',
  sau: 'sau-latn-id',
  sav: 'sav-latn-sn',
  saw: 'saw-latn-id',
  sax: 'sax-latn-vu',
  say: 'say-latn-ng',
  saz: 'saz-saur-in',
  sba: 'sba-latn-zz',
  sbb: 'sbb-latn-sb',
  sbc: 'sbc-latn-pg',
  sbd: 'sbd-latn-bf',
  sbe: 'sbe-latn-zz',
  sbg: 'sbg-latn-id',
  sbh: 'sbh-latn-pg',
  sbi: 'sbi-latn-pg',
  sbj: 'sbj-latn-td',
  sbk: 'sbk-latn-tz',
  sbl: 'sbl-latn-ph',
  sbm: 'sbm-latn-tz',
  sbn: 'sbn-arab-pk',
  sbo: 'sbo-latn-my',
  sbp: 'sbp-latn-tz',
  sbq: 'sbq-latn-pg',
  sbr: 'sbr-latn-id',
  sbs: 'sbs-latn-na',
  sbt: 'sbt-latn-id',
  sbu: 'sbu-tibt-in',
  'sbu-deva': 'sbu-deva-in',
  sbv: 'sbv-latn-it',
  sbw: 'sbw-latn-ga',
  sbx: 'sbx-latn-id',
  sby: 'sby-latn-zm',
  sbz: 'sbz-latn-cf',
  sc: 'sc-latn-it',
  scb: 'scb-latn-vn',
  sce: 'sce-latn-cn',
  'sce-arab': 'sce-arab-cn',
  scf: 'scf-latn-pa',
  scg: 'scg-latn-id',
  sch: 'sch-latn-in',
  sci: 'sci-latn-lk',
  sck: 'sck-deva-in',
  scl: 'scl-arab-zz',
  scn: 'scn-latn-it',
  sco: 'sco-latn-gb',
  scp: 'scp-deva-np',
  scs: 'scs-latn-ca',
  'scs-cans': 'scs-cans-ca',
  sct: 'sct-laoo-la',
  scu: 'scu-takr-in',
  scv: 'scv-latn-ng',
  scw: 'scw-latn-ng',
  scx: 'scx-grek-it',
  sd: 'sd-arab-pk',
  'sd-deva': 'sd-deva-in',
  'sd-in': 'sd-deva-in',
  'sd-khoj': 'sd-khoj-in',
  'sd-sind': 'sd-sind-in',
  sda: 'sda-latn-id',
  sdb: 'sdb-arab-iq',
  sdc: 'sdc-latn-it',
  sde: 'sde-latn-ng',
  sdf: 'sdf-arab-iq',
  sdg: 'sdg-arab-af',
  sdh: 'sdh-arab-ir',
  sdj: 'sdj-latn-cg',
  sdk: 'sdk-latn-pg',
  sdn: 'sdn-latn-it',
  sdo: 'sdo-latn-my',
  sdq: 'sdq-latn-id',
  sds: 'sds-arab-tn',
  sdu: 'sdu-latn-id',
  sdx: 'sdx-latn-my',
  se: 'se-latn-no',
  sea: 'sea-latn-my',
  seb: 'seb-latn-ci',
  sec: 'sec-latn-ca',
  sed: 'sed-latn-vn',
  see: 'see-latn-us',
  sef: 'sef-latn-ci',
  seg: 'seg-latn-tz',
  seh: 'seh-latn-mz',
  sei: 'sei-latn-mx',
  sej: 'sej-latn-pg',
  sek: 'sek-latn-ca',
  'sek-cans': 'sek-cans-ca',
  sel: 'sel-cyrl-ru',
  sen: 'sen-latn-bf',
  seo: 'seo-latn-pg',
  sep: 'sep-latn-bf',
  seq: 'seq-latn-bf',
  ser: 'ser-latn-us',
  ses: 'ses-latn-ml',
  set: 'set-latn-id',
  seu: 'seu-latn-id',
  sev: 'sev-latn-ci',
  sew: 'sew-latn-pg',
  sey: 'sey-latn-ec',
  sez: 'sez-latn-mm',
  sfe: 'sfe-latn-ph',
  sfm: 'sfm-plrd-cn',
  sfw: 'sfw-latn-gh',
  sg: 'sg-latn-cf',
  sga: 'sga-ogam-ie',
  sgb: 'sgb-latn-ph',
  sgc: 'sgc-latn-ke',
  sgd: 'sgd-latn-ph',
  sge: 'sge-latn-id',
  sgh: 'sgh-cyrl-tj',
  'sgh-arab': 'sgh-arab-af',
  'sgh-latn': 'sgh-latn-tj',
  sgi: 'sgi-latn-cm',
  sgj: 'sgj-deva-in',
  sgm: 'sgm-latn-ke',
  sgp: 'sgp-latn-in',
  sgr: 'sgr-arab-ir',
  sgs: 'sgs-latn-lt',
  sgt: 'sgt-tibt-bt',
  sgu: 'sgu-latn-id',
  sgw: 'sgw-ethi-zz',
  sgy: 'sgy-arab-af',
  sgz: 'sgz-latn-zz',
  sha: 'sha-latn-ng',
  shb: 'shb-latn-br',
  shc: 'shc-latn-cd',
  shd: 'shd-arab-pk',
  she: 'she-latn-et',
  shg: 'shg-latn-bw',
  shh: 'shh-latn-us',
  shi: 'shi-tfng-ma',
  shj: 'shj-latn-sd',
  shk: 'shk-latn-zz',
  shm: 'shm-arab-ir',
  shn: 'shn-mymr-mm',
  sho: 'sho-latn-ng',
  shp: 'shp-latn-pe',
  shq: 'shq-latn-zm',
  shr: 'shr-latn-cd',
  shs: 'shs-latn-ca',
  sht: 'sht-latn-us',
  shu: 'shu-arab-zz',
  shv: 'shv-arab-om',
  shw: 'shw-latn-sd',
  shy: 'shy-latn-dz',
  'shy-arab': 'shy-arab-dz',
  'shy-tfng': 'shy-tfng-dz',
  shz: 'shz-latn-ml',
  si: 'si-sinh-lk',
  sia: 'sia-cyrl-ru',
  sib: 'sib-latn-my',
  sid: 'sid-latn-et',
  sie: 'sie-latn-zm',
  sif: 'sif-latn-bf',
  sig: 'sig-latn-zz',
  sih: 'sih-latn-nc',
  sii: 'sii-latn-in',
  sij: 'sij-latn-pg',
  sik: 'sik-latn-br',
  sil: 'sil-latn-zz',
  sim: 'sim-latn-zz',
  sip: 'sip-tibt-in',
  siq: 'siq-latn-pg',
  sir: 'sir-latn-ng',
  sis: 'sis-latn-us',
  siu: 'siu-latn-pg',
  siv: 'siv-latn-pg',
  siw: 'siw-latn-pg',
  six: 'six-latn-pg',
  siy: 'siy-arab-ir',
  siz: 'siz-arab-eg',
  sja: 'sja-latn-co',
  sjb: 'sjb-latn-id',
  sjd: 'sjd-cyrl-ru',
  sje: 'sje-latn-se',
  sjg: 'sjg-latn-td',
  sjl: 'sjl-latn-in',
  sjm: 'sjm-latn-ph',
  sjp: 'sjp-deva-in',
  'sjp-beng': 'sjp-beng-in',
  sjr: 'sjr-latn-zz',
  sjt: 'sjt-cyrl-ru',
  sju: 'sju-latn-se',
  sjw: 'sjw-latn-us',
  sk: 'sk-latn-sk',
  ska: 'ska-latn-us',
  skb: 'skb-thai-th',
  skc: 'skc-latn-zz',
  skd: 'skd-latn-us',
  ske: 'ske-latn-vu',
  skf: 'skf-latn-br',
  skg: 'skg-latn-mg',
  skh: 'skh-latn-id',
  ski: 'ski-latn-id',
  skj: 'skj-deva-np',
  skm: 'skm-latn-pg',
  skn: 'skn-latn-ph',
  sko: 'sko-latn-id',
  skp: 'skp-latn-my',
  skq: 'skq-latn-bf',
  skr: 'skr-arab-pk',
  sks: 'sks-latn-zz',
  skt: 'skt-latn-cd',
  sku: 'sku-latn-vu',
  skv: 'skv-latn-id',
  skw: 'skw-latn-gy',
  skx: 'skx-latn-id',
  sky: 'sky-latn-sb',
  skz: 'skz-latn-id',
  sl: 'sl-latn-si',
  slc: 'slc-latn-co',
  sld: 'sld-latn-zz',
  slg: 'slg-latn-id',
  slh: 'slh-latn-us',
  sli: 'sli-latn-pl',
  slj: 'slj-latn-br',
  sll: 'sll-latn-zz',
  slm: 'slm-latn-ph',
  sln: 'sln-latn-us',
  slp: 'slp-latn-id',
  slq: 'slq-arab-ir',
  slr: 'slr-latn-cn',
  slu: 'slu-latn-id',
  slw: 'slw-latn-pg',
  slx: 'slx-latn-cd',
  sly: 'sly-latn-id',
  slz: 'slz-latn-id',
  sm: 'sm-latn-ws',
  sma: 'sma-latn-se',
  smb: 'smb-latn-pg',
  smc: 'smc-latn-pg',
  smd: 'smd-latn-ao',
  smf: 'smf-latn-pg',
  smg: 'smg-latn-pg',
  smh: 'smh-yiii-cn',
  smj: 'smj-latn-se',
  smk: 'smk-latn-ph',
  sml: 'sml-latn-ph',
  smn: 'smn-latn-fi',
  smp: 'smp-samr-il',
  smq: 'smq-latn-zz',
  smr: 'smr-latn-id',
  sms: 'sms-latn-fi',
  smt: 'smt-latn-in',
  smu: 'smu-khmr-kh',
  smw: 'smw-latn-id',
  smx: 'smx-latn-cd',
  smy: 'smy-arab-ir',
  smz: 'smz-latn-pg',
  sn: 'sn-latn-zw',
  snb: 'snb-latn-my',
  snc: 'snc-latn-zz',
  sne: 'sne-latn-my',
  snf: 'snf-latn-sn',
  sng: 'sng-latn-cd',
  'sng-brai': 'sng-brai-cd',
  sni: 'sni-latn-pe',
  snj: 'snj-latn-cf',
  snk: 'snk-latn-ml',
  snl: 'snl-latn-ph',
  snm: 'snm-latn-ug',
  snn: 'snn-latn-co',
  sno: 'sno-latn-us',
  snp: 'snp-latn-zz',
  snq: 'snq-latn-ga',
  snr: 'snr-latn-pg',
  sns: 'sns-latn-vu',
  snu: 'snu-latn-id',
  snv: 'snv-latn-my',
  snw: 'snw-latn-gh',
  snx: 'snx-latn-zz',
  sny: 'sny-latn-zz',
  snz: 'snz-latn-pg',
  so: 'so-latn-so',
  soa: 'soa-tavt-th',
  'soa-thai': 'soa-thai-th',
  sob: 'sob-latn-id',
  soc: 'soc-latn-cd',
  sod: 'sod-latn-cd',
  soe: 'soe-latn-cd',
  sog: 'sog-sogd-uz',
  soi: 'soi-deva-np',
  sok: 'sok-latn-zz',
  sol: 'sol-latn-pg',
  soo: 'soo-latn-cd',
  sop: 'sop-latn-cd',
  soq: 'soq-latn-zz',
  sor: 'sor-latn-td',
  sos: 'sos-latn-bf',
  sou: 'sou-thai-th',
  sov: 'sov-latn-pw',
  sow: 'sow-latn-pg',
  sox: 'sox-latn-cm',
  soy: 'soy-latn-zz',
  soz: 'soz-latn-tz',
  spb: 'spb-latn-id',
  spc: 'spc-latn-ve',
  spd: 'spd-latn-zz',
  spe: 'spe-latn-pg',
  spg: 'spg-latn-my',
  spi: 'spi-latn-id',
  spk: 'spk-latn-pg',
  spl: 'spl-latn-zz',
  spm: 'spm-latn-pg',
  spn: 'spn-latn-py',
  spo: 'spo-latn-us',
  spp: 'spp-latn-ml',
  spq: 'spq-latn-pe',
  spr: 'spr-latn-id',
  sps: 'sps-latn-zz',
  spt: 'spt-tibt-in',
  spv: 'spv-orya-in',
  sq: 'sq-latn-al',
  sqa: 'sqa-latn-ng',
  sqh: 'sqh-latn-ng',
  sqm: 'sqm-latn-cf',
  sqo: 'sqo-arab-ir',
  sqq: 'sqq-laoo-la',
  sqt: 'sqt-arab-ye',
  'sqt-latn': 'sqt-latn-ye',
  squ: 'squ-latn-ca',
  sr: 'sr-cyrl-rs',
  'sr-me': 'sr-latn-me',
  'sr-ro': 'sr-latn-ro',
  'sr-ru': 'sr-latn-ru',
  'sr-tr': 'sr-latn-tr',
  sra: 'sra-latn-pg',
  srb: 'srb-sora-in',
  sre: 'sre-latn-id',
  srf: 'srf-latn-pg',
  srg: 'srg-latn-ph',
  srh: 'srh-arab-cn',
  sri: 'sri-latn-co',
  srk: 'srk-latn-my',
  srl: 'srl-latn-id',
  srm: 'srm-latn-sr',
  srn: 'srn-latn-sr',
  sro: 'sro-latn-it',
  srq: 'srq-latn-bo',
  srr: 'srr-latn-sn',
  srs: 'srs-latn-ca',
  srt: 'srt-latn-id',
  sru: 'sru-latn-br',
  srv: 'srv-latn-ph',
  srw: 'srw-latn-id',
  srx: 'srx-deva-in',
  sry: 'sry-latn-pg',
  srz: 'srz-arab-ir',
  ss: 'ss-latn-za',
  ssb: 'ssb-latn-ph',
  ssc: 'ssc-latn-tz',
  ssd: 'ssd-latn-zz',
  sse: 'sse-latn-ph',
  'sse-arab': 'sse-arab-ph',
  ssf: 'ssf-latn-tw',
  ssg: 'ssg-latn-zz',
  ssh: 'ssh-arab-ae',
  ssj: 'ssj-latn-pg',
  ssl: 'ssl-latn-gh',
  ssm: 'ssm-latn-my',
  ssn: 'ssn-latn-ke',
  sso: 'sso-latn-pg',
  ssq: 'ssq-latn-id',
  sss: 'sss-laoo-la',
  'sss-thai': 'sss-thai-th',
  sst: 'sst-latn-pg',
  ssu: 'ssu-latn-pg',
  ssv: 'ssv-latn-vu',
  ssx: 'ssx-latn-pg',
  ssy: 'ssy-latn-er',
  ssz: 'ssz-latn-pg',
  st: 'st-latn-za',
  sta: 'sta-latn-zm',
  stb: 'stb-latn-ph',
  ste: 'ste-latn-id',
  stf: 'stf-latn-pg',
  stg: 'stg-latn-vn',
  sth: 'sth-latn-ie',
  sti: 'sti-latn-vn',
  'sti-kh': 'sti-latn-kh',
  stj: 'stj-latn-bf',
  stk: 'stk-latn-zz',
  stl: 'stl-latn-nl',
  stm: 'stm-latn-pg',
  stn: 'stn-latn-sb',
  sto: 'sto-latn-ca',
  stp: 'stp-latn-mx',
  stq: 'stq-latn-de',
  str: 'str-latn-ca',
  sts: 'sts-arab-af',
  stt: 'stt-latn-vn',
  stv: 'stv-ethi-et',
  'stv-arab': 'stv-arab-et',
  stw: 'stw-latn-fm',
  sty: 'sty-cyrl-ru',
  su: 'su-latn-id',
  sua: 'sua-latn-zz',
  sub: 'sub-latn-cd',
  suc: 'suc-latn-ph',
  sue: 'sue-latn-zz',
  sug: 'sug-latn-pg',
  sui: 'sui-latn-pg',
  suj: 'suj-latn-tz',
  suk: 'suk-latn-tz',
  suo: 'suo-latn-pg',
  suq: 'suq-latn-et',
  'suq-ethi': 'suq-ethi-et',
  sur: 'sur-latn-zz',
  sus: 'sus-latn-gn',
  sut: 'sut-latn-ni',
  suv: 'suv-latn-in',
  'suv-beng': 'suv-beng-in',
  'suv-deva': 'suv-deva-in',
  suw: 'suw-latn-tz',
  suy: 'suy-latn-br',
  suz: 'suz-deva-np',
  sv: 'sv-latn-se',
  sva: 'sva-geor-ge',
  'sva-cyrl': 'sva-cyrl-ge',
  'sva-latn': 'sva-latn-ge',
  svb: 'svb-latn-pg',
  svc: 'svc-latn-vc',
  sve: 'sve-latn-id',
  svm: 'svm-latn-it',
  svs: 'svs-latn-sb',
  sw: 'sw-latn-tz',
  swb: 'swb-arab-yt',
  swc: 'swc-latn-cd',
  swf: 'swf-latn-cd',
  swg: 'swg-latn-de',
  swi: 'swi-hani-cn',
  swj: 'swj-latn-ga',
  swk: 'swk-latn-mw',
  swm: 'swm-latn-pg',
  swo: 'swo-latn-br',
  swp: 'swp-latn-zz',
  swq: 'swq-latn-cm',
  swr: 'swr-latn-id',
  sws: 'sws-latn-id',
  swt: 'swt-latn-id',
  swu: 'swu-latn-id',
  swv: 'swv-deva-in',
  sww: 'sww-latn-vu',
  swx: 'swx-latn-br',
  swy: 'swy-latn-td',
  sxb: 'sxb-latn-ke',
  sxe: 'sxe-latn-ga',
  sxn: 'sxn-latn-id',
  sxr: 'sxr-latn-tw',
  sxs: 'sxs-latn-ng',
  sxu: 'sxu-latn-de',
  'sxu-runr': 'sxu-runr-de',
  sxw: 'sxw-latn-zz',
  sya: 'sya-latn-id',
  syb: 'syb-latn-ph',
  syc: 'syc-syrc-tr',
  syi: 'syi-latn-ga',
  syk: 'syk-latn-ng',
  syl: 'syl-beng-bd',
  sym: 'sym-latn-bf',
  syn: 'syn-syrc-ir',
  syo: 'syo-latn-kh',
  syr: 'syr-syrc-iq',
  sys: 'sys-latn-td',
  syw: 'syw-deva-np',
  syx: 'syx-latn-ga',
  sza: 'sza-latn-my',
  szb: 'szb-latn-id',
  szc: 'szc-latn-my',
  szd: 'szd-latn-my',
  szg: 'szg-latn-cd',
  szl: 'szl-latn-pl',
  szn: 'szn-latn-id',
  szp: 'szp-latn-id',
  szv: 'szv-latn-cm',
  szw: 'szw-latn-id',
  szy: 'szy-latn-tw',
  ta: 'ta-taml-in',
  taa: 'taa-latn-us',
  tab: 'tab-cyrl-ru',
  tac: 'tac-latn-mx',
  tad: 'tad-latn-id',
  tae: 'tae-latn-br',
  taf: 'taf-latn-br',
  tag: 'tag-latn-sd',
  taj: 'taj-deva-np',
  tak: 'tak-latn-ng',
  tal: 'tal-latn-zz',
  tan: 'tan-latn-zz',
  tao: 'tao-latn-tw',
  tap: 'tap-latn-cd',
  taq: 'taq-latn-zz',
  tar: 'tar-latn-mx',
  tas: 'tas-latn-vn',
  tau: 'tau-latn-us',
  tav: 'tav-latn-co',
  taw: 'taw-latn-pg',
  tax: 'tax-latn-td',
  tay: 'tay-latn-tw',
  'tay-hans': 'tay-hans-tw',
  'tay-hant': 'tay-hant-tw',
  taz: 'taz-latn-sd',
  tba: 'tba-latn-br',
  tbc: 'tbc-latn-zz',
  tbd: 'tbd-latn-zz',
  tbe: 'tbe-latn-sb',
  tbf: 'tbf-latn-zz',
  tbg: 'tbg-latn-zz',
  tbh: 'tbh-latn-au',
  tbi: 'tbi-latn-sd',
  tbj: 'tbj-latn-pg',
  tbk: 'tbk-tagb-ph',
  'tbk-hano': 'tbk-hano-ph',
  'tbk-latn': 'tbk-latn-ph',
  tbl: 'tbl-latn-ph',
  tbm: 'tbm-latn-cd',
  tbn: 'tbn-latn-co',
  tbo: 'tbo-latn-zz',
  tbp: 'tbp-latn-id',
  tbs: 'tbs-latn-pg',
  tbt: 'tbt-latn-cd',
  tbu: 'tbu-latn-mx',
  tbv: 'tbv-latn-pg',
  tbw: 'tbw-latn-ph',
  tbx: 'tbx-latn-pg',
  tby: 'tby-latn-id',
  tbz: 'tbz-latn-zz',
  tca: 'tca-latn-br',
  tcb: 'tcb-latn-us',
  tcc: 'tcc-latn-tz',
  tcd: 'tcd-latn-gh',
  tce: 'tce-latn-ca',
  tcf: 'tcf-latn-mx',
  tcg: 'tcg-latn-id',
  tch: 'tch-latn-tc',
  tci: 'tci-latn-zz',
  tck: 'tck-latn-ga',
  tcm: 'tcm-latn-id',
  tcn: 'tcn-tibt-np',
  tco: 'tco-mymr-mm',
  tcp: 'tcp-latn-mm',
  tcq: 'tcq-latn-id',
  tcs: 'tcs-latn-au',
  tcu: 'tcu-latn-mx',
  tcw: 'tcw-latn-mx',
  tcx: 'tcx-taml-in',
  tcy: 'tcy-knda-in',
  tcz: 'tcz-latn-in',
  tda: 'tda-tfng-ne',
  'tda-arab': 'tda-arab-ne',
  'tda-latn': 'tda-latn-ne',
  tdb: 'tdb-deva-in',
  'tdb-beng': 'tdb-beng-in',
  'tdb-kthi': 'tdb-kthi-in',
  tdc: 'tdc-latn-co',
  tdd: 'tdd-tale-cn',
  tde: 'tde-latn-ml',
  tdg: 'tdg-deva-np',
  tdh: 'tdh-deva-np',
  tdi: 'tdi-latn-id',
  tdj: 'tdj-latn-id',
  tdk: 'tdk-latn-ng',
  tdl: 'tdl-latn-ng',
  tdm: 'tdm-latn-gy',
  tdn: 'tdn-latn-id',
  tdo: 'tdo-latn-ng',
  tdq: 'tdq-latn-ng',
  tdr: 'tdr-latn-vn',
  tds: 'tds-latn-id',
  tdt: 'tdt-latn-tl',
  tdu: 'tdu-latn-my',
  tdv: 'tdv-latn-ng',
  tdx: 'tdx-latn-mg',
  tdy: 'tdy-latn-ph',
  te: 'te-telu-in',
  tea: 'tea-latn-my',
  teb: 'teb-latn-ec',
  tec: 'tec-latn-ke',
  ted: 'ted-latn-zz',
  tee: 'tee-latn-mx',
  teg: 'teg-latn-ga',
  teh: 'teh-latn-ar',
  tei: 'tei-latn-pg',
  tek: 'tek-latn-cd',
  tem: 'tem-latn-sl',
  ten: 'ten-latn-co',
  teo: 'teo-latn-ug',
  tep: 'tep-latn-mx',
  teq: 'teq-latn-sd',
  ter: 'ter-latn-br',
  tes: 'tes-java-id',
  tet: 'tet-latn-tl',
  teu: 'teu-latn-ug',
  tev: 'tev-latn-id',
  tew: 'tew-latn-us',
  tex: 'tex-latn-ss',
  tey: 'tey-latn-sd',
  tfi: 'tfi-latn-zz',
  tfn: 'tfn-latn-us',
  tfo: 'tfo-latn-id',
  tfr: 'tfr-latn-pa',
  tft: 'tft-latn-id',
  tg: 'tg-cyrl-tj',
  'tg-arab': 'tg-arab-pk',
  'tg-pk': 'tg-arab-pk',
  tga: 'tga-latn-ke',
  tgb: 'tgb-latn-my',
  tgc: 'tgc-latn-zz',
  tgd: 'tgd-latn-ng',
  tge: 'tge-deva-np',
  tgf: 'tgf-tibt-bt',
  tgh: 'tgh-latn-tt',
  tgi: 'tgi-latn-pg',
  tgj: 'tgj-latn-in',
  tgn: 'tgn-latn-ph',
  tgo: 'tgo-latn-zz',
  tgp: 'tgp-latn-vu',
  tgq: 'tgq-latn-my',
  tgs: 'tgs-latn-vu',
  tgt: 'tgt-latn-ph',
  'tgt-hano': 'tgt-hano-ph',
  'tgt-tagb': 'tgt-tagb-ph',
  tgu: 'tgu-latn-zz',
  tgv: 'tgv-latn-br',
  tgw: 'tgw-latn-ci',
  tgx: 'tgx-latn-ca',
  tgy: 'tgy-latn-ss',
  tgz: 'tgz-latn-au',
  th: 'th-thai-th',
  thd: 'thd-latn-au',
  the: 'the-deva-np',
  thf: 'thf-deva-np',
  thh: 'thh-latn-mx',
  thi: 'thi-tale-la',
  thk: 'thk-latn-ke',
  thl: 'thl-deva-np',
  thm: 'thm-thai-th',
  thp: 'thp-latn-ca',
  'thp-dupl': 'thp-dupl-ca',
  thq: 'thq-deva-np',
  thr: 'thr-deva-np',
  ths: 'ths-deva-np',
  tht: 'tht-latn-ca',
  thu: 'thu-latn-ss',
  thv: 'thv-latn-dz',
  'thv-arab': 'thv-arab-dz',
  'thv-tfng': 'thv-tfng-dz',
  thy: 'thy-latn-ng',
  thz: 'thz-latn-ne',
  'thz-tfng': 'thz-tfng-ne',
  ti: 'ti-ethi-et',
  tic: 'tic-latn-sd',
  tif: 'tif-latn-zz',
  tig: 'tig-ethi-er',
  tih: 'tih-latn-my',
  tii: 'tii-latn-cd',
  tij: 'tij-deva-np',
  tik: 'tik-latn-zz',
  til: 'til-latn-us',
  tim: 'tim-latn-zz',
  tin: 'tin-cyrl-ru',
  tio: 'tio-latn-zz',
  tip: 'tip-latn-id',
  tiq: 'tiq-latn-bf',
  tis: 'tis-latn-ph',
  tit: 'tit-latn-co',
  tiu: 'tiu-latn-ph',
  tiv: 'tiv-latn-ng',
  tiw: 'tiw-latn-au',
  tix: 'tix-latn-us',
  tiy: 'tiy-latn-ph',
  tja: 'tja-latn-lr',
  tjg: 'tjg-latn-id',
  tji: 'tji-latn-cn',
  tjj: 'tjj-latn-au',
  tjl: 'tjl-mymr-mm',
  tjn: 'tjn-latn-ci',
  tjo: 'tjo-arab-dz',
  tjp: 'tjp-latn-au',
  tjs: 'tjs-latn-cn',
  tju: 'tju-latn-au',
  tjw: 'tjw-latn-au',
  tk: 'tk-latn-tm',
  tka: 'tka-latn-br',
  tkb: 'tkb-deva-in',
  tkd: 'tkd-latn-tl',
  tke: 'tke-latn-mz',
  tkf: 'tkf-latn-br',
  tkg: 'tkg-latn-mg',
  tkl: 'tkl-latn-tk',
  tkp: 'tkp-latn-sb',
  tkq: 'tkq-latn-ng',
  tkr: 'tkr-latn-az',
  tks: 'tks-arab-ir',
  tkt: 'tkt-deva-np',
  tku: 'tku-latn-mx',
  tkv: 'tkv-latn-pg',
  tkw: 'tkw-latn-sb',
  tkx: 'tkx-latn-id',
  tkz: 'tkz-latn-vn',
  tl: 'tl-latn-ph',
  tla: 'tla-latn-mx',
  tlb: 'tlb-latn-id',
  tlc: 'tlc-latn-mx',
  tld: 'tld-latn-id',
  tlf: 'tlf-latn-zz',
  tlg: 'tlg-latn-id',
  tli: 'tli-latn-us',
  'tli-cyrl': 'tli-cyrl-us',
  tlj: 'tlj-latn-ug',
  tlk: 'tlk-latn-id',
  tll: 'tll-latn-cd',
  tlm: 'tlm-latn-vu',
  tln: 'tln-latn-id',
  tlp: 'tlp-latn-mx',
  tlq: 'tlq-latn-mm',
  tlr: 'tlr-latn-sb',
  tls: 'tls-latn-vu',
  tlt: 'tlt-latn-id',
  tlu: 'tlu-latn-id',
  tlv: 'tlv-latn-id',
  tlx: 'tlx-latn-zz',
  tly: 'tly-latn-az',
  tma: 'tma-latn-td',
  tmb: 'tmb-latn-vu',
  tmc: 'tmc-latn-td',
  tmd: 'tmd-latn-pg',
  tme: 'tme-latn-br',
  tmf: 'tmf-latn-py',
  tmg: 'tmg-latn-id',
  tmh: 'tmh-latn-ne',
  tmi: 'tmi-latn-vu',
  tmj: 'tmj-latn-id',
  tmk: 'tmk-deva-np',
  tml: 'tml-latn-id',
  tmm: 'tmm-latn-vn',
  tmn: 'tmn-latn-id',
  tmo: 'tmo-latn-my',
  tmq: 'tmq-latn-pg',
  tmr: 'tmr-syrc-il',
  tmt: 'tmt-latn-vu',
  tmu: 'tmu-latn-id',
  tmv: 'tmv-latn-cd',
  tmw: 'tmw-latn-my',
  tmy: 'tmy-latn-zz',
  tmz: 'tmz-latn-ve',
  tn: 'tn-latn-za',
  tna: 'tna-latn-bo',
  tnb: 'tnb-latn-co',
  tnc: 'tnc-latn-co',
  tnd: 'tnd-latn-co',
  tng: 'tng-latn-td',
  tnh: 'tnh-latn-zz',
  tni: 'tni-latn-id',
  tnk: 'tnk-latn-vu',
  tnl: 'tnl-latn-vu',
  tnm: 'tnm-latn-id',
  tnn: 'tnn-latn-vu',
  tno: 'tno-latn-bo',
  tnp: 'tnp-latn-vu',
  tnq: 'tnq-latn-pr',
  tnr: 'tnr-latn-sn',
  tns: 'tns-latn-pg',
  tnt: 'tnt-latn-id',
  tnv: 'tnv-cakm-bd',
  tnw: 'tnw-latn-id',
  tnx: 'tnx-latn-sb',
  tny: 'tny-latn-tz',
  to: 'to-latn-to',
  tob: 'tob-latn-ar',
  toc: 'toc-latn-mx',
  tod: 'tod-latn-gn',
  tof: 'tof-latn-zz',
  tog: 'tog-latn-mw',
  toh: 'toh-latn-mz',
  toi: 'toi-latn-zm',
  toj: 'toj-latn-mx',
  tok: 'tok-latn-001',
  tol: 'tol-latn-us',
  tom: 'tom-latn-id',
  too: 'too-latn-mx',
  top: 'top-latn-mx',
  toq: 'toq-latn-zz',
  tor: 'tor-latn-cd',
  tos: 'tos-latn-mx',
  tou: 'tou-latn-vn',
  tov: 'tov-arab-ir',
  tow: 'tow-latn-us',
  tox: 'tox-latn-pw',
  toy: 'toy-latn-id',
  toz: 'toz-latn-cm',
  tpa: 'tpa-latn-pg',
  tpc: 'tpc-latn-mx',
  tpe: 'tpe-latn-bd',
  'tpe-beng': 'tpe-beng-bd',
  tpf: 'tpf-latn-id',
  tpg: 'tpg-latn-id',
  tpi: 'tpi-latn-pg',
  tpj: 'tpj-latn-py',
  tpk: 'tpk-latn-br',
  tpl: 'tpl-latn-mx',
  tpm: 'tpm-latn-zz',
  tpn: 'tpn-latn-br',
  tpp: 'tpp-latn-mx',
  tpr: 'tpr-latn-br',
  tpt: 'tpt-latn-mx',
  tpu: 'tpu-khmr-kh',
  tpv: 'tpv-latn-mp',
  tpx: 'tpx-latn-mx',
  tpy: 'tpy-latn-br',
  tpz: 'tpz-latn-zz',
  tqb: 'tqb-latn-br',
  tql: 'tql-latn-vu',
  tqm: 'tqm-latn-pg',
  tqn: 'tqn-latn-us',
  tqo: 'tqo-latn-zz',
  tqp: 'tqp-latn-pg',
  tqt: 'tqt-latn-mx',
  tqu: 'tqu-latn-sb',
  tqw: 'tqw-latn-us',
  tr: 'tr-latn-tr',
  tra: 'tra-arab-af',
  trb: 'trb-latn-pg',
  trc: 'trc-latn-mx',
  tre: 'tre-latn-id',
  trf: 'trf-latn-tt',
  trg: 'trg-hebr-il',
  trh: 'trh-latn-pg',
  tri: 'tri-latn-sr',
  trj: 'trj-latn-td',
  trl: 'trl-latn-gb',
  trm: 'trm-arab-af',
  trn: 'trn-latn-bo',
  tro: 'tro-latn-in',
  trp: 'trp-latn-in',
  'trp-beng': 'trp-beng-in',
  trq: 'trq-latn-mx',
  trr: 'trr-latn-pe',
  trs: 'trs-latn-mx',
  trt: 'trt-latn-id',
  tru: 'tru-latn-tr',
  trv: 'trv-latn-tw',
  trw: 'trw-arab-pk',
  trx: 'trx-latn-my',
  try: 'try-latn-in',
  trz: 'trz-latn-br',
  ts: 'ts-latn-za',
  tsa: 'tsa-latn-cg',
  tsb: 'tsb-latn-et',
  tsc: 'tsc-latn-mz',
  tsd: 'tsd-grek-gr',
  tsf: 'tsf-deva-np',
  tsg: 'tsg-latn-ph',
  tsh: 'tsh-latn-cm',
  tsi: 'tsi-latn-ca',
  tsj: 'tsj-tibt-bt',
  tsl: 'tsl-latn-vn',
  tsp: 'tsp-latn-bf',
  tsr: 'tsr-latn-vu',
  tst: 'tst-latn-ml',
  tsu: 'tsu-latn-tw',
  tsv: 'tsv-latn-ga',
  tsw: 'tsw-latn-zz',
  tsx: 'tsx-latn-pg',
  tsz: 'tsz-latn-mx',
  tt: 'tt-cyrl-ru',
  ttb: 'ttb-latn-ng',
  ttc: 'ttc-latn-gt',
  ttd: 'ttd-latn-zz',
  tte: 'tte-latn-zz',
  ttf: 'ttf-latn-cm',
  tth: 'tth-laoo-la',
  tti: 'tti-latn-id',
  ttj: 'ttj-latn-ug',
  ttk: 'ttk-latn-co',
  ttl: 'ttl-latn-zm',
  ttm: 'ttm-latn-ca',
  ttn: 'ttn-latn-id',
  tto: 'tto-laoo-la',
  ttp: 'ttp-latn-id',
  ttr: 'ttr-latn-zz',
  tts: 'tts-thai-th',
  ttt: 'ttt-latn-az',
  ttu: 'ttu-latn-pg',
  ttv: 'ttv-latn-pg',
  ttw: 'ttw-latn-my',
  tty: 'tty-latn-id',
  tua: 'tua-latn-pg',
  tub: 'tub-latn-us',
  tuc: 'tuc-latn-pg',
  tud: 'tud-latn-br',
  tue: 'tue-latn-co',
  tuf: 'tuf-latn-co',
  tug: 'tug-latn-td',
  tuh: 'tuh-latn-zz',
  tui: 'tui-latn-cm',
  tuj: 'tuj-latn-id',
  tul: 'tul-latn-zz',
  tum: 'tum-latn-mw',
  tun: 'tun-latn-us',
  tuo: 'tuo-latn-br',
  tuq: 'tuq-latn-zz',
  tus: 'tus-latn-ca',
  tuu: 'tuu-latn-us',
  tuv: 'tuv-latn-ke',
  tux: 'tux-latn-br',
  tuy: 'tuy-latn-ke',
  tuz: 'tuz-latn-bf',
  tva: 'tva-latn-sb',
  tvd: 'tvd-latn-zz',
  tve: 'tve-latn-id',
  tvk: 'tvk-latn-vu',
  tvl: 'tvl-latn-tv',
  tvm: 'tvm-latn-id',
  tvn: 'tvn-mymr-mm',
  tvo: 'tvo-latn-id',
  tvs: 'tvs-latn-ke',
  tvt: 'tvt-latn-in',
  tvu: 'tvu-latn-zz',
  tvw: 'tvw-latn-id',
  tvx: 'tvx-latn-tw',
  twa: 'twa-latn-us',
  twb: 'twb-latn-ph',
  twd: 'twd-latn-nl',
  twe: 'twe-latn-id',
  twf: 'twf-latn-us',
  twg: 'twg-latn-id',
  twh: 'twh-latn-zz',
  twl: 'twl-latn-mz',
  twm: 'twm-deva-in',
  twn: 'twn-latn-cm',
  two: 'two-latn-bw',
  twp: 'twp-latn-pg',
  twq: 'twq-latn-ne',
  twr: 'twr-latn-mx',
  twt: 'twt-latn-br',
  twu: 'twu-latn-id',
  tww: 'tww-latn-pg',
  twx: 'twx-latn-mz',
  twy: 'twy-latn-id',
  txa: 'txa-latn-my',
  txe: 'txe-latn-id',
  txg: 'txg-tang-cn',
  txi: 'txi-latn-br',
  txj: 'txj-latn-ng',
  txm: 'txm-latn-id',
  txn: 'txn-latn-id',
  txo: 'txo-toto-in',
  txq: 'txq-latn-id',
  txs: 'txs-latn-id',
  txt: 'txt-latn-id',
  txu: 'txu-latn-br',
  txx: 'txx-latn-my',
  txy: 'txy-latn-mg',
  ty: 'ty-latn-pf',
  tya: 'tya-latn-zz',
  tye: 'tye-latn-ng',
  tyh: 'tyh-latn-vn',
  tyi: 'tyi-latn-cg',
  tyj: 'tyj-latn-vn',
  tyl: 'tyl-latn-vn',
  tyn: 'tyn-latn-id',
  typ: 'typ-latn-au',
  tyr: 'tyr-tavt-vn',
  tys: 'tys-latn-vn',
  tyt: 'tyt-latn-vn',
  'tyt-tavt': 'tyt-tavt-vn',
  tyu: 'tyu-latn-bw',
  tyv: 'tyv-cyrl-ru',
  tyx: 'tyx-latn-cg',
  tyy: 'tyy-latn-ng',
  tyz: 'tyz-latn-vn',
  tzh: 'tzh-latn-mx',
  tzj: 'tzj-latn-gt',
  tzl: 'tzl-latn-001',
  tzm: 'tzm-latn-ma',
  tzn: 'tzn-latn-id',
  tzo: 'tzo-latn-mx',
  tzx: 'tzx-latn-pg',
  uam: 'uam-latn-br',
  uar: 'uar-latn-pg',
  uba: 'uba-latn-ng',
  ubi: 'ubi-latn-td',
  ubl: 'ubl-latn-ph',
  ubr: 'ubr-latn-pg',
  ubu: 'ubu-latn-zz',
  uda: 'uda-latn-ng',
  ude: 'ude-cyrl-ru',
  udg: 'udg-mlym-in',
  udi: 'udi-aghb-ru',
  udj: 'udj-latn-id',
  udl: 'udl-latn-cm',
  udm: 'udm-cyrl-ru',
  udu: 'udu-latn-sd',
  ues: 'ues-latn-id',
  ufi: 'ufi-latn-pg',
  ug: 'ug-arab-cn',
  'ug-cyrl': 'ug-cyrl-kz',
  'ug-kz': 'ug-cyrl-kz',
  'ug-mn': 'ug-cyrl-mn',
  uga: 'uga-ugar-sy',
  ugb: 'ugb-latn-au',
  uge: 'uge-latn-sb',
  ugh: 'ugh-cyrl-ru',
  ugo: 'ugo-thai-th',
  uha: 'uha-latn-ng',
  uhn: 'uhn-latn-id',
  uis: 'uis-latn-pg',
  uiv: 'uiv-latn-cm',
  uji: 'uji-latn-ng',
  uk: 'uk-cyrl-ua',
  uka: 'uka-latn-id',
  ukg: 'ukg-latn-pg',
  ukh: 'ukh-latn-cf',
  uki: 'uki-orya-in',
  ukk: 'ukk-latn-mm',
  ukp: 'ukp-latn-ng',
  ukq: 'ukq-latn-ng',
  uku: 'uku-latn-ng',
  ukv: 'ukv-latn-ss',
  ukw: 'ukw-latn-ng',
  uky: 'uky-latn-au',
  ula: 'ula-latn-ng',
  ulb: 'ulb-latn-ng',
  ulc: 'ulc-cyrl-ru',
  ule: 'ule-latn-ar',
  ulf: 'ulf-latn-id',
  uli: 'uli-latn-fm',
  ulk: 'ulk-latn-au',
  ulm: 'ulm-latn-id',
  uln: 'uln-latn-pg',
  ulu: 'ulu-latn-id',
  ulw: 'ulw-latn-ni',
  uma: 'uma-latn-us',
  umb: 'umb-latn-ao',
  umd: 'umd-latn-au',
  umg: 'umg-latn-au',
  umi: 'umi-latn-my',
  umm: 'umm-latn-ng',
  umn: 'umn-latn-mm',
  umo: 'umo-latn-br',
  ump: 'ump-latn-au',
  umr: 'umr-latn-au',
  ums: 'ums-latn-id',
  una: 'una-latn-pg',
  und: 'en-latn-us',
  'und-002': 'en-latn-ng',
  'und-003': 'en-latn-us',
  'und-005': 'pt-latn-br',
  'und-009': 'en-latn-au',
  'und-011': 'en-latn-ng',
  'und-013': 'es-latn-mx',
  'und-014': 'sw-latn-tz',
  'und-015': 'ar-arab-eg',
  'und-017': 'sw-latn-cd',
  'und-018': 'en-latn-za',
  'und-019': 'en-latn-us',
  'und-021': 'en-latn-us',
  'und-029': 'es-latn-cu',
  'und-030': 'zh-hans-cn',
  'und-034': 'hi-deva-in',
  'und-035': 'id-latn-id',
  'und-039': 'it-latn-it',
  'und-053': 'en-latn-au',
  'und-054': 'en-latn-pg',
  'und-057': 'en-latn-gu',
  'und-061': 'sm-latn-ws',
  'und-142': 'zh-hans-cn',
  'und-143': 'uz-latn-uz',
  'und-145': 'ar-arab-sa',
  'und-150': 'ru-cyrl-ru',
  'und-151': 'ru-cyrl-ru',
  'und-154': 'en-latn-gb',
  'und-155': 'de-latn-de',
  'und-202': 'en-latn-ng',
  'und-419': 'es-latn-419',
  'und-ad': 'ca-latn-ad',
  'und-adlm': 'ff-adlm-gn',
  'und-ae': 'ar-arab-ae',
  'und-af': 'fa-arab-af',
  'und-aghb': 'udi-aghb-ru',
  'und-ahom': 'aho-ahom-in',
  'und-al': 'sq-latn-al',
  'und-am': 'hy-armn-am',
  'und-ao': 'pt-latn-ao',
  'und-aq': 'und-latn-aq',
  'und-ar': 'es-latn-ar',
  'und-arab': 'ar-arab-eg',
  'und-arab-cc': 'ms-arab-cc',
  'und-arab-cn': 'ug-arab-cn',
  'und-arab-gb': 'ur-arab-gb',
  'und-arab-id': 'ms-arab-id',
  'und-arab-in': 'ur-arab-in',
  'und-arab-kh': 'cja-arab-kh',
  'und-arab-mm': 'rhg-arab-mm',
  'und-arab-mn': 'kk-arab-mn',
  'und-arab-mu': 'ur-arab-mu',
  'und-arab-ng': 'ha-arab-ng',
  'und-arab-pk': 'ur-arab-pk',
  'und-arab-tg': 'apd-arab-tg',
  'und-arab-th': 'mfa-arab-th',
  'und-arab-tj': 'fa-arab-tj',
  'und-arab-tr': 'apc-arab-tr',
  'und-arab-yt': 'swb-arab-yt',
  'und-armi': 'arc-armi-ir',
  'und-armn': 'hy-armn-am',
  'und-as': 'sm-latn-as',
  'und-at': 'de-latn-at',
  'und-avst': 'ae-avst-ir',
  'und-aw': 'nl-latn-aw',
  'und-ax': 'sv-latn-ax',
  'und-az': 'az-latn-az',
  'und-ba': 'bs-latn-ba',
  'und-bali': 'ban-bali-id',
  'und-bamu': 'bax-bamu-cm',
  'und-bass': 'bsq-bass-lr',
  'und-batk': 'bbc-batk-id',
  'und-bd': 'bn-beng-bd',
  'und-be': 'nl-latn-be',
  'und-beng': 'bn-beng-bd',
  'und-bf': 'fr-latn-bf',
  'und-bg': 'bg-cyrl-bg',
  'und-bh': 'ar-arab-bh',
  'und-bhks': 'sa-bhks-in',
  'und-bi': 'rn-latn-bi',
  'und-bj': 'fr-latn-bj',
  'und-bl': 'fr-latn-bl',
  'und-bn': 'ms-latn-bn',
  'und-bo': 'es-latn-bo',
  'und-bopo': 'zh-bopo-tw',
  'und-bq': 'pap-latn-bq',
  'und-br': 'pt-latn-br',
  'und-brah': 'pka-brah-in',
  'und-brai': 'fr-brai-fr',
  'und-bt': 'dz-tibt-bt',
  'und-bugi': 'bug-bugi-id',
  'und-buhd': 'bku-buhd-ph',
  'und-bv': 'und-latn-bv',
  'und-by': 'be-cyrl-by',
  'und-cakm': 'ccp-cakm-bd',
  'und-cans': 'iu-cans-ca',
  'und-cari': 'xcr-cari-tr',
  'und-cd': 'sw-latn-cd',
  'und-cf': 'fr-latn-cf',
  'und-cg': 'fr-latn-cg',
  'und-ch': 'de-latn-ch',
  'und-cham': 'cjm-cham-vn',
  'und-cher': 'chr-cher-us',
  'und-chrs': 'xco-chrs-uz',
  'und-ci': 'fr-latn-ci',
  'und-cl': 'es-latn-cl',
  'und-cm': 'fr-latn-cm',
  'und-cn': 'zh-hans-cn',
  'und-co': 'es-latn-co',
  'und-copt': 'cop-copt-eg',
  'und-cp': 'und-latn-cp',
  'und-cpmn': 'und-cpmn-cy',
  'und-cpmn-cy': 'und-cpmn-cy',
  'und-cprt': 'grc-cprt-cy',
  'und-cr': 'es-latn-cr',
  'und-cu': 'es-latn-cu',
  'und-cv': 'pt-latn-cv',
  'und-cw': 'pap-latn-cw',
  'und-cy': 'el-grek-cy',
  'und-cyrl': 'ru-cyrl-ru',
  'und-cyrl-al': 'mk-cyrl-al',
  'und-cyrl-ba': 'sr-cyrl-ba',
  'und-cyrl-ge': 'ab-cyrl-ge',
  'und-cyrl-gr': 'mk-cyrl-gr',
  'und-cyrl-md': 'uk-cyrl-md',
  'und-cyrl-ro': 'bg-cyrl-ro',
  'und-cyrl-sk': 'uk-cyrl-sk',
  'und-cyrl-tr': 'kbd-cyrl-tr',
  'und-cyrl-xk': 'sr-cyrl-xk',
  'und-cz': 'cs-latn-cz',
  'und-de': 'de-latn-de',
  'und-deva': 'hi-deva-in',
  'und-deva-bt': 'ne-deva-bt',
  'und-deva-fj': 'hif-deva-fj',
  'und-deva-mu': 'bho-deva-mu',
  'und-deva-pk': 'btv-deva-pk',
  'und-diak': 'dv-diak-mv',
  'und-dj': 'aa-latn-dj',
  'und-dk': 'da-latn-dk',
  'und-do': 'es-latn-do',
  'und-dogr': 'doi-dogr-in',
  'und-dupl': 'fr-dupl-fr',
  'und-dz': 'ar-arab-dz',
  'und-ea': 'es-latn-ea',
  'und-ec': 'es-latn-ec',
  'und-ee': 'et-latn-ee',
  'und-eg': 'ar-arab-eg',
  'und-egyp': 'egy-egyp-eg',
  'und-eh': 'ar-arab-eh',
  'und-elba': 'sq-elba-al',
  'und-elym': 'arc-elym-ir',
  'und-er': 'ti-ethi-er',
  'und-es': 'es-latn-es',
  'und-et': 'am-ethi-et',
  'und-ethi': 'am-ethi-et',
  'und-eu': 'en-latn-ie',
  'und-ez': 'de-latn-ez',
  'und-fi': 'fi-latn-fi',
  'und-fo': 'fo-latn-fo',
  'und-fr': 'fr-latn-fr',
  'und-ga': 'fr-latn-ga',
  'und-ge': 'ka-geor-ge',
  'und-geor': 'ka-geor-ge',
  'und-gf': 'fr-latn-gf',
  'und-gh': 'ak-latn-gh',
  'und-gl': 'kl-latn-gl',
  'und-glag': 'cu-glag-bg',
  'und-gn': 'fr-latn-gn',
  'und-gong': 'wsg-gong-in',
  'und-gonm': 'esg-gonm-in',
  'und-goth': 'got-goth-ua',
  'und-gp': 'fr-latn-gp',
  'und-gq': 'es-latn-gq',
  'und-gr': 'el-grek-gr',
  'und-gran': 'sa-gran-in',
  'und-grek': 'el-grek-gr',
  'und-grek-tr': 'bgx-grek-tr',
  'und-gs': 'und-latn-gs',
  'und-gt': 'es-latn-gt',
  'und-gujr': 'gu-gujr-in',
  'und-guru': 'pa-guru-in',
  'und-gw': 'pt-latn-gw',
  'und-hanb': 'zh-hanb-tw',
  'und-hang': 'ko-hang-kr',
  'und-hani': 'zh-hani-cn',
  'und-hano': 'hnn-hano-ph',
  'und-hans': 'zh-hans-cn',
  'und-hant': 'zh-hant-tw',
  'und-hant-ca': 'yue-hant-ca',
  'und-hebr': 'he-hebr-il',
  'und-hebr-se': 'yi-hebr-se',
  'und-hebr-ua': 'yi-hebr-ua',
  'und-hebr-us': 'yi-hebr-us',
  'und-hira': 'ja-hira-jp',
  'und-hk': 'zh-hant-hk',
  'und-hluw': 'hlu-hluw-tr',
  'und-hm': 'und-latn-hm',
  'und-hmng': 'hnj-hmng-la',
  'und-hmnp': 'hnj-hmnp-us',
  'und-hn': 'es-latn-hn',
  'und-hr': 'hr-latn-hr',
  'und-ht': 'ht-latn-ht',
  'und-hu': 'hu-latn-hu',
  'und-hung': 'hu-hung-hu',
  'und-ic': 'es-latn-ic',
  'und-id': 'id-latn-id',
  'und-il': 'he-hebr-il',
  'und-in': 'hi-deva-in',
  'und-iq': 'ar-arab-iq',
  'und-ir': 'fa-arab-ir',
  'und-is': 'is-latn-is',
  'und-it': 'it-latn-it',
  'und-ital': 'ett-ital-it',
  'und-jamo': 'ko-jamo-kr',
  'und-java': 'jv-java-id',
  'und-jo': 'ar-arab-jo',
  'und-jp': 'ja-jpan-jp',
  'und-jpan': 'ja-jpan-jp',
  'und-kali': 'eky-kali-mm',
  'und-kana': 'ja-kana-jp',
  'und-kawi': 'kaw-kawi-id',
  'und-ke': 'sw-latn-ke',
  'und-kg': 'ky-cyrl-kg',
  'und-kh': 'km-khmr-kh',
  'und-khar': 'pra-khar-pk',
  'und-khmr': 'km-khmr-kh',
  'und-khoj': 'sd-khoj-in',
  'und-kits': 'zkt-kits-cn',
  'und-km': 'ar-arab-km',
  'und-knda': 'kn-knda-in',
  'und-kore': 'ko-kore-kr',
  'und-kp': 'ko-kore-kp',
  'und-kr': 'ko-kore-kr',
  'und-kthi': 'bho-kthi-in',
  'und-kw': 'ar-arab-kw',
  'und-kz': 'ru-cyrl-kz',
  'und-la': 'lo-laoo-la',
  'und-lana': 'nod-lana-th',
  'und-laoo': 'lo-laoo-la',
  'und-laoo-au': 'hnj-laoo-au',
  'und-laoo-cn': 'hnj-laoo-cn',
  'und-laoo-fr': 'hnj-laoo-fr',
  'und-laoo-gf': 'hnj-laoo-gf',
  'und-laoo-mm': 'hnj-laoo-mm',
  'und-laoo-sr': 'hnj-laoo-sr',
  'und-laoo-th': 'hnj-laoo-th',
  'und-laoo-us': 'hnj-laoo-us',
  'und-laoo-vn': 'hnj-laoo-vn',
  'und-latn-af': 'tk-latn-af',
  'und-latn-am': 'ku-latn-am',
  'und-latn-cn': 'za-latn-cn',
  'und-latn-cy': 'tr-latn-cy',
  'und-latn-dz': 'fr-latn-dz',
  'und-latn-et': 'en-latn-et',
  'und-latn-ge': 'ku-latn-ge',
  'und-latn-ir': 'tk-latn-ir',
  'und-latn-km': 'fr-latn-km',
  'und-latn-ma': 'fr-latn-ma',
  'und-latn-mk': 'sq-latn-mk',
  'und-latn-mm': 'kac-latn-mm',
  'und-latn-mo': 'pt-latn-mo',
  'und-latn-mr': 'fr-latn-mr',
  'und-latn-ru': 'krl-latn-ru',
  'und-latn-sy': 'fr-latn-sy',
  'und-latn-tn': 'fr-latn-tn',
  'und-latn-tw': 'trv-latn-tw',
  'und-latn-ua': 'pl-latn-ua',
  'und-lb': 'ar-arab-lb',
  'und-lepc': 'lep-lepc-in',
  'und-li': 'de-latn-li',
  'und-limb': 'lif-limb-in',
  'und-lina': 'lab-lina-gr',
  'und-linb': 'grc-linb-gr',
  'und-lisu': 'lis-lisu-cn',
  'und-lk': 'si-sinh-lk',
  'und-ls': 'st-latn-ls',
  'und-lt': 'lt-latn-lt',
  'und-lu': 'fr-latn-lu',
  'und-lv': 'lv-latn-lv',
  'und-ly': 'ar-arab-ly',
  'und-lyci': 'xlc-lyci-tr',
  'und-lydi': 'xld-lydi-tr',
  'und-ma': 'ar-arab-ma',
  'und-mahj': 'hi-mahj-in',
  'und-maka': 'mak-maka-id',
  'und-mand': 'myz-mand-ir',
  'und-mani': 'xmn-mani-cn',
  'und-marc': 'bo-marc-cn',
  'und-mc': 'fr-latn-mc',
  'und-md': 'ro-latn-md',
  'und-me': 'sr-latn-me',
  'und-medf': 'dmf-medf-ng',
  'und-mend': 'men-mend-sl',
  'und-merc': 'xmr-merc-sd',
  'und-mero': 'xmr-mero-sd',
  'und-mf': 'fr-latn-mf',
  'und-mg': 'mg-latn-mg',
  'und-mk': 'mk-cyrl-mk',
  'und-ml': 'bm-latn-ml',
  'und-mlym': 'ml-mlym-in',
  'und-mm': 'my-mymr-mm',
  'und-mn': 'mn-cyrl-mn',
  'und-mo': 'zh-hant-mo',
  'und-modi': 'mr-modi-in',
  'und-mong': 'mn-mong-cn',
  'und-mq': 'fr-latn-mq',
  'und-mr': 'ar-arab-mr',
  'und-mroo': 'mro-mroo-bd',
  'und-mt': 'mt-latn-mt',
  'und-mtei': 'mni-mtei-in',
  'und-mu': 'mfe-latn-mu',
  'und-mult': 'skr-mult-pk',
  'und-mv': 'dv-thaa-mv',
  'und-mx': 'es-latn-mx',
  'und-my': 'ms-latn-my',
  'und-mymr': 'my-mymr-mm',
  'und-mymr-in': 'kht-mymr-in',
  'und-mymr-th': 'mnw-mymr-th',
  'und-mz': 'pt-latn-mz',
  'und-na': 'af-latn-na',
  'und-nagm': 'unr-nagm-in',
  'und-nand': 'sa-nand-in',
  'und-narb': 'xna-narb-sa',
  'und-nbat': 'arc-nbat-jo',
  'und-nc': 'fr-latn-nc',
  'und-ne': 'ha-latn-ne',
  'und-newa': 'new-newa-np',
  'und-ni': 'es-latn-ni',
  'und-nkoo': 'man-nkoo-gn',
  'und-nl': 'nl-latn-nl',
  'und-no': 'nb-latn-no',
  'und-np': 'ne-deva-np',
  'und-nshu': 'zhx-nshu-cn',
  'und-ogam': 'sga-ogam-ie',
  'und-olck': 'sat-olck-in',
  'und-om': 'ar-arab-om',
  'und-orkh': 'otk-orkh-mn',
  'und-orya': 'or-orya-in',
  'und-osge': 'osa-osge-us',
  'und-osma': 'so-osma-so',
  'und-ougr': 'oui-ougr-143',
  'und-pa': 'es-latn-pa',
  'und-palm': 'arc-palm-sy',
  'und-pauc': 'ctd-pauc-mm',
  'und-pe': 'es-latn-pe',
  'und-perm': 'kv-perm-ru',
  'und-pf': 'fr-latn-pf',
  'und-pg': 'tpi-latn-pg',
  'und-ph': 'fil-latn-ph',
  'und-phag': 'lzh-phag-cn',
  'und-phli': 'pal-phli-ir',
  'und-phlp': 'pal-phlp-cn',
  'und-phnx': 'phn-phnx-lb',
  'und-pk': 'ur-arab-pk',
  'und-pl': 'pl-latn-pl',
  'und-plrd': 'hmd-plrd-cn',
  'und-pm': 'fr-latn-pm',
  'und-pr': 'es-latn-pr',
  'und-prti': 'xpr-prti-ir',
  'und-ps': 'ar-arab-ps',
  'und-pt': 'pt-latn-pt',
  'und-pw': 'pau-latn-pw',
  'und-py': 'gn-latn-py',
  'und-qa': 'ar-arab-qa',
  'und-qo': 'en-latn-dg',
  'und-re': 'fr-latn-re',
  'und-rjng': 'rej-rjng-id',
  'und-ro': 'ro-latn-ro',
  'und-rohg': 'rhg-rohg-mm',
  'und-rs': 'sr-cyrl-rs',
  'und-ru': 'ru-cyrl-ru',
  'und-runr': 'non-runr-se',
  'und-rw': 'rw-latn-rw',
  'und-sa': 'ar-arab-sa',
  'und-samr': 'smp-samr-il',
  'und-sarb': 'xsa-sarb-ye',
  'und-saur': 'saz-saur-in',
  'und-sc': 'fr-latn-sc',
  'und-sd': 'ar-arab-sd',
  'und-se': 'sv-latn-se',
  'und-sgnw': 'ase-sgnw-us',
  'und-shaw': 'en-shaw-gb',
  'und-shrd': 'sa-shrd-in',
  'und-si': 'sl-latn-si',
  'und-sidd': 'sa-sidd-in',
  'und-sind': 'sd-sind-in',
  'und-sinh': 'si-sinh-lk',
  'und-sj': 'nb-latn-sj',
  'und-sk': 'sk-latn-sk',
  'und-sm': 'it-latn-sm',
  'und-sn': 'fr-latn-sn',
  'und-so': 'so-latn-so',
  'und-sogd': 'sog-sogd-uz',
  'und-sogo': 'sog-sogo-uz',
  'und-sora': 'srb-sora-in',
  'und-soyo': 'cmg-soyo-mn',
  'und-sr': 'nl-latn-sr',
  'und-st': 'pt-latn-st',
  'und-sund': 'su-sund-id',
  'und-sv': 'es-latn-sv',
  'und-sy': 'ar-arab-sy',
  'und-sylo': 'syl-sylo-bd',
  'und-syrc': 'syr-syrc-iq',
  'und-tagb': 'tbw-tagb-ph',
  'und-takr': 'doi-takr-in',
  'und-tale': 'tdd-tale-cn',
  'und-talu': 'khb-talu-cn',
  'und-taml': 'ta-taml-in',
  'und-tang': 'txg-tang-cn',
  'und-tavt': 'blt-tavt-vn',
  'und-td': 'fr-latn-td',
  'und-telu': 'te-telu-in',
  'und-tf': 'fr-latn-tf',
  'und-tfng': 'zgh-tfng-ma',
  'und-tg': 'fr-latn-tg',
  'und-tglg': 'fil-tglg-ph',
  'und-th': 'th-thai-th',
  'und-thaa': 'dv-thaa-mv',
  'und-thai': 'th-thai-th',
  'und-thai-cn': 'lcp-thai-cn',
  'und-thai-kh': 'kdt-thai-kh',
  'und-thai-la': 'kdt-thai-la',
  'und-tibt': 'bo-tibt-cn',
  'und-tirh': 'mai-tirh-in',
  'und-tj': 'tg-cyrl-tj',
  'und-tk': 'tkl-latn-tk',
  'und-tl': 'pt-latn-tl',
  'und-tm': 'tk-latn-tm',
  'und-tn': 'ar-arab-tn',
  'und-tnsa': 'nst-tnsa-in',
  'und-to': 'to-latn-to',
  'und-toto': 'txo-toto-in',
  'und-tr': 'tr-latn-tr',
  'und-tv': 'tvl-latn-tv',
  'und-tw': 'zh-hant-tw',
  'und-tz': 'sw-latn-tz',
  'und-ua': 'uk-cyrl-ua',
  'und-ug': 'sw-latn-ug',
  'und-ugar': 'uga-ugar-sy',
  'und-uy': 'es-latn-uy',
  'und-uz': 'uz-latn-uz',
  'und-va': 'it-latn-va',
  'und-vaii': 'vai-vaii-lr',
  'und-ve': 'es-latn-ve',
  'und-vith': 'sq-vith-al',
  'und-vn': 'vi-latn-vn',
  'und-vu': 'bi-latn-vu',
  'und-wara': 'hoc-wara-in',
  'und-wcho': 'nnp-wcho-in',
  'und-wf': 'fr-latn-wf',
  'und-ws': 'sm-latn-ws',
  'und-xk': 'sq-latn-xk',
  'und-xpeo': 'peo-xpeo-ir',
  'und-xsux': 'akk-xsux-iq',
  'und-ye': 'ar-arab-ye',
  'und-yezi': 'ku-yezi-ge',
  'und-yiii': 'ii-yiii-cn',
  'und-yt': 'fr-latn-yt',
  'und-zanb': 'cmg-zanb-mn',
  'und-zw': 'sn-latn-zw',
  une: 'une-latn-ng',
  ung: 'ung-latn-au',
  uni: 'uni-latn-pg',
  unk: 'unk-latn-br',
  unm: 'unm-latn-us',
  unn: 'unn-latn-au',
  unr: 'unr-beng-in',
  'unr-deva': 'unr-deva-np',
  'unr-np': 'unr-deva-np',
  unu: 'unu-latn-pg',
  unx: 'unx-beng-in',
  unz: 'unz-latn-id',
  uok: 'uok-latn-zz',
  uon: 'uon-latn-tw',
  upi: 'upi-latn-pg',
  upv: 'upv-latn-vu',
  ur: 'ur-arab-pk',
  ura: 'ura-latn-pe',
  urb: 'urb-latn-br',
  urc: 'urc-latn-au',
  ure: 'ure-latn-bo',
  urf: 'urf-latn-au',
  urg: 'urg-latn-pg',
  urh: 'urh-latn-ng',
  uri: 'uri-latn-zz',
  urk: 'urk-thai-th',
  urm: 'urm-latn-pg',
  urn: 'urn-latn-id',
  uro: 'uro-latn-pg',
  urp: 'urp-latn-br',
  urr: 'urr-latn-vu',
  urt: 'urt-latn-zz',
  uru: 'uru-latn-br',
  urv: 'urv-latn-pg',
  urw: 'urw-latn-zz',
  urx: 'urx-latn-pg',
  ury: 'ury-latn-id',
  urz: 'urz-latn-br',
  usa: 'usa-latn-zz',
  ush: 'ush-arab-pk',
  usi: 'usi-latn-bd',
  'usi-beng': 'usi-beng-bd',
  usk: 'usk-latn-cm',
  usp: 'usp-latn-gt',
  uss: 'uss-latn-ng',
  usu: 'usu-latn-pg',
  uta: 'uta-latn-ng',
  ute: 'ute-latn-us',
  uth: 'uth-latn-zz',
  utp: 'utp-latn-sb',
  utr: 'utr-latn-zz',
  utu: 'utu-latn-pg',
  uum: 'uum-grek-ge',
  'uum-cyrl': 'uum-cyrl-ge',
  uur: 'uur-latn-vu',
  uve: 'uve-latn-nc',
  uvh: 'uvh-latn-zz',
  uvl: 'uvl-latn-zz',
  uwa: 'uwa-latn-au',
  uya: 'uya-latn-ng',
  uz: 'uz-latn-uz',
  'uz-af': 'uz-arab-af',
  'uz-arab': 'uz-arab-af',
  'uz-cn': 'uz-cyrl-cn',
  uzs: 'uzs-arab-af',
  vaa: 'vaa-taml-in',
  vae: 'vae-latn-cf',
  vaf: 'vaf-arab-ir',
  vag: 'vag-latn-zz',
  vah: 'vah-deva-in',
  vai: 'vai-vaii-lr',
  vaj: 'vaj-latn-na',
  val: 'val-latn-pg',
  vam: 'vam-latn-pg',
  van: 'van-latn-zz',
  vao: 'vao-latn-vu',
  vap: 'vap-latn-in',
  var: 'var-latn-mx',
  vas: 'vas-deva-in',
  'vas-gujr': 'vas-gujr-in',
  vau: 'vau-latn-cd',
  vav: 'vav-deva-in',
  'vav-gujr': 'vav-gujr-in',
  vay: 'vay-deva-np',
  vbb: 'vbb-latn-id',
  vbk: 'vbk-latn-ph',
  ve: 've-latn-za',
  vec: 'vec-latn-it',
  vem: 'vem-latn-ng',
  veo: 'veo-latn-us',
  vep: 'vep-latn-ru',
  ver: 'ver-latn-ng',
  vgr: 'vgr-arab-pk',
  vi: 'vi-latn-vn',
  vic: 'vic-latn-sx',
  vid: 'vid-latn-tz',
  vif: 'vif-latn-cg',
  vig: 'vig-latn-bf',
  vil: 'vil-latn-ar',
  vin: 'vin-latn-tz',
  vit: 'vit-latn-ng',
  viv: 'viv-latn-zz',
  vka: 'vka-latn-au',
  vkj: 'vkj-latn-td',
  vkk: 'vkk-latn-id',
  vkl: 'vkl-latn-id',
  vkm: 'vkm-latn-br',
  vkn: 'vkn-latn-ng',
  vko: 'vko-latn-id',
  vkp: 'vkp-latn-in',
  'vkp-deva': 'vkp-deva-in',
  vkt: 'vkt-latn-id',
  vku: 'vku-latn-au',
  vkz: 'vkz-latn-ng',
  vlp: 'vlp-latn-vu',
  vls: 'vls-latn-be',
  vma: 'vma-latn-au',
  vmb: 'vmb-latn-au',
  vmc: 'vmc-latn-mx',
  vmd: 'vmd-knda-in',
  vme: 'vme-latn-id',
  vmf: 'vmf-latn-de',
  vmg: 'vmg-latn-pg',
  vmh: 'vmh-arab-ir',
  vmi: 'vmi-latn-au',
  vmj: 'vmj-latn-mx',
  vmk: 'vmk-latn-mz',
  vml: 'vml-latn-au',
  vmm: 'vmm-latn-mx',
  vmp: 'vmp-latn-mx',
  vmq: 'vmq-latn-mx',
  vmr: 'vmr-latn-mz',
  vms: 'vms-latn-id',
  vmu: 'vmu-latn-au',
  vmw: 'vmw-latn-mz',
  vmx: 'vmx-latn-mx',
  vmy: 'vmy-latn-mx',
  vmz: 'vmz-latn-mx',
  vnk: 'vnk-latn-sb',
  vnm: 'vnm-latn-vu',
  vnp: 'vnp-latn-vu',
  vo: 'vo-latn-001',
  vor: 'vor-latn-ng',
  vot: 'vot-latn-ru',
  vra: 'vra-latn-vu',
  vro: 'vro-latn-ee',
  vrs: 'vrs-latn-sb',
  vrt: 'vrt-latn-vu',
  vto: 'vto-latn-id',
  vum: 'vum-latn-ga',
  vun: 'vun-latn-tz',
  vut: 'vut-latn-zz',
  vwa: 'vwa-latn-cn',
  'vwa-mymr': 'vwa-mymr-cn',
  wa: 'wa-latn-be',
  waa: 'waa-latn-us',
  wab: 'wab-latn-pg',
  wac: 'wac-latn-us',
  wad: 'wad-latn-id',
  wae: 'wae-latn-ch',
  waf: 'waf-latn-br',
  wag: 'wag-latn-pg',
  wah: 'wah-latn-id',
  wai: 'wai-latn-id',
  waj: 'waj-latn-zz',
  wal: 'wal-ethi-et',
  wam: 'wam-latn-us',
  wan: 'wan-latn-zz',
  wap: 'wap-latn-gy',
  waq: 'waq-latn-au',
  war: 'war-latn-ph',
  was: 'was-latn-us',
  wat: 'wat-latn-pg',
  wau: 'wau-latn-br',
  wav: 'wav-latn-ng',
  waw: 'waw-latn-br',
  wax: 'wax-latn-pg',
  way: 'way-latn-sr',
  waz: 'waz-latn-pg',
  wba: 'wba-latn-ve',
  wbb: 'wbb-latn-id',
  wbe: 'wbe-latn-id',
  wbf: 'wbf-latn-bf',
  wbh: 'wbh-latn-tz',
  wbi: 'wbi-latn-tz',
  wbj: 'wbj-latn-tz',
  wbk: 'wbk-arab-af',
  wbl: 'wbl-latn-pk',
  'wbl-arab': 'wbl-arab-af',
  'wbl-cyrl': 'wbl-cyrl-tj',
  wbm: 'wbm-latn-cn',
  wbp: 'wbp-latn-au',
  wbq: 'wbq-telu-in',
  wbr: 'wbr-deva-in',
  wbt: 'wbt-latn-au',
  wbv: 'wbv-latn-au',
  wbw: 'wbw-latn-id',
  wca: 'wca-latn-br',
  wci: 'wci-latn-zz',
  wdd: 'wdd-latn-ga',
  wdg: 'wdg-latn-pg',
  wdj: 'wdj-latn-au',
  wdk: 'wdk-latn-au',
  wdt: 'wdt-latn-ca',
  wdu: 'wdu-latn-au',
  wdy: 'wdy-latn-au',
  wec: 'wec-latn-ci',
  wed: 'wed-latn-pg',
  weg: 'weg-latn-au',
  weh: 'weh-latn-cm',
  wei: 'wei-latn-pg',
  wem: 'wem-latn-bj',
  weo: 'weo-latn-id',
  wep: 'wep-latn-de',
  wer: 'wer-latn-zz',
  wes: 'wes-latn-cm',
  wet: 'wet-latn-id',
  weu: 'weu-latn-mm',
  wew: 'wew-latn-id',
  wfg: 'wfg-latn-id',
  wga: 'wga-latn-au',
  wgb: 'wgb-latn-pg',
  wgg: 'wgg-latn-au',
  wgi: 'wgi-latn-zz',
  wgo: 'wgo-latn-id',
  wgu: 'wgu-latn-au',
  wgy: 'wgy-latn-au',
  wha: 'wha-latn-id',
  whg: 'whg-latn-zz',
  whk: 'whk-latn-id',
  whu: 'whu-latn-id',
  wib: 'wib-latn-zz',
  wic: 'wic-latn-us',
  wie: 'wie-latn-au',
  wif: 'wif-latn-au',
  wig: 'wig-latn-au',
  wih: 'wih-latn-au',
  wii: 'wii-latn-pg',
  wij: 'wij-latn-au',
  wik: 'wik-latn-au',
  wil: 'wil-latn-au',
  wim: 'wim-latn-au',
  win: 'win-latn-us',
  wir: 'wir-latn-br',
  wiu: 'wiu-latn-zz',
  wiv: 'wiv-latn-zz',
  wiy: 'wiy-latn-us',
  wja: 'wja-latn-zz',
  wji: 'wji-latn-zz',
  wka: 'wka-latn-tz',
  wkd: 'wkd-latn-id',
  wkr: 'wkr-latn-au',
  wkw: 'wkw-latn-au',
  wky: 'wky-latn-au',
  wla: 'wla-latn-pg',
  wlg: 'wlg-latn-au',
  wlh: 'wlh-latn-tl',
  wli: 'wli-latn-id',
  wlm: 'wlm-latn-gb',
  wlo: 'wlo-arab-id',
  wlr: 'wlr-latn-vu',
  wls: 'wls-latn-wf',
  wlu: 'wlu-latn-au',
  wlv: 'wlv-latn-ar',
  wlw: 'wlw-latn-id',
  wlx: 'wlx-latn-gh',
  wma: 'wma-latn-ng',
  wmb: 'wmb-latn-au',
  wmc: 'wmc-latn-pg',
  wmd: 'wmd-latn-br',
  wme: 'wme-deva-np',
  wmh: 'wmh-latn-tl',
  wmi: 'wmi-latn-au',
  wmm: 'wmm-latn-id',
  wmn: 'wmn-latn-nc',
  wmo: 'wmo-latn-zz',
  wms: 'wms-latn-id',
  wmt: 'wmt-latn-au',
  wmw: 'wmw-latn-mz',
  'wmw-arab': 'wmw-arab-mz',
  wmx: 'wmx-latn-pg',
  wnb: 'wnb-latn-pg',
  wnc: 'wnc-latn-zz',
  wnd: 'wnd-latn-au',
  wne: 'wne-arab-pk',
  wng: 'wng-latn-id',
  wni: 'wni-arab-km',
  wnk: 'wnk-latn-id',
  wnm: 'wnm-latn-au',
  wnn: 'wnn-latn-au',
  wno: 'wno-latn-id',
  wnp: 'wnp-latn-pg',
  wnu: 'wnu-latn-zz',
  wnw: 'wnw-latn-us',
  wny: 'wny-latn-au',
  wo: 'wo-latn-sn',
  woa: 'woa-latn-au',
  wob: 'wob-latn-zz',
  woc: 'woc-latn-pg',
  wod: 'wod-latn-id',
  woe: 'woe-latn-fm',
  wof: 'wof-latn-gm',
  'wof-arab': 'wof-arab-gm',
  wog: 'wog-latn-pg',
  woi: 'woi-latn-id',
  wok: 'wok-latn-cm',
  wom: 'wom-latn-ng',
  won: 'won-latn-cd',
  woo: 'woo-latn-id',
  wor: 'wor-latn-id',
  wos: 'wos-latn-zz',
  wow: 'wow-latn-id',
  wpc: 'wpc-latn-ve',
  wrb: 'wrb-latn-au',
  wrg: 'wrg-latn-au',
  wrh: 'wrh-latn-au',
  wri: 'wri-latn-au',
  wrk: 'wrk-latn-au',
  wrl: 'wrl-latn-au',
  wrm: 'wrm-latn-au',
  wro: 'wro-latn-au',
  wrp: 'wrp-latn-id',
  wrr: 'wrr-latn-au',
  wrs: 'wrs-latn-zz',
  wru: 'wru-latn-id',
  wrv: 'wrv-latn-pg',
  wrw: 'wrw-latn-au',
  wrx: 'wrx-latn-id',
  wrz: 'wrz-latn-au',
  wsa: 'wsa-latn-id',
  wsg: 'wsg-gong-in',
  wsi: 'wsi-latn-vu',
  wsk: 'wsk-latn-zz',
  wsr: 'wsr-latn-pg',
  wss: 'wss-latn-gh',
  wsu: 'wsu-latn-br',
  wsv: 'wsv-arab-af',
  wtf: 'wtf-latn-pg',
  wth: 'wth-latn-au',
  wti: 'wti-latn-et',
  wtk: 'wtk-latn-pg',
  wtm: 'wtm-deva-in',
  wtw: 'wtw-latn-id',
  'wtw-bugi': 'wtw-bugi-id',
  wua: 'wua-latn-au',
  wub: 'wub-latn-au',
  wud: 'wud-latn-tg',
  wul: 'wul-latn-id',
  wum: 'wum-latn-ga',
  wun: 'wun-latn-tz',
  wur: 'wur-latn-au',
  wut: 'wut-latn-pg',
  wuu: 'wuu-hans-cn',
  wuv: 'wuv-latn-zz',
  wux: 'wux-latn-au',
  wuy: 'wuy-latn-id',
  wwa: 'wwa-latn-zz',
  wwb: 'wwb-latn-au',
  wwo: 'wwo-latn-vu',
  wwr: 'wwr-latn-au',
  www: 'www-latn-cm',
  wxw: 'wxw-latn-au',
  wyb: 'wyb-latn-au',
  wyi: 'wyi-latn-au',
  wym: 'wym-latn-pl',
  wyn: 'wyn-latn-us',
  wyr: 'wyr-latn-br',
  wyy: 'wyy-latn-fj',
  xaa: 'xaa-latn-es',
  xab: 'xab-latn-ng',
  xai: 'xai-latn-br',
  xaj: 'xaj-latn-br',
  xak: 'xak-latn-ve',
  xal: 'xal-cyrl-ru',
  xam: 'xam-latn-za',
  xan: 'xan-ethi-et',
  xao: 'xao-latn-vn',
  xar: 'xar-latn-pg',
  xas: 'xas-cyrl-ru',
  xat: 'xat-latn-br',
  xau: 'xau-latn-id',
  xav: 'xav-latn-br',
  xaw: 'xaw-latn-us',
  xay: 'xay-latn-id',
  xbb: 'xbb-latn-au',
  xbd: 'xbd-latn-au',
  xbe: 'xbe-latn-au',
  xbg: 'xbg-latn-au',
  xbi: 'xbi-latn-zz',
  xbj: 'xbj-latn-au',
  xbm: 'xbm-latn-fr',
  xbn: 'xbn-latn-my',
  xbp: 'xbp-latn-au',
  xbr: 'xbr-latn-id',
  xbw: 'xbw-latn-br',
  xby: 'xby-latn-au',
  xch: 'xch-latn-us',
  xco: 'xco-chrs-uz',
  xcr: 'xcr-cari-tr',
  xda: 'xda-latn-au',
  xdk: 'xdk-latn-au',
  xdo: 'xdo-latn-ao',
  xdq: 'xdq-cyrl-ru',
  xdy: 'xdy-latn-id',
  xed: 'xed-latn-cm',
  xeg: 'xeg-latn-za',
  xem: 'xem-latn-id',
  xer: 'xer-latn-br',
  xes: 'xes-latn-zz',
  xet: 'xet-latn-br',
  xeu: 'xeu-latn-pg',
  xgb: 'xgb-latn-ci',
  xgd: 'xgd-latn-au',
  xgg: 'xgg-latn-au',
  xgi: 'xgi-latn-au',
  xgm: 'xgm-latn-au',
  xgu: 'xgu-latn-au',
  xgw: 'xgw-latn-au',
  xh: 'xh-latn-za',
  xhe: 'xhe-arab-pk',
  xhm: 'xhm-khmr-kh',
  xhv: 'xhv-latn-vn',
  xii: 'xii-latn-za',
  xin: 'xin-latn-gt',
  xir: 'xir-latn-br',
  xis: 'xis-orya-in',
  xiy: 'xiy-latn-br',
  xjb: 'xjb-latn-au',
  xjt: 'xjt-latn-au',
  xka: 'xka-arab-pk',
  xkb: 'xkb-latn-bj',
  xkc: 'xkc-arab-ir',
  xkd: 'xkd-latn-id',
  xke: 'xke-latn-id',
  xkg: 'xkg-latn-ml',
  xkj: 'xkj-arab-ir',
  xkl: 'xkl-latn-id',
  xkn: 'xkn-latn-id',
  xkp: 'xkp-arab-ir',
  xkq: 'xkq-latn-id',
  xkr: 'xkr-latn-br',
  xks: 'xks-latn-id',
  xkt: 'xkt-latn-gh',
  xku: 'xku-latn-cg',
  xkv: 'xkv-latn-bw',
  xkw: 'xkw-latn-id',
  xkx: 'xkx-latn-pg',
  xky: 'xky-latn-my',
  xkz: 'xkz-latn-bt',
  xla: 'xla-latn-zz',
  xlc: 'xlc-lyci-tr',
  xld: 'xld-lydi-tr',
  xly: 'xly-elym-ir',
  xma: 'xma-latn-so',
  xmb: 'xmb-latn-cm',
  xmc: 'xmc-latn-mz',
  xmd: 'xmd-latn-cm',
  xmf: 'xmf-geor-ge',
  xmg: 'xmg-latn-cm',
  xmh: 'xmh-latn-au',
  xmj: 'xmj-latn-cm',
  xmm: 'xmm-latn-id',
  xmn: 'xmn-mani-cn',
  xmo: 'xmo-latn-br',
  xmp: 'xmp-latn-au',
  xmq: 'xmq-latn-au',
  xmr: 'xmr-merc-sd',
  xmt: 'xmt-latn-id',
  xmu: 'xmu-latn-au',
  xmv: 'xmv-latn-mg',
  xmw: 'xmw-latn-mg',
  xmx: 'xmx-latn-id',
  xmy: 'xmy-latn-au',
  xmz: 'xmz-latn-id',
  xna: 'xna-narb-sa',
  xnb: 'xnb-latn-tw',
  xni: 'xni-latn-au',
  xnj: 'xnj-latn-tz',
  xnk: 'xnk-latn-au',
  xnm: 'xnm-latn-au',
  xnn: 'xnn-latn-ph',
  xnq: 'xnq-latn-mz',
  xnr: 'xnr-deva-in',
  xnt: 'xnt-latn-us',
  xnu: 'xnu-latn-au',
  xny: 'xny-latn-au',
  xnz: 'xnz-latn-eg',
  'xnz-arab': 'xnz-arab-eg',
  xoc: 'xoc-latn-ng',
  xod: 'xod-latn-id',
  xog: 'xog-latn-ug',
  xoi: 'xoi-latn-pg',
  xok: 'xok-latn-br',
  xom: 'xom-latn-sd',
  'xom-ethi': 'xom-ethi-et',
  xon: 'xon-latn-zz',
  xoo: 'xoo-latn-br',
  xop: 'xop-latn-pg',
  xor: 'xor-latn-br',
  xow: 'xow-latn-pg',
  xpa: 'xpa-latn-au',
  xpb: 'xpb-latn-au',
  xpd: 'xpd-latn-au',
  xpf: 'xpf-latn-au',
  xpg: 'xpg-grek-tr',
  xph: 'xph-latn-au',
  xpi: 'xpi-ogam-gb',
  xpj: 'xpj-latn-au',
  xpk: 'xpk-latn-br',
  xpl: 'xpl-latn-au',
  xpm: 'xpm-cyrl-ru',
  xpn: 'xpn-latn-br',
  xpo: 'xpo-latn-mx',
  xpq: 'xpq-latn-us',
  xpr: 'xpr-prti-ir',
  xpt: 'xpt-latn-au',
  xpv: 'xpv-latn-au',
  xpw: 'xpw-latn-au',
  xpx: 'xpx-latn-au',
  xpz: 'xpz-latn-au',
  xra: 'xra-latn-br',
  xrb: 'xrb-latn-zz',
  xrd: 'xrd-latn-au',
  xre: 'xre-latn-br',
  xrg: 'xrg-latn-au',
  xri: 'xri-latn-br',
  xrm: 'xrm-cyrl-ru',
  xrn: 'xrn-cyrl-ru',
  xrr: 'xrr-latn-it',
  xru: 'xru-latn-au',
  xrw: 'xrw-latn-pg',
  xsa: 'xsa-sarb-ye',
  xsb: 'xsb-latn-ph',
  xse: 'xse-latn-id',
  xsh: 'xsh-latn-ng',
  xsi: 'xsi-latn-zz',
  xsm: 'xsm-latn-zz',
  xsn: 'xsn-latn-ng',
  xsp: 'xsp-latn-pg',
  xsq: 'xsq-latn-mz',
  xsr: 'xsr-deva-np',
  xss: 'xss-cyrl-ru',
  xsu: 'xsu-latn-ve',
  xsy: 'xsy-latn-tw',
  xta: 'xta-latn-mx',
  xtb: 'xtb-latn-mx',
  xtc: 'xtc-latn-sd',
  xtd: 'xtd-latn-mx',
  xte: 'xte-latn-id',
  xth: 'xth-latn-au',
  xti: 'xti-latn-mx',
  xtj: 'xtj-latn-mx',
  xtl: 'xtl-latn-mx',
  xtm: 'xtm-latn-mx',
  xtn: 'xtn-latn-mx',
  xtp: 'xtp-latn-mx',
  xts: 'xts-latn-mx',
  xtt: 'xtt-latn-mx',
  xtu: 'xtu-latn-mx',
  xtv: 'xtv-latn-au',
  xtw: 'xtw-latn-br',
  xty: 'xty-latn-mx',
  xub: 'xub-taml-in',
  'xub-knda': 'xub-knda-in',
  'xub-mlym': 'xub-mlym-in',
  xud: 'xud-latn-au',
  xuj: 'xuj-taml-in',
  xul: 'xul-latn-au',
  xum: 'xum-latn-it',
  'xum-ital': 'xum-ital-it',
  xun: 'xun-latn-au',
  xuo: 'xuo-latn-td',
  xut: 'xut-latn-au',
  xuu: 'xuu-latn-na',
  xve: 'xve-ital-it',
  xvi: 'xvi-arab-af',
  xvn: 'xvn-latn-es',
  xvo: 'xvo-latn-it',
  xvs: 'xvs-latn-it',
  xwa: 'xwa-latn-br',
  xwd: 'xwd-latn-au',
  xwe: 'xwe-latn-zz',
  xwj: 'xwj-latn-au',
  xwk: 'xwk-latn-au',
  xwl: 'xwl-latn-bj',
  xwo: 'xwo-cyrl-ru',
  xwr: 'xwr-latn-id',
  xwt: 'xwt-latn-au',
  xww: 'xww-latn-au',
  xxb: 'xxb-latn-gh',
  xxk: 'xxk-latn-id',
  xxm: 'xxm-latn-au',
  xxr: 'xxr-latn-br',
  xxt: 'xxt-latn-id',
  xya: 'xya-latn-au',
  xyb: 'xyb-latn-au',
  xyj: 'xyj-latn-au',
  xyk: 'xyk-latn-au',
  xyl: 'xyl-latn-br',
  xyt: 'xyt-latn-au',
  xyy: 'xyy-latn-au',
  xzh: 'xzh-marc-cn',
  xzp: 'xzp-latn-mx',
  yaa: 'yaa-latn-pe',
  yab: 'yab-latn-br',
  yac: 'yac-latn-id',
  yad: 'yad-latn-pe',
  yae: 'yae-latn-ve',
  yaf: 'yaf-latn-cd',
  yag: 'yag-latn-cl',
  yai: 'yai-cyrl-tj',
  yaj: 'yaj-latn-cf',
  yak: 'yak-latn-us',
  yal: 'yal-latn-gn',
  'yal-arab': 'yal-arab-gn',
  yam: 'yam-latn-zz',
  yan: 'yan-latn-ni',
  yao: 'yao-latn-mz',
  yap: 'yap-latn-fm',
  yaq: 'yaq-latn-mx',
  yar: 'yar-latn-ve',
  yas: 'yas-latn-zz',
  yat: 'yat-latn-zz',
  yau: 'yau-latn-ve',
  yav: 'yav-latn-cm',
  yaw: 'yaw-latn-br',
  yax: 'yax-latn-ao',
  yay: 'yay-latn-zz',
  yaz: 'yaz-latn-zz',
  yba: 'yba-latn-zz',
  ybb: 'ybb-latn-cm',
  ybe: 'ybe-latn-cn',
  'ybe-ougr': 'ybe-ougr-cn',
  ybh: 'ybh-deva-np',
  ybi: 'ybi-deva-np',
  ybj: 'ybj-latn-ng',
  ybl: 'ybl-latn-ng',
  ybm: 'ybm-latn-pg',
  ybn: 'ybn-latn-br',
  ybo: 'ybo-latn-pg',
  ybx: 'ybx-latn-pg',
  yby: 'yby-latn-zz',
  ycl: 'ycl-latn-cn',
  ycn: 'ycn-latn-co',
  yda: 'yda-latn-au',
  yde: 'yde-latn-pg',
  ydg: 'ydg-arab-pk',
  ydk: 'ydk-latn-pg',
  yea: 'yea-mlym-in',
  'yea-knda': 'yea-knda-in',
  yec: 'yec-latn-de',
  yee: 'yee-latn-pg',
  yei: 'yei-latn-cm',
  yej: 'yej-grek-il',
  yel: 'yel-latn-cd',
  yer: 'yer-latn-zz',
  yes: 'yes-latn-ng',
  yet: 'yet-latn-id',
  yeu: 'yeu-telu-in',
  yev: 'yev-latn-pg',
  yey: 'yey-latn-bw',
  yga: 'yga-latn-au',
  ygi: 'ygi-latn-au',
  ygl: 'ygl-latn-pg',
  ygm: 'ygm-latn-pg',
  ygp: 'ygp-plrd-cn',
  ygr: 'ygr-latn-zz',
  ygu: 'ygu-latn-au',
  ygw: 'ygw-latn-zz',
  yhd: 'yhd-hebr-il',
  yi: 'yi-hebr-001',
  yia: 'yia-latn-au',
  yig: 'yig-yiii-cn',
  yih: 'yih-hebr-de',
  yii: 'yii-latn-au',
  yij: 'yij-latn-au',
  yil: 'yil-latn-au',
  yim: 'yim-latn-in',
  yir: 'yir-latn-id',
  yis: 'yis-latn-pg',
  yiv: 'yiv-yiii-cn',
  yka: 'yka-latn-ph',
  'yka-arab': 'yka-arab-ph',
  ykg: 'ykg-cyrl-ru',
  yki: 'yki-latn-id',
  ykk: 'ykk-latn-pg',
  ykm: 'ykm-latn-pg',
  yko: 'yko-latn-zz',
  ykr: 'ykr-latn-pg',
  yky: 'yky-latn-cf',
  yla: 'yla-latn-pg',
  ylb: 'ylb-latn-pg',
  yle: 'yle-latn-zz',
  ylg: 'ylg-latn-zz',
  yli: 'yli-latn-id',
  yll: 'yll-latn-zz',
  ylr: 'ylr-latn-au',
  ylu: 'ylu-latn-pg',
  yly: 'yly-latn-nc',
  ymb: 'ymb-latn-pg',
  yme: 'yme-latn-pe',
  ymg: 'ymg-latn-cd',
  ymk: 'ymk-latn-mz',
  'ymk-arab': 'ymk-arab-mz',
  yml: 'yml-latn-zz',
  ymm: 'ymm-latn-so',
  ymn: 'ymn-latn-id',
  ymo: 'ymo-latn-pg',
  ymp: 'ymp-latn-pg',
  yna: 'yna-plrd-cn',
  ynd: 'ynd-latn-au',
  yng: 'yng-latn-cd',
  ynk: 'ynk-cyrl-ru',
  ynl: 'ynl-latn-pg',
  ynq: 'ynq-latn-ng',
  yns: 'yns-latn-cd',
  ynu: 'ynu-latn-co',
  yo: 'yo-latn-ng',
  yob: 'yob-latn-pg',
  yog: 'yog-latn-ph',
  yoi: 'yoi-jpan-jp',
  yok: 'yok-latn-us',
  yol: 'yol-latn-gb',
  yom: 'yom-latn-cd',
  yon: 'yon-latn-zz',
  yot: 'yot-latn-ng',
  yoy: 'yoy-thai-th',
  yra: 'yra-latn-pg',
  yrb: 'yrb-latn-zz',
  yre: 'yre-latn-zz',
  yrk: 'yrk-cyrl-ru',
  yrl: 'yrl-latn-br',
  yrm: 'yrm-latn-au',
  yro: 'yro-latn-br',
  yrs: 'yrs-latn-id',
  yrw: 'yrw-latn-pg',
  yry: 'yry-latn-au',
  ysd: 'ysd-yiii-cn',
  ysn: 'ysn-yiii-cn',
  ysp: 'ysp-yiii-cn',
  ysr: 'ysr-cyrl-ru',
  yss: 'yss-latn-zz',
  ysy: 'ysy-plrd-cn',
  ytw: 'ytw-latn-pg',
  yty: 'yty-latn-au',
  yua: 'yua-latn-mx',
  yub: 'yub-latn-au',
  yuc: 'yuc-latn-us',
  yud: 'yud-hebr-il',
  yue: 'yue-hant-hk',
  'yue-cn': 'yue-hans-cn',
  'yue-hans': 'yue-hans-cn',
  yuf: 'yuf-latn-us',
  yug: 'yug-cyrl-ru',
  yui: 'yui-latn-co',
  yuj: 'yuj-latn-zz',
  yul: 'yul-latn-cf',
  yum: 'yum-latn-us',
  yun: 'yun-latn-ng',
  yup: 'yup-latn-co',
  yuq: 'yuq-latn-bo',
  yur: 'yur-latn-us',
  yut: 'yut-latn-zz',
  yuw: 'yuw-latn-zz',
  yux: 'yux-cyrl-ru',
  yuz: 'yuz-latn-bo',
  yva: 'yva-latn-id',
  yvt: 'yvt-latn-ve',
  ywa: 'ywa-latn-pg',
  ywg: 'ywg-latn-au',
  ywn: 'ywn-latn-br',
  ywq: 'ywq-plrd-cn',
  'ywq-yiii': 'ywq-yiii-cn',
  ywr: 'ywr-latn-au',
  ywu: 'ywu-plrd-cn',
  'ywu-yiii': 'ywu-yiii-cn',
  yww: 'yww-latn-au',
  yxa: 'yxa-latn-au',
  yxg: 'yxg-latn-au',
  yxl: 'yxl-latn-au',
  yxm: 'yxm-latn-au',
  yxu: 'yxu-latn-au',
  yxy: 'yxy-latn-au',
  yyr: 'yyr-latn-au',
  yyu: 'yyu-latn-pg',
  za: 'za-latn-cn',
  zaa: 'zaa-latn-mx',
  zab: 'zab-latn-mx',
  zac: 'zac-latn-mx',
  zad: 'zad-latn-mx',
  zae: 'zae-latn-mx',
  zaf: 'zaf-latn-mx',
  zag: 'zag-latn-sd',
  zah: 'zah-latn-ng',
  zaj: 'zaj-latn-tz',
  zak: 'zak-latn-tz',
  zam: 'zam-latn-mx',
  zao: 'zao-latn-mx',
  zap: 'zap-latn-mx',
  zaq: 'zaq-latn-mx',
  zar: 'zar-latn-mx',
  zas: 'zas-latn-mx',
  zat: 'zat-latn-mx',
  zau: 'zau-tibt-in',
  'zau-arab': 'zau-arab-in',
  zav: 'zav-latn-mx',
  zaw: 'zaw-latn-mx',
  zax: 'zax-latn-mx',
  zay: 'zay-latn-et',
  'zay-ethi': 'zay-ethi-et',
  zaz: 'zaz-latn-ng',
  zba: 'zba-arab-001',
  zbc: 'zbc-latn-my',
  zbe: 'zbe-latn-my',
  zbt: 'zbt-latn-id',
  zbu: 'zbu-latn-ng',
  zbw: 'zbw-latn-my',
  zca: 'zca-latn-mx',
  zch: 'zch-hani-cn',
  zdj: 'zdj-arab-km',
  zea: 'zea-latn-nl',
  zeg: 'zeg-latn-pg',
  zeh: 'zeh-hani-cn',
  zen: 'zen-tfng-mr',
  'zen-arab': 'zen-arab-mr',
  zga: 'zga-latn-tz',
  zgb: 'zgb-hani-cn',
  zgh: 'zgh-tfng-ma',
  zgm: 'zgm-hani-cn',
  zgn: 'zgn-hani-cn',
  zgr: 'zgr-latn-pg',
  zh: 'zh-hans-cn',
  'zh-au': 'zh-hant-au',
  'zh-bn': 'zh-hant-bn',
  'zh-bopo': 'zh-bopo-tw',
  'zh-gb': 'zh-hant-gb',
  'zh-gf': 'zh-hant-gf',
  'zh-hanb': 'zh-hanb-tw',
  'zh-hant': 'zh-hant-tw',
  'zh-hk': 'zh-hant-hk',
  'zh-id': 'zh-hant-id',
  'zh-mo': 'zh-hant-mo',
  'zh-pa': 'zh-hant-pa',
  'zh-pf': 'zh-hant-pf',
  'zh-ph': 'zh-hant-ph',
  'zh-sr': 'zh-hant-sr',
  'zh-th': 'zh-hant-th',
  'zh-tw': 'zh-hant-tw',
  'zh-us': 'zh-hant-us',
  'zh-vn': 'zh-hant-vn',
  zhd: 'zhd-hani-cn',
  'zhd-latn': 'zhd-latn-vn',
  zhi: 'zhi-latn-ng',
  zhn: 'zhn-latn-cn',
  'zhn-hani': 'zhn-hani-cn',
  zhw: 'zhw-latn-cm',
  zhx: 'zhx-nshu-cn',
  zia: 'zia-latn-zz',
  zik: 'zik-latn-pg',
  zil: 'zil-latn-gn',
  zim: 'zim-latn-td',
  zin: 'zin-latn-tz',
  ziw: 'ziw-latn-tz',
  ziz: 'ziz-latn-ng',
  zka: 'zka-latn-id',
  zkb: 'zkb-cyrl-ru',
  zkd: 'zkd-latn-mm',
  zko: 'zko-cyrl-ru',
  zkp: 'zkp-latn-br',
  zkt: 'zkt-kits-cn',
  zku: 'zku-latn-au',
  zkz: 'zkz-cyrl-ru',
  zla: 'zla-latn-cd',
  zlj: 'zlj-hani-cn',
  'zlj-latn': 'zlj-latn-cn',
  zlm: 'zlm-latn-tg',
  zln: 'zln-hani-cn',
  zlq: 'zlq-hani-cn',
  zma: 'zma-latn-au',
  zmb: 'zmb-latn-cd',
  zmc: 'zmc-latn-au',
  zmd: 'zmd-latn-au',
  zme: 'zme-latn-au',
  zmf: 'zmf-latn-cd',
  zmg: 'zmg-latn-au',
  zmh: 'zmh-latn-pg',
  zmi: 'zmi-latn-my',
  zmj: 'zmj-latn-au',
  zmk: 'zmk-latn-au',
  zml: 'zml-latn-au',
  zmm: 'zmm-latn-au',
  zmn: 'zmn-latn-ga',
  zmo: 'zmo-latn-sd',
  zmp: 'zmp-latn-cd',
  zmq: 'zmq-latn-cd',
  zmr: 'zmr-latn-au',
  zms: 'zms-latn-cd',
  zmt: 'zmt-latn-au',
  zmu: 'zmu-latn-au',
  zmv: 'zmv-latn-au',
  zmw: 'zmw-latn-cd',
  zmx: 'zmx-latn-cg',
  zmy: 'zmy-latn-au',
  zmz: 'zmz-latn-cd',
  zna: 'zna-latn-td',
  zne: 'zne-latn-zz',
  zng: 'zng-latn-vn',
  znk: 'znk-latn-au',
  zns: 'zns-latn-ng',
  zoc: 'zoc-latn-mx',
  zoh: 'zoh-latn-mx',
  zom: 'zom-latn-in',
  zoo: 'zoo-latn-mx',
  zoq: 'zoq-latn-mx',
  zor: 'zor-latn-mx',
  zos: 'zos-latn-mx',
  zpa: 'zpa-latn-mx',
  zpb: 'zpb-latn-mx',
  zpc: 'zpc-latn-mx',
  zpd: 'zpd-latn-mx',
  zpe: 'zpe-latn-mx',
  zpf: 'zpf-latn-mx',
  zpg: 'zpg-latn-mx',
  zph: 'zph-latn-mx',
  zpi: 'zpi-latn-mx',
  zpj: 'zpj-latn-mx',
  zpk: 'zpk-latn-mx',
  zpl: 'zpl-latn-mx',
  zpm: 'zpm-latn-mx',
  zpn: 'zpn-latn-mx',
  zpo: 'zpo-latn-mx',
  zpp: 'zpp-latn-mx',
  zpq: 'zpq-latn-mx',
  zpr: 'zpr-latn-mx',
  zps: 'zps-latn-mx',
  zpt: 'zpt-latn-mx',
  zpu: 'zpu-latn-mx',
  zpv: 'zpv-latn-mx',
  zpw: 'zpw-latn-mx',
  zpx: 'zpx-latn-mx',
  zpy: 'zpy-latn-mx',
  zpz: 'zpz-latn-mx',
  zqe: 'zqe-hani-cn',
  'zqe-latn': 'zqe-latn-cn',
  zrn: 'zrn-latn-td',
  zro: 'zro-latn-ec',
  zrp: 'zrp-hebr-fr',
  zrs: 'zrs-latn-id',
  zsa: 'zsa-latn-pg',
  zsr: 'zsr-latn-mx',
  zsu: 'zsu-latn-pg',
  zte: 'zte-latn-mx',
  ztg: 'ztg-latn-mx',
  ztl: 'ztl-latn-mx',
  ztm: 'ztm-latn-mx',
  ztn: 'ztn-latn-mx',
  ztp: 'ztp-latn-mx',
  ztq: 'ztq-latn-mx',
  zts: 'zts-latn-mx',
  ztt: 'ztt-latn-mx',
  ztu: 'ztu-latn-mx',
  ztx: 'ztx-latn-mx',
  zty: 'zty-latn-mx',
  zu: 'zu-latn-za',
  zua: 'zua-latn-ng',
  zuh: 'zuh-latn-pg',
  zum: 'zum-arab-om',
  zun: 'zun-latn-us',
  zuy: 'zuy-latn-cm',
  zyg: 'zyg-hani-cn',
  zyj: 'zyj-latn-cn',
  'zyj-hani': 'zyj-hani-cn',
  zyn: 'zyn-hani-cn',
  zyp: 'zyp-latn-mm',
  zza: 'zza-latn-tr',
  zzj: 'zzj-hani-cn'
}
