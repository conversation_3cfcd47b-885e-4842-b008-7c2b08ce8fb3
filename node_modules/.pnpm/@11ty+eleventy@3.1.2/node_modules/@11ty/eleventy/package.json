{"name": "@11ty/eleventy", "version": "3.1.2", "description": "A simpler static site generator.", "publishConfig": {"access": "public", "provenance": true}, "type": "module", "main": "./src/Eleventy.js", "exports": {".": {"import": "./src/Eleventy.js", "require": "./src/EleventyCommonJs.cjs"}, "./UserConfig": {"types": "./src/UserConfig.js"}}, "bin": {"eleventy": "cmd.cjs"}, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "keywords": ["static-site-generator", "static-site", "ssg", "documentation", "website", "j<PERSON><PERSON><PERSON>", "blog", "templates", "generator", "framework", "eleventy", "11ty", "html", "markdown", "liquid", "nunjucks"], "scripts": {"default": "npm run test", "test": "npm run test:node && npm run test:ava", "test:ava": "ava --verbose --timeout 20s", "test:node": "node --test test_node/tests.js", "format": "prettier . --write", "check": "eslint src", "check-types": "tsc", "nano-staged": "nano-staged", "coverage": "npx c8 ava && npx c8 report --reporter=json-summary && cp coverage/coverage-summary.json docs/_data/coverage.json && node cmd.cjs --config=docs/eleventy.coverage.js", "prepare": "simple-git-hooks"}, "author": "<PERSON> <<EMAIL>> (https://zachleat.com/)", "repository": {"type": "git", "url": "git://github.com/11ty/eleventy.git"}, "bugs": "https://github.com/11ty/eleventy/issues", "homepage": "https://www.11ty.dev/", "ava": {"environmentVariables": {}, "failFast": true, "files": ["./test/*.js", "./test/_issues/**/*test.js"], "watchMode": {"ignoreChanges": ["./test/stubs*/**/*", "./test/**/_site/**/*", ".cache"]}}, "nano-staged": {"*.{js,css,md}": ["prettier --write"]}, "simple-git-hooks": {"pre-commit": "npm test && npm run nano-staged", "pre-push": "npm test"}, "devDependencies": {"@11ty/eleventy-img": "^6.0.4", "@11ty/eleventy-plugin-rss": "^2.0.4", "@11ty/eleventy-plugin-syntaxhighlight": "^5.0.1", "@11ty/eleventy-plugin-webc": "^0.12.0-beta.3", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@iarna/toml": "^2.2.5", "@mdx-js/node-loader": "^3.1.0", "@types/node": "^22.15.32", "@vue/server-renderer": "^3.5.17", "@zachleat/noop": "^1.0.6", "ava": "^6.4.0", "c8": "^10.1.3", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "globals": "^16.2.0", "jsx-async-runtime": "^1.0.2", "markdown-it-abbr": "^2.0.0", "markdown-it-emoji": "^3.0.0", "marked": "^15.0.12", "nano-staged": "^0.8.0", "prettier": "^3.5.3", "pretty": "^2.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rimraf": "^6.0.1", "sass": "^1.89.2", "simple-git-hooks": "^2.13.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "vue": "^3.5.17", "zod": "^3.25.67", "zod-validation-error": "^3.5.2"}, "dependencies": {"@11ty/dependency-tree": "^4.0.0", "@11ty/dependency-tree-esm": "^2.0.0", "@11ty/eleventy-dev-server": "^2.0.8", "@11ty/eleventy-plugin-bundle": "^3.0.6", "@11ty/eleventy-utils": "^2.0.7", "@11ty/lodash-custom": "^4.17.21", "@11ty/posthtml-urls": "^1.0.1", "@11ty/recursive-copy": "^4.0.2", "@sindresorhus/slugify": "^2.2.1", "bcp-47-normalize": "^2.3.0", "chokidar": "^3.6.0", "debug": "^4.4.1", "dependency-graph": "^1.0.0", "entities": "^6.0.1", "filesize": "^10.1.6", "gray-matter": "^4.0.3", "iso-639-1": "^3.1.5", "js-yaml": "^4.1.0", "kleur": "^4.1.5", "liquidjs": "^10.21.1", "luxon": "^3.6.1", "markdown-it": "^14.1.0", "minimist": "^1.2.8", "moo": "^0.5.2", "node-retrieve-globals": "^6.0.1", "nunjucks": "^3.2.4", "picomatch": "^4.0.2", "please-upgrade-node": "^3.2.0", "posthtml": "^0.16.6", "posthtml-match-helper": "^2.0.3", "semver": "^7.7.2", "slugify": "^1.6.6", "tinyglobby": "^0.2.14"}, "overrides": {"gray-matter": {"js-yaml": "$js-yaml"}}}