{"name": "bcp-47", "version": "2.1.0", "description": "Parse and stringify BCP 47 language tags", "license": "MIT", "keywords": ["bcp", "47", "bcp47", "bcp-47", "language", "tag", "parse"], "repository": "wooorm/bcp-47", "bugs": "https://github.com/wooorm/bcp-47/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js", "lib/"], "dependencies": {"is-alphabetical": "^2.0.0", "is-alphanumerical": "^2.0.0", "is-decimal": "^2.0.0"}, "devDependencies": {"@types/tape": "^4.0.0", "c8": "^7.0.0", "is-hidden": "^2.0.0", "prettier": "^2.0.0", "remark-cli": "^10.0.0", "remark-preset-wooorm": "^9.0.0", "rimraf": "^3.0.0", "tape": "^5.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.46.0"}, "scripts": {"prepublishOnly": "npm run build && npm run format", "build": "rimraf \"{lib,test}/**/*.d.ts\" \"*.d.ts\" && tsc && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --check-coverage --branches 100 --functions 100 --lines 100 --statements 100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"complexity": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}