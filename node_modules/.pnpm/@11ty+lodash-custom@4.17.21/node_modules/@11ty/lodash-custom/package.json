{"name": "@11ty/lodash-custom", "version": "4.17.21", "description": "A custom, focused build of lodash exclusively for use internally in Eleventy.", "main": "lodash.custom.js", "scripts": {"build": "npx lodash exports=node include=get,set,chunk && rm lodash.custom.min.js"}, "publishConfig": {"access": "public"}, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/11ty"}, "author": {"name": "<PERSON>", "email": "zach<PERSON><PERSON><EMAIL>", "url": "https://zachleat.com/"}, "repository": {"type": "git", "url": "git://github.com/11ty/lodash-custom.git"}, "bugs": "https://github.com/11ty/lodash-custom/issues", "homepage": "https://github.com/11ty/lodash-custom/", "devDependencies": {"ava": "^5.2.0", "lodash-cli": "^4.17.5"}, "overrides": {"lodash": "4.17.21"}}