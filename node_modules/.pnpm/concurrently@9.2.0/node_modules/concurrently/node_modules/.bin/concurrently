#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/dist/bin/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/dist/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/dist/bin/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/dist/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules/concurrently/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/concurrently@9.2.0/node_modules:/Users/<USER>/Sites/sanrado-com/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/bin/concurrently.js" "$@"
else
  exec node  "$basedir/../../dist/bin/concurrently.js" "$@"
fi
