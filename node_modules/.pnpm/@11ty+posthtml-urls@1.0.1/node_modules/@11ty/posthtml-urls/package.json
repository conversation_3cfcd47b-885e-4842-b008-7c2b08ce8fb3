{"name": "@11ty/posthtml-urls", "version": "1.0.1", "description": "PostHTML plugin for transforming URLs. A fork of posthtml/posthtml-urls.", "publishConfig": {"access": "public"}, "main": "lib", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://zachleat.com/)", "contributors": ["<PERSON> <<EMAIL>> (https://svachon.com)"], "repository": {"type": "git", "url": "git://github.com/11ty/eleventy-posthtml-urls.git"}, "dependencies": {"evaluate-value": "^2.0.0", "http-equiv-refresh": "^2.0.1", "list-to-array": "^1.1.0", "parse-srcset": "^1.0.2"}, "devDependencies": {"chai": "^4.2.0", "coveralls": "^3.0.6", "html-tags": "^3.1.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "posthtml": "~0.16.6"}, "engines": {"node": ">= 6"}, "scripts": {"ci": "npm test && nyc report --reporter=text-lcov | coveralls", "posttest": "nyc report --reporter=text-summary --reporter=html", "prepublishOnly": "npm test", "test": "nyc --silent mocha test.js --bail --check-leaks"}, "files": ["lib"], "keywords": ["posthtml", "posthtml-plugin", "url"]}