{"name": "bcp-47-match", "version": "2.0.3", "description": "Match BCP 47 language tags with language ranges per RFC 4647", "license": "MIT", "keywords": ["bcp", "47", "bcp47", "bcp-47", "language", "tag", "match", "matching", "check", "rfc", "4647"], "repository": "wooorm/bcp-47-match", "bugs": "https://github.com/wooorm/bcp-47-match/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "devDependencies": {"@types/node": "^18.0.0", "c8": "^7.0.0", "chalk": "^5.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.52.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["preset-wooorm", ["gfm", {"tablePipeAlign": false}], ["lint-table-pipe-alignment", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}