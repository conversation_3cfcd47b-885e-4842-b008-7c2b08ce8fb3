{"name": "@11ty/recursive-copy", "version": "4.0.2", "description": "A fork of `recursive-copy`: Simple, flexible file copy utility", "engines": {"node": ">=18"}, "main": "index.js", "types": "index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["index.js", "index.d.ts", "lib"], "scripts": {"test": "npm run test:lint && npm run test:mocha && if-node-version '>=10' npm run test:typings", "test:lint": "if-node-version '>=4' eslint index.js test", "test:mocha": "mocha --reporter spec", "test:typings": "tsd && echo 'TypeScript definitions are valid'", "prepublishOnly": "npm run test"}, "repository": {"type": "git", "url": "https://github.com/11ty/recursive-copy.git"}, "keywords": ["copy", "recursive", "file", "directory", "folder", "symlink", "fs", "rename", "filter", "transform", "glob", "regex", "regexp"], "author": "<PERSON> <tim<PERSON><PERSON>@gmail.com>", "contributors": ["<PERSON> <<EMAIL>> (https://zachleat.com/)"], "license": "ISC", "bugs": {"url": "https://github.com/11ty/recursive-copy/issues"}, "homepage": "https://github.com/11ty/recursive-copy", "dependencies": {"errno": "^1.0.0", "junk": "^3.1.0", "maximatch": "^0.1.0", "slash": "^3.0.0"}, "devDependencies": {"@types/node": "^14.18.63", "chai": "^3.5.0", "chai-as-promised": "^5.3.0", "eslint": "^2.13.1", "if-node-version": "^1.1.1", "mocha": "^2.5.3", "read-dir-files": "^0.1.1", "rewire": "^2.5.2", "through2": "^2.0.5", "tsd": "0.31.2"}}