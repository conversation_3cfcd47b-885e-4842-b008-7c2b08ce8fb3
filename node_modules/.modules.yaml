hoistPattern:
  - '*'
hoistedDependencies:
  '@11ty/dependency-tree-esm@2.0.0':
    '@11ty/dependency-tree-esm': private
  '@11ty/dependency-tree@4.0.0':
    '@11ty/dependency-tree': private
  '@11ty/eleventy-dev-server@2.0.8':
    '@11ty/eleventy-dev-server': private
  '@11ty/eleventy-plugin-bundle@3.0.6(posthtml@0.16.6)':
    '@11ty/eleventy-plugin-bundle': private
  '@11ty/eleventy-utils@2.0.7':
    '@11ty/eleventy-utils': private
  '@11ty/lodash-custom@4.17.21':
    '@11ty/lodash-custom': private
  '@11ty/posthtml-urls@1.0.1':
    '@11ty/posthtml-urls': private
  '@11ty/recursive-copy@4.0.2':
    '@11ty/recursive-copy': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@sindresorhus/slugify@2.2.1':
    '@sindresorhus/slugify': private
  '@sindresorhus/transliterate@1.6.0':
    '@sindresorhus/transliterate': private
  a-sync-waterfall@1.0.1:
    a-sync-waterfall: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-differ@1.0.0:
    array-differ: private
  array-union@1.0.2:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  arrify@1.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  balanced-match@1.0.2:
    balanced-match: private
  bcp-47-match@2.0.3:
    bcp-47-match: private
  bcp-47-normalize@2.3.0:
    bcp-47-normalize: private
  bcp-47@2.1.0:
    bcp-47: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-from@1.1.2:
    buffer-from: private
  camel-case@4.1.2:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  clean-css@5.3.3:
    clean-css: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@10.0.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  debug@4.4.1:
    debug: private
  depd@2.0.0:
    depd: private
  dependency-graph@1.0.0:
    dependency-graph: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  entities@6.0.1:
    entities: private
  errno@1.0.0:
    errno: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@5.0.0:
    escape-string-regexp: private
  esm-import-transformer@3.0.3:
    esm-import-transformer: private
  esprima@4.0.1:
    esprima: private
  etag@1.8.1:
    etag: private
  evaluate-value@2.0.0:
    evaluate-value: private
  extend-shallow@2.0.1:
    extend-shallow: private
  fast-glob@3.3.3:
    fast-glob: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  filesize@10.1.6:
    filesize: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  foreground-child@3.3.1:
    foreground-child: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  gray-matter@4.0.3:
    gray-matter: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  htmlparser2@7.2.0:
    htmlparser2: private
  http-equiv-refresh@2.0.1:
    http-equiv-refresh: private
  http-errors@2.0.0:
    http-errors: private
  inherits@2.0.4:
    inherits: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-decimal@2.0.1:
    is-decimal: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-json@2.0.1:
    is-json: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  iso-639-1@3.1.5:
    iso-639-1: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  js-yaml@4.1.0:
    js-yaml: private
  junk@3.1.0:
    junk: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  liquidjs@10.21.1:
    liquidjs: private
  list-to-array@1.1.0:
    list-to-array: private
  lodash@4.17.21:
    lodash: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@10.4.3:
    lru-cache: private
  luxon@3.6.1:
    luxon: private
  markdown-it@14.1.0:
    markdown-it: private
  maximatch@0.1.0:
    maximatch: private
  mdurl@2.0.0:
    mdurl: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mime@3.0.0:
    mime: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  moo@0.5.2:
    moo: private
  morphdom@2.7.5:
    morphdom: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  no-case@3.0.4:
    no-case: private
  node-retrieve-globals@6.0.1:
    node-retrieve-globals: private
  normalize-path@3.0.0:
    normalize-path: private
  nunjucks@3.2.4(chokidar@3.6.0):
    nunjucks: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  on-finished@2.4.1:
    on-finished: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  param-case@3.0.4:
    param-case: private
  parse-srcset@1.0.2:
    parse-srcset: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  please-upgrade-node@3.2.0:
    please-upgrade-node: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  posthtml-match-helper@2.0.3(posthtml@0.16.6):
    posthtml-match-helper: private
  posthtml-parser@0.11.0:
    posthtml-parser: private
  posthtml-render@3.0.0:
    posthtml-render: private
  posthtml@0.16.6:
    posthtml: private
  prr@1.0.1:
    prr: private
  punycode.js@2.3.1:
    punycode.js: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  relateurl@0.2.7:
    relateurl: private
  require-directory@2.1.1:
    require-directory: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  section-matter@1.0.0:
    section-matter: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  signal-exit@4.1.0:
    signal-exit: private
  slash@3.0.0:
    slash: private
  slugify@1.6.6:
    slugify: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssri@11.0.0:
    ssri: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  terser@5.43.1:
    terser: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  uc.micro@2.1.0:
    uc.micro: private
  unpipe@1.0.0:
    unpipe: private
  urlpattern-polyfill@10.1.0:
    urlpattern-polyfill: private
  util-deprecate@1.0.2:
    util-deprecate: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  ws@8.18.2:
    ws: private
  y18n@5.0.8:
    y18n: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Wed, 25 Jun 2025 14:25:06 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@tailwindcss/oxide-android-arm64@4.1.10'
  - '@tailwindcss/oxide-darwin-x64@4.1.10'
  - '@tailwindcss/oxide-freebsd-x64@4.1.10'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.10'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.10'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.10'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.10'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.10'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.10'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
