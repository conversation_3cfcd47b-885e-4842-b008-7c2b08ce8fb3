{"name": "sanrado-com", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pnpm run build:css && eleventy --serve", "build": "pnpm run build:css:prod && eleventy", "build:css": "tailwindcss -i ./src/assets/css/style.css -o ./dist/assets/css/style.css --watch", "build:css:prod": "tailwindcss -i ./src/assets/css/style.css -o ./dist/assets/css/style.css --minify", "dev": "concurrently \"pnpm run build:css\" \"eleventy --serve\"", "test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "dependencies": {"@11ty/eleventy": "^3.1.2", "html-minifier-terser": "^7.2.0"}, "devDependencies": {"concurrently": "^9.2.0", "tailwindcss": "^3.4.17"}}